.class public interface abstract Lio/dcloud/uts/json/IJsonStringify;
.super Ljava/lang/Object;
.source "IJsonStringify.kt"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\u0008f\u0018\u00002\u00020\u0001J\n\u0010\u0002\u001a\u0004\u0018\u00010\u0001H&\u00a8\u0006\u0003"
    }
    d2 = {
        "Lio/dcloud/uts/json/IJsonStringify;",
        "",
        "toJSON",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract toJSON()Ljava/lang/Object;
.end method
