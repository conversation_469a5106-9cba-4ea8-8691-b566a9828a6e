.class public final Lio/dcloud/uts/prompt/R$style;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/prompt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "style"
.end annotation


# static fields
.field public static uni_app_uni_prompt_ActionsheetDialog:I = 0x7f0f017f

.field public static uni_app_uni_prompt_DialogAnimations_slideWindow:I = 0x7f0f0180

.field public static uni_app_uni_prompt_LoadingDialog:I = 0x7f0f0181


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
