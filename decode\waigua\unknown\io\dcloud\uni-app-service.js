!function(){"use strict";var e=Object.prototype.toString,f=Object.prototype.hasOwnProperty;function u(t){return"function"==typeof t}function n(t){return"[object Object]"===e.call(t)}var c=["invoke","success","fail","complete","returnValue"],a={},s={};function r(o,i){Object.keys(i).forEach(function(t){var e,n,r;-1!==c.indexOf(t)&&u(i[t])&&(o[t]=(e=o[t],n=i[t],(r=n?e?e.concat(n):Array.isArray(n)?n:[n]:e)?function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(r):r))})}function o(o,i){o&&i&&Object.keys(i).forEach(function(t){var e,n,r;-1!==c.indexOf(t)&&u(i[t])&&(e=o[t],n=i[t],-1!==(r=e.indexOf(n))&&e.splice(r,1))})}function p(e){return function(t){return e(t)||t}}function l(t){return!!t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof t.then}function h(t,e){for(var n=!1,r=0;r<t.length;r++){var o=t[r];if(n)n=Promise.then(p(o));else{var i=o(e);if(l(i)&&(n=Promise.resolve(i)),!1===i)return{then:function(){}}}}return n||{then:function(t){return t(e)}}}function v(r,t){return void 0===t&&(t={}),["success","fail","complete"].forEach(function(e){if(Array.isArray(r[e])){var n=t[e];t[e]=function(t){h(r[e],t).then(function(t){return u(n)&&n(t)||t})}}}),t}function y(t,e){var n=[];Array.isArray(a.returnValue)&&n.push.apply(n,a.returnValue);var r=s[t];return r&&Array.isArray(r.returnValue)&&n.push.apply(n,r.returnValue),n.forEach(function(t){e=t(e)||e}),e}function d(t,e,n){for(var r=[],o=arguments.length-3;0<o--;)r[o]=arguments[o+3];var i=function(t){var e=Object.create(null);Object.keys(a).forEach(function(t){"returnValue"!==t&&(e[t]=a[t].slice())});var n=s[t];return n&&Object.keys(n).forEach(function(t){"returnValue"!==t&&(e[t]=(e[t]||[]).concat(n[t]))}),e}(t);return i&&Object.keys(i).length?Array.isArray(i.invoke)?h(i.invoke,n).then(function(t){return e.apply(void 0,[v(i,t)].concat(r))}):e.apply(void 0,[v(i,n)].concat(r)):e.apply(void 0,[n].concat(r))}var t={returnValue:function(t){return l(t)?t.then(function(t){return t[1]}).catch(function(t){return t[0]}):t}},i=/^\$|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64/,g=/^create|Manager$/,_=/^on/;function m(t){return r=t,!(g.test(r)||(n=t,i.test(n))||(e=t,_.test(e)));var e,n,r}function b(o,i){return m(o)?function(n){void 0===n&&(n={});for(var r=[],t=arguments.length-1;0<t--;)r[t]=arguments[t+1];return u(n.success)||u(n.fail)||u(n.complete)?y(o,d.apply(void 0,[o,i,n].concat(r))):y(o,new Promise(function(t,e){d.apply(void 0,[o,i,Object.assign({},n,{success:t,fail:e})].concat(r)),Promise.prototype.finally||(Promise.prototype.finally=function(e){var n=this.constructor;return this.then(function(t){return n.resolve(e()).then(function(){return t})},function(t){return n.resolve(e()).then(function(){throw t})})})}).then(function(t){return[null,t]}).catch(function(t){return[t]}))}:i}var x="__uniapp__service";function w(p,l,t){var h,v=[],y=p.requireModule("plus").postMessage,e=function(e){v.forEach(function(t){return t({origin:h,data:e})})};p.requireModule("globalEvent").addEventListener("plusMessage",function(t){"UniAppSubNVue"===t.data.type&&e(t.data.data,t.data.options)});var n=l.webview.currentWebview().id,d=new t("UNI-APP-SUBNVUE");d.onmessage=function(t){t.data.to===n&&e(t.data.data)};var r=function(n){n.$processed=!0;var e=l.webview.currentWebview().id===n.id,r="uniNView"===n.__uniapp_origin_type&&n.__uniapp_origin_id,o=n.id;if(n.postMessage=function(t){r?d.postMessage({data:t,to:e?r:o}):y({type:"UniAppSubNVue",data:t},x)},n.onMessage=function(t){v.push(t)},n.__uniapp_mask_id){h=n.__uniapp_host;var i=n.__uniapp_mask,u="0"===n.__uniapp_mask_id?{setStyle:function(t){var e=t.mask;p.requireModule("uni-tabview").setMask({color:e})}}:l.webview.getWebviewById(n.__uniapp_mask_id),c=n.show,a=n.hide,s=n.close,f=function(){u.setStyle({mask:"none"})};n.show=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return u.setStyle({mask:i}),c.apply(n,t)},n.hide=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return f(),a.apply(n,t)},n.close=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return f(),s.apply(n,t)}}},o=function(t){var e=l.webview.getWebviewById(t);return e&&!e.$processed&&r(e),e};return{getSubNVueById:o,getCurrentSubNVue:function(){return o(l.webview.currentWebview().id)}}}function M(){}var k,O=!1,B=0,S=0;function C(t,e,n){return t[e].apply(t,n)}var A="success",V="fail",j=[A,V,"complete"];function E(t,e,n,r){if(t){if(void 0===n)return t[e]();var o=q(n,r)[1];return Object.keys(o).length?t[e]($(e,o)):t[e]()}}function P(t,e,n,r){if(t){var o=q(n,r),i=o[0],u=o[1];return Object.keys(u).length?t[e](i,$(e,u)):t[e](i)}}function N(t,e){return function t(e,n){if(!e||!n)return;if(n.attr.id===e)return n;var r=n.children;if(!r)return;for(var o=0,i=r.length;o<i;o++){var u=t(e,r[o]);if(u)return u}}(t,e.$el)}function q(n,t){void 0===n&&(n={});var r=Object.create(null),e=function(t){var e=n[t];u(e)&&(r[t]=e,delete n[t])};return j.forEach(e),t&&t.forEach(e),[n,r]}function $(o,i){return function(t){var e=t.type;delete t.type;var n=i[e];if(e===A?t.errMsg=o+":ok":e===V&&(t.errMsg=o+":fail"+(t.msg?" "+t.msg:"")),delete t.code,delete t.msg,u(n)&&n(t),e===A||e===V){var r=i.complete;u(r)&&r(t)}}}var I=function(t,e){this.id=t,this.ctx=e};I.prototype.getCenterLocation=function(t){return E(this.ctx,"getCenterLocation",t)},I.prototype.moveToLocation=function(){return E(this.ctx,"moveToLocation")},I.prototype.translateMarker=function(t){return P(this.ctx,"translateMarker",t,["animationEnd"])},I.prototype.includePoints=function(t){return P(this.ctx,"includePoints",t)},I.prototype.getRegion=function(t){return E(this.ctx,"getRegion",t)},I.prototype.getScale=function(t){return E(this.ctx,"getScale",t)};var U=function(t,e){this.id=t,this.ctx=e};U.prototype.play=function(){return E(this.ctx,"play")},U.prototype.pause=function(){return E(this.ctx,"pause")},U.prototype.seek=function(t){return P(this.ctx,"seek",t)},U.prototype.stop=function(){return E(this.ctx,"stop")},U.prototype.sendDanmu=function(t){return P(this.ctx,"sendDanmu",t)},U.prototype.playbackRate=function(t){return P(this.ctx,"playbackRate",t)},U.prototype.requestFullScreen=function(t){return P(this.ctx,"requestFullScreen",t)},U.prototype.exitFullScreen=function(){return E(this.ctx,"exitFullScreen")},U.prototype.showStatusBar=function(){return E(this.ctx,"showStatusBar")},U.prototype.hideStatusBar=function(){return E(this.ctx,"hideStatusBar")};var G=function(t,e){this.id=t,this.ctx=e};G.prototype.start=function(t){return E(this.ctx,"start",t)},G.prototype.stop=function(t){return E(this.ctx,"stop",t)},G.prototype.pause=function(t){return E(this.ctx,"pause",t)},G.prototype.resume=function(t){return E(this.ctx,"resume",t)},G.prototype.switchCamera=function(t){return E(this.ctx,"switchCamera",t)},G.prototype.snapshot=function(t){return E(this.ctx,"snapshot",t)},G.prototype.toggleTorch=function(t){return E(this.ctx,"toggleTorch",t)},G.prototype.playBGM=function(t){return P(this.ctx,"playBGM",t)},G.prototype.stopBGM=function(t){return E(this.ctx,"stopBGM",t)},G.prototype.pauseBGM=function(t){return E(this.ctx,"pauseBGM",t)},G.prototype.resumeBGM=function(t){return E(this.ctx,"resumeBGM",t)},G.prototype.setBGMVolume=function(t){return P(this.ctx,"setBGMVolume",t)},G.prototype.startPreview=function(t){return E(this.ctx,"startPreview",t)},G.prototype.stopPreview=function(t){return E(this.ctx,"stopPreview",t)};var L=function(t,e,n,r){this._selectorQuery=t,this._component=e,this._selector=n,this._single=r};function Q(n){var r={};return Object.keys(n||{}).forEach(function(t){if(0===t.indexOf("data")){var e=t.replace("data","");e=e.charAt(0).toLowerCase()+e.slice(1),r[e]=n[t]}}),r}function R(t,e,n,r){var o=function(t){for(var e=[],n=0;n<t.length;n++){var r=t[n].selector;0===r.indexOf("#")&&e.push(r.substring(1))}return e}(n),i=new Array(o.length);!function t(e,n,r){var o=n.children;if(!Array.isArray(o))return!1;for(var i=0;i<o.length;i++){var u=o[i];if(u.attr){var c=e.indexOf(u.attr.id);if(0<=c&&(r[c]={id:e[c],ref:u.ref,dataset:Q(u.attr)},1===e.length))break}u.children&&t(e,u,r)}}(o,e.$el,i),function e(n,r,o,i,u){var c=r[o];n.getComponentRect(c.ref,function(t){t.size.id=c.id,t.size.dataset=c.dataset,i.push(t.size),(o+=1)<r.length?e(n,r,o,i,u):u(i)})}(t,i,0,[],function(t){r(t)})}L.prototype.boundingClientRect=function(t){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},t),this._selectorQuery},L.prototype.fields=function(t,e){return this._selectorQuery._push(this._selector,this._component,this._single,t,e),this._selectorQuery},L.prototype.scrollOffset=function(t){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},t),this._selectorQuery};var T=function(t){this.pageId=t,this._queue=[],this._queueCb=[]};T.prototype.exec=function(e){var o=this;this._component&&(this._dom=this._component._$weex.requireModule("dom"),R(this._dom,this._component,this._queue,function(t){var r=o._queueCb;t.forEach(function(t,e){var n=r[e];u(n)&&n.call(o,t)}),u(e)&&e.call(o,t)}))},T.prototype.in=function(t){return t?(this._component=t,this):console.warn("uni.createSelectorQuery 必须传入当前 vm 对象(this)")},T.prototype.select=function(t){return new L(this,this._component,t,!0)},T.prototype.selectAll=function(t){return new L(this,this._component,t,!1)},T.prototype.selectViewport=function(){return new L(this,0,"",!0)},T.prototype._push=function(t,e,n,r,o){this._queue.push({component:e,selector:t,single:n,fields:r}),this._queueCb.push(o)};var W,z,F,D,K={promiseInterceptor:t},H=Object.freeze({upx2px:function(t,e){if(0===(t=Number(t)))return 0;var n=t/750*(e||B);return n<0&&(n=-n),0===(n=Math.floor(n+1e-4))?1!==S&&O?.5:1:t<0?-n:n},$on:function(){for(var t=arguments.length,e=Array(t);t--;)e[t]=arguments[t];return C(k(),"$on",[].concat(e))},$once:function(){for(var t=arguments.length,e=Array(t);t--;)e[t]=arguments[t];return C(k(),"$once",[].concat(e))},$off:function(){for(var t=arguments.length,e=Array(t);t--;)e[t]=arguments[t];return C(k(),"$off",[].concat(e))},$emit:function(){for(var t=arguments.length,e=Array(t);t--;)e[t]=arguments[t];return C(k(),"$emit",[].concat(e))},createMapContext:function(t,e){if(!e)return console.warn("uni.createMapContext 必须传入第二个参数，即当前 vm 对象(this)");var n=N(t,e);return n?new I(t,n):console.warn("Can not find `"+t+"`")},createVideoContext:function(t,e){if(!e)return console.warn("uni.createVideoContext 必须传入第二个参数，即当前 vm 对象(this)");var n=N(t,e);return n?new U(t,n):console.warn("Can not find `"+t+"`")},createLivePusherContext:function(t,e){if(!e)return console.warn("uni.createLivePusherContext 必须传入第二个参数，即当前 vm 对象(this)");var n=N(t,e);return n?new G(t,n):console.warn("Can not find `"+t+"`")},createSelectorQuery:function(){return new T},interceptors:K,addInterceptor:function(t,e){"string"==typeof t&&n(e)?r(s[t]||(s[t]={}),e):n(t)&&r(a,t)},removeInterceptor:function(t,e){"string"==typeof t?n(e)?o(s[t],e):delete s[t]:n(t)&&o(a,t)}});function J(o,t,e,n){var r,i=w(t,e,n),u=i.getSubNVueById,c=i.getCurrentSubNVue,a=Object.assign({getSubNVueById:u,getCurrentSubNVue:c,requireNativePlugin:t.requireModule},function(t){var n={onNavigationBarButtonTap:M,onNavigationBarSearchInputChanged:M,onNavigationBarSearchInputConfirmed:M,onNavigationBarSearchInputClicked:M};t.requireModule("globalEvent").addEventListener("plusMessage",function(t){n[t.data.type]&&n[t.data.type](t.data.data)});var r=Object.create(null);return Object.keys(n).forEach(function(e){r[e]=function(t){n[e]=t}}),r}(t),(r=t.requireModule("plus"),{postMessage:function(t){r.postMessage(t,x)}}));if("undefined"!=typeof Proxy)return new Proxy({},{get:function(t,e){return t[e]?t[e]:H[e]?H[e]:a[e]?a[e]:(n=o,r=e,f.call(n,r)?b(e,o[e]):void 0);var n,r},set:function(t,e,n){return t[e]=n,!0}});var s={requireNativePlugin:t.requireModule};return Object.keys(H).forEach(function(t){s[t]=H[t]}),Object.keys(a).forEach(function(t){s[t]=a[t]}),Object.keys(o).forEach(function(t){s[t]=b(t,o[t])}),s}var X={create:function(t,e,n){return{initUniApp:function(t){var e,n=t.nvue,r=t.getUni,o=t.getApp,i=t.getUniEmitter,u=t.getCurrentPages;W=r,z=o,F=i,D=u,e=n.config.env,S=e.scale,B=Math.ceil(e.deviceWidth/S),O="iOS"===e.platform,k=i},instance:{getUni:function(t,e,n){return J(W(),t,e,n)},getApp:function(){return z()},getUniEmitter:function(){return F()},getCurrentPages:function(t){return D(t)}}}},refresh:function(t,e,n){},destroy:function(t,e){}};service.register("UniApp",X)}();
