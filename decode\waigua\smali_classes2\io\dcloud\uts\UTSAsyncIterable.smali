.class public interface abstract Lio/dcloud/uts/UTSAsyncIterable;
.super Ljava/lang/Object;
.source "UTSIterator.kt"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008f\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u0010\u0012\u000c\u0012\n\u0012\u0006\u0012\u0004\u0018\u00010\u00010\u00040\u0003H&\u00a8\u0006\u0005"
    }
    d2 = {
        "Lio/dcloud/uts/UTSAsyncIterable;",
        "",
        "asyncIterator",
        "Lio/dcloud/uts/UTSIterator;",
        "Lio/dcloud/uts/UTSPromise;",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract asyncIterator()Lio/dcloud/uts/UTSIterator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lio/dcloud/uts/UTSIterator<",
            "Lio/dcloud/uts/UTSPromise<",
            "Ljava/lang/Object;",
            ">;>;"
        }
    .end annotation
.end method
