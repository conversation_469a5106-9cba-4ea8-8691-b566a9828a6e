.class final Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;
.super Ljava/util/AbstractSet;
.source "LinkedHashTreeMap.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x10
    name = "KeySet"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/AbstractSet<",
        "TK;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;


# direct methods
.method constructor <init>(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;)V
    .locals 0

    .line 832
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    invoke-direct {p0}, Ljava/util/AbstractSet;-><init>()V

    return-void
.end method


# virtual methods
.method public clear()V
    .locals 1

    .line 854
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->clear()V

    return-void
.end method

.method public contains(Ljava/lang/Object;)Z
    .locals 1

    .line 846
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "TK;>;"
        }
    .end annotation

    .line 838
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet$1;

    invoke-direct {v0, p0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet$1;-><init>(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;)V

    return-object v0
.end method

.method public remove(Ljava/lang/Object;)Z
    .locals 1

    .line 850
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->removeInternalByKey(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public size()I
    .locals 1

    .line 834
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    iget v0, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    return v0
.end method
