.class public final Lio/dcloud/uts/prompt/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lio/dcloud/uts/prompt/R$anim;,
        Lio/dcloud/uts/prompt/R$color;,
        Lio/dcloud/uts/prompt/R$drawable;,
        Lio/dcloud/uts/prompt/R$id;,
        Lio/dcloud/uts/prompt/R$layout;,
        Lio/dcloud/uts/prompt/R$string;,
        Lio/dcloud/uts/prompt/R$style;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
