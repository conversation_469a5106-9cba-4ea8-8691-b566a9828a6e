.class public final Lio/dcloud/uts/nativeregex/RegexpBridge;
.super Ljava/lang/Object;
.source "RegexpBridge.kt"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u000b\n\u0000\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002J\u0019\u0010\u000c\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0004H\u0086 J\u0011\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u000fH\u0086 J#\u0010\u0013\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u0004H\u0086 J\u0011\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u0015\u001a\u00020\rH\u0086 J\u0013\u0010\u0019\u001a\u00020\u000f2\u0008\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0086 J+\u0010\u001a\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\u000f2\u0006\u0010\u001c\u001a\u00020\u001dH\u0086 R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0008\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"
    }
    d2 = {
        "Lio/dcloud/uts/nativeregex/RegexpBridge;",
        "",
        "()V",
        "FLAG_DOTALL",
        "",
        "FLAG_GLOBAL",
        "FLAG_IGNORECASE",
        "FLAG_INDICES",
        "FLAG_MULTILINE",
        "FLAG_STICKY",
        "FLAG_UNICODE",
        "FLAG_UNICODE_SETS",
        "compileRegexp",
        "",
        "pattern",
        "",
        "flags",
        "escape",
        "str",
        "execRegexp",
        "Lio/dcloud/uts/RegExpExecArray;",
        "bytecode",
        "input",
        "startIndex",
        "getFlags",
        "getSource",
        "replace",
        "replacement",
        "global",
        "",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final FLAG_DOTALL:I = 0x8

.field public static final FLAG_GLOBAL:I = 0x1

.field public static final FLAG_IGNORECASE:I = 0x2

.field public static final FLAG_INDICES:I = 0x40

.field public static final FLAG_MULTILINE:I = 0x4

.field public static final FLAG_STICKY:I = 0x20

.field public static final FLAG_UNICODE:I = 0x10

.field public static final FLAG_UNICODE_SETS:I = 0x80

.field public static final INSTANCE:Lio/dcloud/uts/nativeregex/RegexpBridge;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lio/dcloud/uts/nativeregex/RegexpBridge;

    invoke-direct {v0}, Lio/dcloud/uts/nativeregex/RegexpBridge;-><init>()V

    sput-object v0, Lio/dcloud/uts/nativeregex/RegexpBridge;->INSTANCE:Lio/dcloud/uts/nativeregex/RegexpBridge;

    .line 8
    const-string v0, "uts-runtime"

    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 6
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final native compileRegexp(Ljava/lang/String;I)[B
.end method

.method public final native escape(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public final native execRegexp([BLjava/lang/String;I)Lio/dcloud/uts/RegExpExecArray;
.end method

.method public final native getFlags([B)I
.end method

.method public final native getSource(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public final native replace([BLjava/lang/String;Ljava/lang/String;Z)Ljava/lang/String;
.end method
