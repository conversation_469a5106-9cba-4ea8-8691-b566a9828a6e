.class abstract Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;
.super Ljava/lang/Object;
.source "LinkedHashTreeMap.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x402
    name = "LinkedTreeMapIterator"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "TT;>;"
    }
.end annotation


# instance fields
.field expectedModCount:I

.field lastReturned:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field final synthetic this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;


# direct methods
.method constructor <init>(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;)V
    .locals 1

    .line 768
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 764
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iget-object v0, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v0, 0x0

    .line 765
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 766
    iget p1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->expectedModCount:I

    return-void
.end method


# virtual methods
.method public final hasNext()Z
    .locals 2

    .line 772
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    iget-object v1, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method final nextNode()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 776
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 777
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    iget-object v1, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eq v0, v1, :cond_1

    .line 780
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    iget v1, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    iget v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->expectedModCount:I

    if-ne v1, v2, :cond_0

    .line 783
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 784
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    return-object v0

    .line 781
    :cond_0
    new-instance v0, Ljava/util/ConcurrentModificationException;

    invoke-direct {v0}, Ljava/util/ConcurrentModificationException;-><init>()V

    throw v0

    .line 778
    :cond_1
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final remove()V
    .locals 3

    .line 788
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz v0, :cond_0

    .line 791
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    const/4 v2, 0x1

    invoke-virtual {v1, v0, v2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->removeInternal(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Z)V

    const/4 v0, 0x0

    .line 792
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 793
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;

    iget v0, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;->expectedModCount:I

    return-void

    .line 789
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method
