.class public final Lio/dcloud/uts/Uint16Array;
.super Lio/dcloud/uts/TypedArray;
.source "Uint16Array.kt"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lio/dcloud/uts/Uint16Array$Companion;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0004\n\u0002\u0008\u0002\n\u0002\u0010\u001e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010(\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0008\u0018\u0000 +2\u00020\u0001:\u0001+B\u000f\u0008\u0016\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004B\u0015\u0008\u0016\u0012\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\u0002\u0010\u0007B\u000f\u0008\u0016\u0012\u0006\u0010\u0008\u001a\u00020\t\u00a2\u0006\u0002\u0010\nB\'\u0008\u0016\u0012\u0006\u0010\u0008\u001a\u00020\t\u0012\n\u0008\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\u0008\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u000cJ\u0010\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0003H\u0016J_\u0010\u0014\u001a\u00020\u0000\"\u0008\u0008\u0000\u0010\u0015*\u00020\u00012K\u0010\u0016\u001aG\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0018\u0012\u0008\u0008\u0019\u0012\u0004\u0008\u0008(\u0013\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0018\u0012\u0008\u0008\u0019\u0012\u0004\u0008\u0008(\u001a\u0012\u0013\u0012\u0011H\u0015\u00a2\u0006\u000c\u0008\u0018\u0012\u0008\u0008\u0019\u0012\u0004\u0008\u0008(\u0005\u0012\u0004\u0012\u00020\u001b0\u0017H\u0016J\u0010\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u001a\u001a\u00020\u001dH\u0016J\u0008\u0010\u001e\u001a\u00020\u0003H\u0016J\u000f\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u00030 H\u0096\u0002J_\u0010!\u001a\u00020\u0000\"\u0008\u0008\u0000\u0010\u0015*\u00020\u00012K\u0010\"\u001aG\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0018\u0012\u0008\u0008\u0019\u0012\u0004\u0008\u0008(\u0013\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0018\u0012\u0008\u0008\u0019\u0012\u0004\u0008\u0008(\u001a\u0012\u0013\u0012\u0011H\u0015\u00a2\u0006\u000c\u0008\u0018\u0012\u0008\u0008\u0019\u0012\u0004\u0008\u0008(\u0005\u0012\u0004\u0012\u00020\u00030\u0017H\u0016J\u001f\u0010#\u001a\u00020$2\u0008\u0010\u001a\u001a\u0004\u0018\u00010\u001d2\u0006\u0010\u0013\u001a\u00020\u0003H\u0016\u00a2\u0006\u0002\u0010%J\u001c\u0010&\u001a\u00020\u00002\u0008\u0010\'\u001a\u0004\u0018\u00010\u00032\u0008\u0010(\u001a\u0004\u0018\u00010\u0003H\u0016J\u001c\u0010)\u001a\u00020\u00002\u0008\u0010*\u001a\u0004\u0018\u00010\u00032\u0008\u0010(\u001a\u0004\u0018\u00010\u0003H\u0016R\u001c\u0010\r\u001a\u00020\u0003X\u0096\u000e\u00a2\u0006\u0010\n\u0002\u0008\u0011\u001a\u0004\u0008\u000e\u0010\u000f\"\u0004\u0008\u0010\u0010\u0004\u00a8\u0006,"
    }
    d2 = {
        "Lio/dcloud/uts/Uint16Array;",
        "Lio/dcloud/uts/TypedArray;",
        "length",
        "",
        "(Ljava/lang/Number;)V",
        "array",
        "",
        "(Ljava/util/Collection;)V",
        "buffer",
        "Lio/dcloud/uts/ArrayBuffer;",
        "(Lio/dcloud/uts/ArrayBuffer;)V",
        "byteOffset",
        "(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V",
        "BYTES_PER_ELEMENT",
        "getBYTES_PER_ELEMENT",
        "()Ljava/lang/Number;",
        "setBYTES_PER_ELEMENT",
        "BYTES_PER_ELEMENT$1",
        "convertValue",
        "value",
        "filter",
        "T",
        "predicate",
        "Lkotlin/Function3;",
        "Lkotlin/ParameterName;",
        "name",
        "index",
        "",
        "getAuto",
        "",
        "getBytesPerElement",
        "iterator",
        "",
        "map",
        "callbackfn",
        "putAuto",
        "",
        "(Ljava/lang/Integer;Ljava/lang/Number;)V",
        "slice",
        "start",
        "end",
        "subarray",
        "begin",
        "Companion",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final BYTES_PER_ELEMENT:I = 0x2

.field public static final Companion:Lio/dcloud/uts/Uint16Array$Companion;


# instance fields
.field private BYTES_PER_ELEMENT$1:Ljava/lang/Number;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lio/dcloud/uts/Uint16Array$Companion;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lio/dcloud/uts/Uint16Array$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lio/dcloud/uts/Uint16Array;->Companion:Lio/dcloud/uts/Uint16Array$Companion;

    return-void
.end method

.method public constructor <init>(Lio/dcloud/uts/ArrayBuffer;)V
    .locals 1

    const-string v0, "buffer"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 56
    invoke-direct {p0, p1}, Lio/dcloud/uts/TypedArray;-><init>(Lio/dcloud/uts/ArrayBuffer;)V

    const/4 p1, 0x2

    .line 6
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    iput-object p1, p0, Lio/dcloud/uts/Uint16Array;->BYTES_PER_ELEMENT$1:Ljava/lang/Number;

    return-void
.end method

.method public constructor <init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V
    .locals 1

    const-string v0, "buffer"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 62
    invoke-direct {p0, p1, p2, p3}, Lio/dcloud/uts/TypedArray;-><init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V

    const/4 p1, 0x2

    .line 6
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    iput-object p1, p0, Lio/dcloud/uts/Uint16Array;->BYTES_PER_ELEMENT$1:Ljava/lang/Number;

    return-void
.end method

.method public synthetic constructor <init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p5, p4, 0x2

    const/4 v0, 0x0

    if-eqz p5, :cond_0

    move-object p2, v0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    move-object p3, v0

    .line 58
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lio/dcloud/uts/Uint16Array;-><init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Number;)V
    .locals 1

    const-string v0, "length"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 48
    invoke-direct {p0, p1}, Lio/dcloud/uts/TypedArray;-><init>(Ljava/lang/Number;)V

    const/4 p1, 0x2

    .line 6
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    iput-object p1, p0, Lio/dcloud/uts/Uint16Array;->BYTES_PER_ELEMENT$1:Ljava/lang/Number;

    return-void
.end method

.method public constructor <init>(Ljava/util/Collection;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+",
            "Ljava/lang/Number;",
            ">;)V"
        }
    .end annotation

    const-string v0, "array"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 52
    invoke-direct {p0, p1}, Lio/dcloud/uts/TypedArray;-><init>(Ljava/util/Collection;)V

    const/4 p1, 0x2

    .line 6
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    iput-object p1, p0, Lio/dcloud/uts/Uint16Array;->BYTES_PER_ELEMENT$1:Ljava/lang/Number;

    return-void
.end method


# virtual methods
.method public convertValue(Ljava/lang/Number;)Ljava/lang/Number;
    .locals 1

    const-string v0, "value"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    return-object p1
.end method

.method public bridge synthetic filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint16Array;->filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint16Array;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint16Array;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lio/dcloud/uts/TypedArray;",
            ">(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Number;",
            "-",
            "Ljava/lang/Number;",
            "-TT;",
            "Ljava/lang/Boolean;",
            ">;)",
            "Lio/dcloud/uts/Uint16Array;"
        }
    .end annotation

    const-string v0, "predicate"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 66
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 68
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->size()I

    move-result v1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    .line 70
    invoke-virtual {p0, v2}, Lio/dcloud/uts/Uint16Array;->getAuto(I)Ljava/lang/Number;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Number;->shortValue()S

    move-result v3

    .line 71
    invoke-static {v3}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v4

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const-string v6, "null cannot be cast to non-null type T of io.dcloud.uts.Uint16Array.filter"

    invoke-static {p0, v6}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v6, p0

    check-cast v6, Lio/dcloud/uts/TypedArray;

    invoke-interface {p1, v4, v5, v6}, Lkotlin/jvm/functions/Function3;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Boolean;

    invoke-virtual {v4}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v4

    if-eqz v4, :cond_0

    .line 72
    invoke-static {v3}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 76
    :cond_1
    new-instance p1, Lio/dcloud/uts/Uint16Array;

    check-cast v0, Ljava/util/Collection;

    invoke-direct {p1, v0}, Lio/dcloud/uts/Uint16Array;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method

.method public getAuto(I)Ljava/lang/Number;
    .locals 2

    .line 91
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v0

    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getBYTES_PER_ELEMENT()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    mul-int p1, p1, v1

    invoke-virtual {v0, p1}, Ljava/nio/ByteBuffer;->getShort(I)S

    move-result p1

    int-to-short p1, p1

    .line 92
    invoke-static {p1}, Lkotlin/UShort;->constructor-impl(S)S

    move-result p1

    const v0, 0xffff

    and-int/2addr p1, v0

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    return-object p1
.end method

.method public getBYTES_PER_ELEMENT()Ljava/lang/Number;
    .locals 1

    .line 6
    iget-object v0, p0, Lio/dcloud/uts/Uint16Array;->BYTES_PER_ELEMENT$1:Ljava/lang/Number;

    return-object v0
.end method

.method public getBytesPerElement()Ljava/lang/Number;
    .locals 1

    const/4 v0, 0x2

    .line 9
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    check-cast v0, Ljava/lang/Number;

    return-object v0
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation

    .line 151
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->rewind()Ljava/nio/Buffer;

    .line 153
    new-instance v0, Lio/dcloud/uts/Uint16Array$iterator$1;

    invoke-direct {v0, p0}, Lio/dcloud/uts/Uint16Array$iterator$1;-><init>(Lio/dcloud/uts/Uint16Array;)V

    check-cast v0, Ljava/util/Iterator;

    return-object v0
.end method

.method public bridge synthetic map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint16Array;->map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint16Array;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint16Array;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lio/dcloud/uts/TypedArray;",
            ">(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Number;",
            "-",
            "Ljava/lang/Number;",
            "-TT;+",
            "Ljava/lang/Number;",
            ">;)",
            "Lio/dcloud/uts/Uint16Array;"
        }
    .end annotation

    const-string v0, "callbackfn"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 102
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->size()I

    move-result v0

    .line 103
    new-instance v1, Lio/dcloud/uts/UTSArray;

    invoke-direct {v1}, Lio/dcloud/uts/UTSArray;-><init>()V

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 105
    invoke-virtual {p0, v2}, Lio/dcloud/uts/Uint16Array;->getAuto(I)Ljava/lang/Number;

    move-result-object v3

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v5, "null cannot be cast to non-null type T of io.dcloud.uts.Uint16Array.map"

    invoke-static {p0, v5}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v5, p0

    check-cast v5, Lio/dcloud/uts/TypedArray;

    invoke-interface {p1, v3, v4, v5}, Lkotlin/jvm/functions/Function3;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Number;

    invoke-virtual {v3}, Ljava/lang/Number;->shortValue()S

    move-result v3

    invoke-static {v3}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v3

    invoke-virtual {v1, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 109
    :cond_0
    new-instance p1, Lio/dcloud/uts/Uint16Array;

    check-cast v1, Ljava/util/Collection;

    invoke-direct {p1, v1}, Lio/dcloud/uts/Uint16Array;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method

.method public putAuto(Ljava/lang/Integer;Ljava/lang/Number;)V
    .locals 2

    const-string v0, "value"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    if-eqz p1, :cond_0

    .line 81
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v0

    .line 82
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getBYTES_PER_ELEMENT()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    mul-int p1, p1, v1

    .line 83
    invoke-virtual {p2}, Ljava/lang/Number;->shortValue()S

    move-result p2

    .line 81
    invoke-virtual {v0, p1, p2}, Ljava/nio/ByteBuffer;->putShort(IS)Ljava/nio/ByteBuffer;

    goto :goto_0

    .line 86
    :cond_0
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object p1

    invoke-virtual {p2}, Ljava/lang/Number;->shortValue()S

    move-result p2

    invoke-virtual {p1, p2}, Ljava/nio/ByteBuffer;->putShort(S)Ljava/nio/ByteBuffer;

    :goto_0
    return-void
.end method

.method public setBYTES_PER_ELEMENT(Ljava/lang/Number;)V
    .locals 1

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 6
    iput-object p1, p0, Lio/dcloud/uts/Uint16Array;->BYTES_PER_ELEMENT$1:Ljava/lang/Number;

    return-void
.end method

.method public bridge synthetic slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/Uint16Array;->slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint16Array;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint16Array;
    .locals 2

    if-nez p1, :cond_0

    const/4 p1, 0x0

    .line 113
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    :cond_0
    if-nez p2, :cond_1

    .line 114
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object p2

    .line 115
    :cond_1
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 116
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    invoke-static {p1, v1}, Lio/dcloud/uts/utils/IndexKt;->toSliceIndex(Ljava/lang/Number;I)I

    move-result p1

    .line 118
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    .line 116
    invoke-static {p2, v1}, Lio/dcloud/uts/utils/IndexKt;->toSliceIndex(Ljava/lang/Number;I)I

    move-result p2

    :goto_0
    if-ge p1, p2, :cond_2

    .line 120
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint16Array;->getAuto(I)Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->shortValue()S

    move-result v1

    invoke-static {v1}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v1

    invoke-virtual {v0, v1}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 122
    :cond_2
    new-instance p1, Lio/dcloud/uts/Uint16Array;

    check-cast v0, Ljava/util/Collection;

    invoke-direct {p1, v0}, Lio/dcloud/uts/Uint16Array;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method

.method public bridge synthetic subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/Uint16Array;->subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint16Array;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint16Array;
    .locals 4

    const/4 v0, 0x0

    .line 130
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    if-eqz p1, :cond_1

    .line 129
    move-object v2, v1

    check-cast v2, Ljava/lang/Number;

    invoke-static {p1, v2}, Lio/dcloud/uts/NumberKt;->compareTo(Ljava/lang/Number;Ljava/lang/Number;)I

    move-result v2

    if-gez v2, :cond_0

    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object v2

    invoke-static {v2, p1}, Lio/dcloud/uts/NumberKt;->plus(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    :cond_0
    if-nez p1, :cond_2

    .line 130
    :cond_1
    move-object p1, v1

    check-cast p1, Ljava/lang/Number;

    :cond_2
    if-eqz p2, :cond_4

    .line 134
    check-cast v1, Ljava/lang/Number;

    invoke-static {p2, v1}, Lio/dcloud/uts/NumberKt;->compareTo(Ljava/lang/Number;Ljava/lang/Number;)I

    move-result v1

    if-gez v1, :cond_3

    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-static {v1, p2}, Lio/dcloud/uts/NumberKt;->plus(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p2

    :cond_3
    if-nez p2, :cond_5

    .line 135
    :cond_4
    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object p2

    .line 138
    :cond_5
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    invoke-static {p1, v0, v1}, Lkotlin/ranges/RangesKt;->coerceIn(III)I

    move-result p1

    .line 140
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    invoke-virtual {p0}, Lio/dcloud/uts/Uint16Array;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    invoke-static {p2, p1, v1}, Lkotlin/ranges/RangesKt;->coerceIn(III)I

    move-result p2

    .line 141
    new-instance v1, Lio/dcloud/uts/UTSArray;

    invoke-direct {v1}, Lio/dcloud/uts/UTSArray;-><init>()V

    :goto_0
    if-ge p1, p2, :cond_6

    .line 143
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint16Array;->getAuto(I)Ljava/lang/Number;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Number;->shortValue()S

    move-result v2

    invoke-static {v2}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v2

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Short;

    aput-object v2, v3, v0

    invoke-virtual {v1, v3}, Lio/dcloud/uts/UTSArray;->push([Ljava/lang/Object;)I

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 145
    :cond_6
    new-instance p1, Lio/dcloud/uts/Uint16Array;

    check-cast v1, Ljava/util/Collection;

    invoke-direct {p1, v1}, Lio/dcloud/uts/Uint16Array;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method
