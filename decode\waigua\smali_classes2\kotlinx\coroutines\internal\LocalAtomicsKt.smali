.class public final Lkotlinx/coroutines/internal/LocalAtomicsKt;
.super Ljava/lang/Object;
.source "LocalAtomics.kt"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000*\u000c\u0008\u0000\u0010\u0000\"\u00020\u00012\u00020\u0001\u00a8\u0006\u0002"
    }
    d2 = {
        "LocalAtomicInt",
        "Ljava/util/concurrent/atomic/AtomicInteger;",
        "kotlinx-coroutines-core"
    }
    k = 0x2
    mv = {
        0x1,
        0x6,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic LocalAtomicInt$annotations()V
    .locals 0

    return-void
.end method
