.class public final Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;
.super Ljava/util/AbstractMap;
.source "LinkedHashTreeMap.java"

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;,
        Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$EntrySet;,
        Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;,
        Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlIterator;,
        Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;,
        Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$LinkedTreeMapIterator;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "V:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/util/AbstractMap<",
        "TK;TV;>;",
        "Ljava/io/Serializable;"
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z

.field private static final NATURAL_ORDER:Ljava/util/Comparator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Comparator<",
            "Ljava/lang/Comparable;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field comparator:Ljava/util/Comparator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Comparator<",
            "-TK;>;"
        }
    .end annotation
.end field

.field private entrySet:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$EntrySet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap<",
            "TK;TV;>.EntrySet;"
        }
    .end annotation
.end field

.field final header:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field private keySet:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap<",
            "TK;TV;>.KeySet;"
        }
    .end annotation
.end field

.field modCount:I

.field size:I

.field table:[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field threshold:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 45
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$1;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$1;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 64
    sget-object v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    invoke-direct {p0, v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;-><init>(Ljava/util/Comparator;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Comparator;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Comparator<",
            "-TK;>;)V"
        }
    .end annotation

    .line 75
    invoke-direct {p0}, Ljava/util/AbstractMap;-><init>()V

    const/4 v0, 0x0

    .line 54
    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    .line 55
    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    if-eqz p1, :cond_0

    goto :goto_0

    .line 78
    :cond_0
    sget-object p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    :goto_0
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->comparator:Ljava/util/Comparator;

    .line 79
    new-instance p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    invoke-direct {p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;-><init>()V

    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/16 p1, 0x10

    .line 80
    new-array p1, p1, [Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->table:[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 81
    array-length v0, p1

    div-int/lit8 v0, v0, 0x2

    array-length p1, p1

    div-int/lit8 p1, p1, 0x4

    add-int/2addr v0, p1

    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->threshold:I

    return-void
.end method

.method private doubleCapacity()V
    .locals 2

    .line 561
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->table:[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    invoke-static {v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->doubleCapacity([Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object v0

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->table:[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 562
    array-length v1, v0

    div-int/lit8 v1, v1, 0x2

    array-length v0, v0

    div-int/lit8 v0, v0, 0x4

    add-int/2addr v1, v0

    iput v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->threshold:I

    return-void
.end method

.method static doubleCapacity([Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<K:",
            "Ljava/lang/Object;",
            "V:",
            "Ljava/lang/Object;",
            ">([",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;)[",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 571
    array-length v0, p0

    mul-int/lit8 v1, v0, 0x2

    .line 573
    new-array v1, v1, [Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 574
    new-instance v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlIterator;

    invoke-direct {v2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlIterator;-><init>()V

    .line 575
    new-instance v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;

    invoke-direct {v3}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;-><init>()V

    .line 576
    new-instance v4, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;

    invoke-direct {v4}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;-><init>()V

    const/4 v5, 0x0

    const/4 v6, 0x0

    :goto_0
    if-ge v6, v0, :cond_7

    .line 580
    aget-object v7, p0, v6

    if-nez v7, :cond_0

    goto :goto_4

    .line 586
    :cond_0
    invoke-virtual {v2, v7}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlIterator;->reset(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    const/4 v8, 0x0

    const/4 v9, 0x0

    .line 589
    :goto_1
    invoke-virtual {v2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlIterator;->next()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object v10

    if-eqz v10, :cond_2

    .line 590
    iget v10, v10, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->hash:I

    and-int/2addr v10, v0

    if-nez v10, :cond_1

    add-int/lit8 v8, v8, 0x1

    goto :goto_1

    :cond_1
    add-int/lit8 v9, v9, 0x1

    goto :goto_1

    .line 598
    :cond_2
    invoke-virtual {v3, v8}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->reset(I)V

    .line 599
    invoke-virtual {v4, v9}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->reset(I)V

    .line 600
    invoke-virtual {v2, v7}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlIterator;->reset(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 601
    :goto_2
    invoke-virtual {v2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlIterator;->next()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object v7

    if-eqz v7, :cond_4

    .line 602
    iget v10, v7, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->hash:I

    and-int/2addr v10, v0

    if-nez v10, :cond_3

    .line 603
    invoke-virtual {v3, v7}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->add(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    goto :goto_2

    .line 605
    :cond_3
    invoke-virtual {v4, v7}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->add(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    goto :goto_2

    :cond_4
    const/4 v7, 0x0

    if-lez v8, :cond_5

    .line 610
    invoke-virtual {v3}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->root()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object v8

    goto :goto_3

    :cond_5
    move-object v8, v7

    :goto_3
    aput-object v8, v1, v6

    add-int v8, v6, v0

    if-lez v9, :cond_6

    .line 611
    invoke-virtual {v4}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->root()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object v7

    :cond_6
    aput-object v7, v1, v8

    :goto_4
    add-int/lit8 v6, v6, 0x1

    goto :goto_0

    :cond_7
    return-object v1
.end method

.method private equal(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    if-eq p1, p2, :cond_1

    if-eqz p1, :cond_0

    .line 227
    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method private readObject(Ljava/io/ObjectInputStream;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 870
    new-instance p1, Ljava/io/InvalidObjectException;

    const-string v0, "Deserialization is unsupported"

    invoke-direct {p1, v0}, Ljava/io/InvalidObjectException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method private rebalance(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;Z)V"
        }
    .end annotation

    :goto_0
    if-eqz p1, :cond_e

    .line 343
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 344
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    .line 345
    iget v3, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_1

    :cond_0
    const/4 v3, 0x0

    :goto_1
    if-eqz v1, :cond_1

    .line 346
    iget v4, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_2

    :cond_1
    const/4 v4, 0x0

    :goto_2
    sub-int v5, v3, v4

    const/4 v6, -0x2

    if-ne v5, v6, :cond_6

    .line 350
    iget-object v0, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 351
    iget-object v3, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz v3, :cond_2

    .line 352
    iget v3, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_3

    :cond_2
    const/4 v3, 0x0

    :goto_3
    if-eqz v0, :cond_3

    .line 353
    iget v2, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    :cond_3
    sub-int/2addr v2, v3

    const/4 v0, -0x1

    if-eq v2, v0, :cond_5

    if-nez v2, :cond_4

    if-nez p2, :cond_4

    goto :goto_4

    .line 360
    :cond_4
    invoke-direct {p0, v1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rotateRight(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 361
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rotateLeft(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    goto :goto_5

    .line 357
    :cond_5
    :goto_4
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rotateLeft(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    :goto_5
    if-eqz p2, :cond_d

    goto :goto_9

    :cond_6
    const/4 v1, 0x2

    const/4 v6, 0x1

    if-ne v5, v1, :cond_b

    .line 368
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 369
    iget-object v3, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz v3, :cond_7

    .line 370
    iget v3, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_6

    :cond_7
    const/4 v3, 0x0

    :goto_6
    if-eqz v1, :cond_8

    .line 371
    iget v2, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    :cond_8
    sub-int/2addr v2, v3

    if-eq v2, v6, :cond_a

    if-nez v2, :cond_9

    if-nez p2, :cond_9

    goto :goto_7

    .line 378
    :cond_9
    invoke-direct {p0, v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rotateLeft(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 379
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rotateRight(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    goto :goto_8

    .line 375
    :cond_a
    :goto_7
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rotateRight(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    :goto_8
    if-eqz p2, :cond_d

    goto :goto_9

    :cond_b
    if-nez v5, :cond_c

    add-int/lit8 v3, v3, 0x1

    .line 386
    iput v3, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    if-eqz p2, :cond_d

    goto :goto_9

    .line 393
    :cond_c
    invoke-static {v3, v4}, Ljava/lang/Math;->max(II)I

    move-result v0

    add-int/2addr v0, v6

    iput v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    if-nez p2, :cond_d

    goto :goto_9

    .line 342
    :cond_d
    iget-object p1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_0

    :cond_e
    :goto_9
    return-void
.end method

.method private replaceInParent(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;)V"
        }
    .end annotation

    .line 315
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v1, 0x0

    .line 316
    iput-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz p2, :cond_0

    .line 318
    iput-object v0, p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    :cond_0
    if-eqz v0, :cond_2

    .line 322
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-ne v1, p1, :cond_1

    .line 323
    iput-object p2, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_0

    .line 326
    :cond_1
    iput-object p2, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_0

    .line 329
    :cond_2
    iget p1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->hash:I

    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->table:[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    array-length v1, v0

    add-int/lit8 v1, v1, -0x1

    and-int/2addr p1, v1

    .line 330
    aput-object p2, v0, p1

    :goto_0
    return-void
.end method

.method private rotateLeft(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;)V"
        }
    .end annotation

    .line 405
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 406
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 407
    iget-object v2, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 408
    iget-object v3, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 411
    iput-object v2, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz v2, :cond_0

    .line 413
    iput-object p1, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 416
    :cond_0
    invoke-direct {p0, p1, v1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 419
    iput-object p1, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 420
    iput-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v4, 0x0

    if-eqz v0, :cond_1

    .line 423
    iget v0, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    if-eqz v2, :cond_2

    .line 424
    iget v2, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_1

    :cond_2
    const/4 v2, 0x0

    .line 423
    :goto_1
    invoke-static {v0, v2}, Ljava/lang/Math;->max(II)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    iput v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 425
    iget p1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    if-eqz v3, :cond_3

    .line 426
    iget v4, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 425
    :cond_3
    invoke-static {p1, v4}, Ljava/lang/Math;->max(II)I

    move-result p1

    add-int/lit8 p1, p1, 0x1

    iput p1, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    return-void
.end method

.method private rotateRight(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;)V"
        }
    .end annotation

    .line 433
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 434
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 435
    iget-object v2, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 436
    iget-object v3, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 439
    iput-object v3, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz v3, :cond_0

    .line 441
    iput-object p1, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 444
    :cond_0
    invoke-direct {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 447
    iput-object p1, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 448
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v4, 0x0

    if-eqz v1, :cond_1

    .line 451
    iget v1, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    if-eqz v3, :cond_2

    .line 452
    iget v3, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    goto :goto_1

    :cond_2
    const/4 v3, 0x0

    .line 451
    :goto_1
    invoke-static {v1, v3}, Ljava/lang/Math;->max(II)I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    iput v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 453
    iget p1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    if-eqz v2, :cond_3

    .line 454
    iget v4, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 453
    :cond_3
    invoke-static {p1, v4}, Ljava/lang/Math;->max(II)I

    move-result p1

    add-int/lit8 p1, p1, 0x1

    iput p1, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    return-void
.end method

.method private static secondaryHash(I)I
    .locals 2

    ushr-int/lit8 v0, p0, 0x14

    ushr-int/lit8 v1, p0, 0xc

    xor-int/2addr v0, v1

    xor-int/2addr p0, v0

    ushr-int/lit8 v0, p0, 0x7

    xor-int/2addr v0, p0

    ushr-int/lit8 p0, p0, 0x4

    xor-int/2addr p0, v0

    return p0
.end method

.method private writeReplace()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/ObjectStreamException;
        }
    .end annotation

    .line 865
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0, p0}, Ljava/util/LinkedHashMap;-><init>(Ljava/util/Map;)V

    return-object v0
.end method


# virtual methods
.method public clear()V
    .locals 4

    .line 108
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->table:[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Ljava/util/Arrays;->fill([Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 v0, 0x0

    .line 109
    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    .line 110
    iget v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    .line 113
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 114
    iget-object v2, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    :goto_0
    if-eq v2, v0, :cond_0

    .line 115
    iget-object v3, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 116
    iput-object v1, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v1, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-object v2, v3

    goto :goto_0

    .line 120
    :cond_0
    iput-object v0, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v0, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    return-void
.end method

.method public containsKey(Ljava/lang/Object;)Z
    .locals 0

    .line 94
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public entrySet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/util/Map$Entry<",
            "TK;TV;>;>;"
        }
    .end annotation

    .line 461
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->entrySet:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$EntrySet;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 462
    :cond_0
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$EntrySet;

    invoke-direct {v0, p0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$EntrySet;-><init>(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;)V

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->entrySet:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$EntrySet;

    :goto_0
    return-object v0
.end method

.method find(Ljava/lang/Object;Z)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;Z)",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 135
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->comparator:Ljava/util/Comparator;

    .line 136
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->table:[Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 137
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v2

    invoke-static {v2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->secondaryHash(I)I

    move-result v6

    .line 138
    array-length v2, v1

    const/4 v9, 0x1

    sub-int/2addr v2, v9

    and-int/2addr v2, v6

    .line 139
    aget-object v3, v1, v2

    const/4 v4, 0x0

    if-eqz v3, :cond_5

    .line 145
    sget-object v5, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    if-ne v0, v5, :cond_0

    .line 146
    move-object v5, p1

    check-cast v5, Ljava/lang/Comparable;

    goto :goto_0

    :cond_0
    move-object v5, v4

    :goto_0
    if-eqz v5, :cond_1

    .line 151
    iget-object v7, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->key:Ljava/lang/Object;

    invoke-interface {v5, v7}, Ljava/lang/Comparable;->compareTo(Ljava/lang/Object;)I

    move-result v7

    goto :goto_1

    .line 152
    :cond_1
    iget-object v7, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->key:Ljava/lang/Object;

    invoke-interface {v0, p1, v7}, Ljava/util/Comparator;->compare(Ljava/lang/Object;Ljava/lang/Object;)I

    move-result v7

    :goto_1
    if-nez v7, :cond_2

    return-object v3

    :cond_2
    if-gez v7, :cond_3

    .line 160
    iget-object v8, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_2

    :cond_3
    iget-object v8, v3, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    :goto_2
    if-nez v8, :cond_4

    move-object v10, v3

    move v11, v7

    goto :goto_3

    :cond_4
    move-object v3, v8

    goto :goto_0

    :cond_5
    const/4 v7, 0x0

    move-object v10, v3

    const/4 v11, 0x0

    :goto_3
    if-nez p2, :cond_6

    return-object v4

    .line 175
    :cond_6
    iget-object v7, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-nez v10, :cond_9

    .line 179
    sget-object p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    if-ne v0, p2, :cond_8

    instance-of p2, p1, Ljava/lang/Comparable;

    if-eqz p2, :cond_7

    goto :goto_4

    .line 180
    :cond_7
    new-instance p2, Ljava/lang/ClassCastException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " is not Comparable"

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/ClassCastException;-><init>(Ljava/lang/String;)V

    throw p2

    .line 182
    :cond_8
    :goto_4
    new-instance p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iget-object v8, v7, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-object v3, p2

    move-object v4, v10

    move-object v5, p1

    invoke-direct/range {v3 .. v8}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;-><init>(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Ljava/lang/Object;ILio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 183
    aput-object p2, v1, v2

    goto :goto_6

    .line 185
    :cond_9
    new-instance p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iget-object v8, v7, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-object v3, p2

    move-object v4, v10

    move-object v5, p1

    invoke-direct/range {v3 .. v8}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;-><init>(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Ljava/lang/Object;ILio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    if-gez v11, :cond_a

    .line 187
    iput-object p2, v10, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_5

    .line 189
    :cond_a
    iput-object p2, v10, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 191
    :goto_5
    invoke-direct {p0, v10, v9}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rebalance(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Z)V

    .line 194
    :goto_6
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    add-int/lit8 v0, p1, 0x1

    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    iget v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->threshold:I

    if-le p1, v0, :cond_b

    .line 195
    invoke-direct {p0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->doubleCapacity()V

    .line 197
    :cond_b
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    add-int/2addr p1, v9

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    return-object p2
.end method

.method findByEntry(Ljava/util/Map$Entry;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map$Entry<",
            "**>;)",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 221
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p0, v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 222
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->value:Ljava/lang/Object;

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    invoke-direct {p0, v1, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->equal(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    const/4 v1, 0x0

    .line 205
    :try_start_0
    invoke-virtual {p0, p1, v1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->find(Ljava/lang/Object;Z)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/ClassCastException; {:try_start_0 .. :try_end_0} :catch_0

    nop

    :catch_0
    :cond_0
    return-object v0
.end method

.method public get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 89
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 90
    iget-object p1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->value:Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public keySet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "TK;>;"
        }
    .end annotation

    .line 466
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->keySet:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 467
    :cond_0
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;

    invoke-direct {v0, p0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;-><init>(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;)V

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->keySet:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$KeySet;

    :goto_0
    return-object v0
.end method

.method public put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;TV;)TV;"
        }
    .end annotation

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    .line 101
    invoke-virtual {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->find(Ljava/lang/Object;Z)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p1

    .line 102
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->value:Ljava/lang/Object;

    .line 103
    iput-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->value:Ljava/lang/Object;

    return-object v0

    .line 99
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "key == null"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public remove(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 124
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->removeInternalByKey(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 125
    iget-object p1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->value:Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method removeInternal(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Z)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;Z)V"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p2, :cond_0

    .line 250
    iget-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v1, p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 251
    iget-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v1, p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 252
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 255
    :cond_0
    iget-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 256
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 257
    iget-object v2, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v3, 0x0

    if-eqz p2, :cond_4

    if-eqz v1, :cond_4

    .line 269
    iget v2, p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    iget v4, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    if-le v2, v4, :cond_1

    invoke-virtual {p2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->last()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p2

    goto :goto_0

    :cond_1
    invoke-virtual {v1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->first()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p2

    .line 270
    :goto_0
    invoke-virtual {p0, p2, v3}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->removeInternal(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Z)V

    .line 273
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz v1, :cond_2

    .line 275
    iget v2, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 276
    iput-object v1, p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 277
    iput-object p2, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 278
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_1

    :cond_2
    const/4 v2, 0x0

    .line 281
    :goto_1
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-eqz v1, :cond_3

    .line 283
    iget v3, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 284
    iput-object v1, p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 285
    iput-object p2, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 286
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 288
    :cond_3
    invoke-static {v2, v3}, Ljava/lang/Math;->max(II)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    iput v0, p2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 289
    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    return-void

    :cond_4
    if-eqz p2, :cond_5

    .line 292
    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 293
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_2

    :cond_5
    if-eqz v1, :cond_6

    .line 295
    invoke-direct {p0, p1, v1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 296
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_2

    .line 298
    :cond_6
    invoke-direct {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V

    .line 301
    :goto_2
    invoke-direct {p0, v2, v3}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->rebalance(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Z)V

    .line 302
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    .line 303
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->modCount:I

    return-void
.end method

.method removeInternalByKey(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 307
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    .line 309
    invoke-virtual {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->removeInternal(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;Z)V

    :cond_0
    return-object p1
.end method

.method public size()I
    .locals 1

    .line 85
    iget v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;->size:I

    return v0
.end method
