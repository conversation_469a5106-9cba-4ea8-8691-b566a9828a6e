.class public final Lio/dcloud/uts/prompt/R$string;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/prompt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "string"
.end annotation


# static fields
.field public static uni_app_uni_prompt_cancel:I = 0x7f0e016a

.field public static uni_app_uni_prompt_model_cancel:I = 0x7f0e016b

.field public static uni_app_uni_prompt_model_sure:I = 0x7f0e016c

.field public static uni_app_uni_prompt_placement:I = 0x7f0e016d


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
