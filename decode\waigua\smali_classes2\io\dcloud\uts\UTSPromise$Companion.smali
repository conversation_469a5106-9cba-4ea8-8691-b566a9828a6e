.class public final Lio/dcloud/uts/UTSPromise$Companion;
.super Ljava/lang/Object;
.source "UTSPromise.kt"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/UTSPromise;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Companion"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\n\u0010\u0005\u001a\u0006\u0012\u0002\u0008\u00030\u0006J\u001e\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\u0008\u0010\n\u001a\u0004\u0018\u00010\u0001H\u0002J\u0010\u0010\u000b\u001a\u00020\u00042\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u0001J,\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\t0\u000e0\u0008\"\u0004\u0008\u0001\u0010\t2\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\t0\u00080\u000eJ2\u0010\u0010\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\t0\u00110\u000e0\u0008\"\u0004\u0008\u0001\u0010\t2\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\t0\u00080\u000eJ*\u0010\u0012\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\t0\u00110\u000e0\u0008\"\u0004\u0008\u0001\u0010\t2\n\u0010\u000f\u001a\u0006\u0012\u0002\u0008\u00030\u000eJ\u001e\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\n\u0010\u000f\u001a\u0006\u0012\u0002\u0008\u00030\u000eJ&\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\t0\u00080\u000eJ\u001e\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\n\u0010\u000f\u001a\u0006\u0012\u0002\u0008\u00030\u000eJ&\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\t0\u00080\u000eJ\u001e\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\n\u0010\u000f\u001a\u0006\u0012\u0002\u0008\u00030\u000eJ\u0018\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00082\n\u0008\u0002\u0010\n\u001a\u0004\u0018\u00010\u0001J\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0008J\u001f\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\u0006\u0010\n\u001a\u0002H\t\u00a2\u0006\u0002\u0010\u001aJ \u0010\u0019\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008J\u001e\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u0002H\t0\u0008\"\u0004\u0008\u0001\u0010\t2\n\u0008\u0002\u0010\n\u001a\u0004\u0018\u00010\u0001\u00a8\u0006\u001c"
    }
    d2 = {
        "Lio/dcloud/uts/UTSPromise$Companion;",
        "",
        "()V",
        "_immediateFn",
        "",
        "fn",
        "Lkotlin/Function;",
        "_resolve",
        "Lio/dcloud/uts/UTSPromise;",
        "T",
        "value",
        "_unhandledRejectionFn",
        "err",
        "all",
        "Lio/dcloud/uts/UTSArray;",
        "arr",
        "allSettled",
        "Lio/dcloud/uts/UTSPromiseSettledResult;",
        "allSettled_origin",
        "all_origin",
        "any",
        "any_origin",
        "race",
        "race_origin",
        "reject",
        "resolve",
        "(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;",
        "resolve_origin",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 174
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Lio/dcloud/uts/UTSPromise$Companion;-><init>()V

    return-void
.end method

.method private final _resolve(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            ")",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 188
    instance-of v0, p1, Lio/dcloud/uts/UTSPromise;

    if-eqz v0, :cond_0

    .line 189
    check-cast p1, Lio/dcloud/uts/UTSPromise;

    return-object p1

    .line 191
    :cond_0
    new-instance v0, Lio/dcloud/uts/UTSPromise;

    new-instance v1, Lio/dcloud/uts/UTSPromise$Companion$_resolve$1;

    invoke-direct {v1, p1}, Lio/dcloud/uts/UTSPromise$Companion$_resolve$1;-><init>(Ljava/lang/Object;)V

    check-cast v1, Lkotlin/jvm/functions/Function2;

    invoke-direct {v0, v1}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v0
.end method

.method public static final synthetic access$_resolve(Lio/dcloud/uts/UTSPromise$Companion;Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    .line 174
    invoke-direct {p0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->_resolve(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic reject$default(Lio/dcloud/uts/UTSPromise$Companion;Ljava/lang/Object;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    .line 198
    :cond_0
    invoke-virtual {p0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->reject(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic resolve_origin$default(Lio/dcloud/uts/UTSPromise$Companion;Ljava/lang/Object;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    .line 184
    :cond_0
    invoke-virtual {p0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->resolve_origin(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final _immediateFn(Lkotlin/Function;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/Function<",
            "*>;)V"
        }
    .end annotation

    const-string v0, "fn"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 366
    new-instance v0, Lio/dcloud/uts/UTSPromise$Companion$_immediateFn$1;

    invoke-direct {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion$_immediateFn$1;-><init>(Lkotlin/Function;)V

    check-cast v0, Lkotlin/jvm/functions/Function0;

    const/4 p1, 0x0

    .line 369
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    .line 366
    invoke-static {v0, p1}, Lio/dcloud/uts/UTSTimerKt;->setTimeout(Lkotlin/jvm/functions/Function0;Ljava/lang/Number;)Ljava/lang/Number;

    return-void
.end method

.method public final _unhandledRejectionFn(Ljava/lang/Object;)V
    .locals 3

    const/4 v0, 0x2

    .line 372
    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    const-string v2, "Possible Unhandled Promise Rejection:"

    aput-object v2, v0, v1

    const/4 v2, 0x1

    aput-object p1, v0, v2

    invoke-static {v0}, Lio/dcloud/uts/console;->warn([Ljava/lang/Object;)V

    .line 373
    instance-of v0, p1, Ljava/lang/Throwable;

    if-eqz v0, :cond_0

    .line 378
    sget-object v0, Lio/dcloud/uts/console;->INSTANCE:Lio/dcloud/uts/console;

    new-array v2, v2, [Ljava/lang/Object;

    aput-object p1, v2, v1

    invoke-virtual {v0, v2}, Lio/dcloud/uts/console;->errorV1WithStack([Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public final all(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 207
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->all_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final allSettled(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "Lio/dcloud/uts/UTSPromiseSettledResult<",
            "TT;>;>;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 314
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->allSettled_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final allSettled_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "*>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "Lio/dcloud/uts/UTSPromiseSettledResult<",
            "TT;>;>;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 317
    new-instance v0, Lio/dcloud/uts/UTSPromise;

    new-instance v1, Lio/dcloud/uts/UTSPromise$Companion$allSettled_origin$1;

    invoke-direct {v1, p1}, Lio/dcloud/uts/UTSPromise$Companion$allSettled_origin$1;-><init>(Lio/dcloud/uts/UTSArray;)V

    check-cast v1, Lkotlin/jvm/functions/Function2;

    invoke-direct {v0, v1}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v0
.end method

.method public final all_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "*>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 210
    new-instance v0, Lio/dcloud/uts/UTSPromise;

    new-instance v1, Lio/dcloud/uts/UTSPromise$Companion$all_origin$1;

    invoke-direct {v1, p1}, Lio/dcloud/uts/UTSPromise$Companion$all_origin$1;-><init>(Lio/dcloud/uts/UTSArray;)V

    check-cast v1, Lkotlin/jvm/functions/Function2;

    invoke-direct {v0, v1}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v0
.end method

.method public final any(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 274
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->any_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final any_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "*>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 277
    new-instance v0, Lio/dcloud/uts/UTSPromise;

    new-instance v1, Lio/dcloud/uts/UTSPromise$Companion$any_origin$1;

    invoke-direct {v1, p1}, Lio/dcloud/uts/UTSPromise$Companion$any_origin$1;-><init>(Lio/dcloud/uts/UTSArray;)V

    check-cast v1, Lkotlin/jvm/functions/Function2;

    invoke-direct {v0, v1}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v0
.end method

.method public final race(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 258
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->race_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final race_origin(Lio/dcloud/uts/UTSArray;)Lio/dcloud/uts/UTSPromise;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSArray<",
            "*>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "arr"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 261
    new-instance v0, Lio/dcloud/uts/UTSPromise;

    new-instance v1, Lio/dcloud/uts/UTSPromise$Companion$race_origin$1;

    invoke-direct {v1, p1}, Lio/dcloud/uts/UTSPromise$Companion$race_origin$1;-><init>(Lio/dcloud/uts/UTSArray;)V

    check-cast v1, Lkotlin/jvm/functions/Function2;

    invoke-direct {v0, v1}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v0
.end method

.method public final reject(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lio/dcloud/uts/UTSPromise<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .line 199
    new-instance v0, Lio/dcloud/uts/UTSPromise;

    new-instance v1, Lio/dcloud/uts/UTSPromise$Companion$reject$1;

    invoke-direct {v1, p1}, Lio/dcloud/uts/UTSPromise$Companion$reject$1;-><init>(Ljava/lang/Object;)V

    check-cast v1, Lkotlin/jvm/functions/Function2;

    invoke-direct {v0, v1}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v0
.end method

.method public final resolve()Lio/dcloud/uts/UTSPromise;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lio/dcloud/uts/UTSPromise<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .line 176
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-static {v0, v1, v2, v1}, Lio/dcloud/uts/UTSPromise$Companion;->resolve_origin$default(Lio/dcloud/uts/UTSPromise$Companion;Ljava/lang/Object;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;

    move-result-object v0

    return-object v0
.end method

.method public final resolve(Lio/dcloud/uts/UTSPromise;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "value"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 182
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->resolve_origin(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final resolve(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;)",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    .line 179
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    invoke-virtual {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->resolve_origin(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final resolve_origin(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            ")",
            "Lio/dcloud/uts/UTSPromise<",
            "TT;>;"
        }
    .end annotation

    .line 185
    sget-object v0, Lio/dcloud/uts/UTSPromise;->Companion:Lio/dcloud/uts/UTSPromise$Companion;

    invoke-direct {v0, p1}, Lio/dcloud/uts/UTSPromise$Companion;->_resolve(Ljava/lang/Object;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method
