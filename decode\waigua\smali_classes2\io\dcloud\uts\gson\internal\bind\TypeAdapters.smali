.class public final Lio/dcloud/uts/gson/internal/bind/TypeAdapters;
.super Ljava/lang/Object;
.source "TypeAdapters.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter;
    }
.end annotation


# static fields
.field public static final ATOMIC_BOOLEAN:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/concurrent/atomic/AtomicBoolean;",
            ">;"
        }
    .end annotation
.end field

.field public static final ATOMIC_BOOLEAN_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final ATOMIC_INTEGER:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/concurrent/atomic/AtomicInteger;",
            ">;"
        }
    .end annotation
.end field

.field public static final ATOMIC_INTEGER_ARRAY:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/concurrent/atomic/AtomicIntegerArray;",
            ">;"
        }
    .end annotation
.end field

.field public static final ATOMIC_INTEGER_ARRAY_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final ATOMIC_INTEGER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final BIG_DECIMAL:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/math/BigDecimal;",
            ">;"
        }
    .end annotation
.end field

.field public static final BIG_INTEGER:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/math/BigInteger;",
            ">;"
        }
    .end annotation
.end field

.field public static final BIT_SET:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/BitSet;",
            ">;"
        }
    .end annotation
.end field

.field public static final BIT_SET_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final BOOLEAN:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public static final BOOLEAN_AS_STRING:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public static final BOOLEAN_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final BYTE:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final BYTE_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final CALENDAR:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/Calendar;",
            ">;"
        }
    .end annotation
.end field

.field public static final CALENDAR_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final CHARACTER:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Character;",
            ">;"
        }
    .end annotation
.end field

.field public static final CHARACTER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final CLASS:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field

.field public static final CLASS_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final CURRENCY:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/Currency;",
            ">;"
        }
    .end annotation
.end field

.field public static final CURRENCY_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final DOUBLE:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final ENUM_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final FLOAT:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final INET_ADDRESS:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/net/InetAddress;",
            ">;"
        }
    .end annotation
.end field

.field public static final INET_ADDRESS_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final INTEGER:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final INTEGER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final JSON_ELEMENT:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Lio/dcloud/uts/gson/JsonElement;",
            ">;"
        }
    .end annotation
.end field

.field public static final JSON_ELEMENT_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final LOCALE:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/Locale;",
            ">;"
        }
    .end annotation
.end field

.field public static final LOCALE_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final LONG:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final SHORT:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation
.end field

.field public static final SHORT_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final STRING:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final STRING_BUFFER:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/StringBuffer;",
            ">;"
        }
    .end annotation
.end field

.field public static final STRING_BUFFER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final STRING_BUILDER:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/lang/StringBuilder;",
            ">;"
        }
    .end annotation
.end field

.field public static final STRING_BUILDER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final STRING_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final URI:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/net/URI;",
            ">;"
        }
    .end annotation
.end field

.field public static final URI_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final URL:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/net/URL;",
            ">;"
        }
    .end annotation
.end field

.field public static final URL_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

.field public static final UUID:Lio/dcloud/uts/gson/TypeAdapter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "Ljava/util/UUID;",
            ">;"
        }
    .end annotation
.end field

.field public static final UUID_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 70
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$1;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$1;-><init>()V

    .line 81
    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$1;->nullSafe()Lio/dcloud/uts/gson/TypeAdapter;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CLASS:Lio/dcloud/uts/gson/TypeAdapter;

    .line 83
    const-class v1, Ljava/lang/Class;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CLASS_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 85
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$2;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$2;-><init>()V

    .line 130
    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$2;->nullSafe()Lio/dcloud/uts/gson/TypeAdapter;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BIT_SET:Lio/dcloud/uts/gson/TypeAdapter;

    .line 132
    const-class v1, Ljava/util/BitSet;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BIT_SET_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 134
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$3;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$3;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BOOLEAN:Lio/dcloud/uts/gson/TypeAdapter;

    .line 157
    new-instance v1, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$4;

    invoke-direct {v1}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$4;-><init>()V

    sput-object v1, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BOOLEAN_AS_STRING:Lio/dcloud/uts/gson/TypeAdapter;

    .line 171
    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Boolean;

    .line 172
    invoke-static {v1, v2, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BOOLEAN_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 174
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$5;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$5;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BYTE:Lio/dcloud/uts/gson/TypeAdapter;

    .line 194
    sget-object v1, Ljava/lang/Byte;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Byte;

    .line 195
    invoke-static {v1, v2, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BYTE_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 197
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$6;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$6;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->SHORT:Lio/dcloud/uts/gson/TypeAdapter;

    .line 216
    sget-object v1, Ljava/lang/Short;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Short;

    .line 217
    invoke-static {v1, v2, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->SHORT_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 219
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$7;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$7;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->INTEGER:Lio/dcloud/uts/gson/TypeAdapter;

    .line 237
    sget-object v1, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Integer;

    .line 238
    invoke-static {v1, v2, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->INTEGER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 240
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$8;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$8;-><init>()V

    .line 251
    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$8;->nullSafe()Lio/dcloud/uts/gson/TypeAdapter;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->ATOMIC_INTEGER:Lio/dcloud/uts/gson/TypeAdapter;

    .line 252
    const-class v1, Ljava/util/concurrent/atomic/AtomicInteger;

    .line 253
    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->ATOMIC_INTEGER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 255
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$9;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$9;-><init>()V

    .line 262
    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$9;->nullSafe()Lio/dcloud/uts/gson/TypeAdapter;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->ATOMIC_BOOLEAN:Lio/dcloud/uts/gson/TypeAdapter;

    .line 263
    const-class v1, Ljava/util/concurrent/atomic/AtomicBoolean;

    .line 264
    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->ATOMIC_BOOLEAN_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 266
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$10;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$10;-><init>()V

    .line 293
    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$10;->nullSafe()Lio/dcloud/uts/gson/TypeAdapter;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->ATOMIC_INTEGER_ARRAY:Lio/dcloud/uts/gson/TypeAdapter;

    .line 294
    const-class v1, Ljava/util/concurrent/atomic/AtomicIntegerArray;

    .line 295
    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->ATOMIC_INTEGER_ARRAY_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 297
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$11;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$11;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->LONG:Lio/dcloud/uts/gson/TypeAdapter;

    .line 316
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$12;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$12;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->FLOAT:Lio/dcloud/uts/gson/TypeAdapter;

    .line 331
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$13;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$13;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->DOUBLE:Lio/dcloud/uts/gson/TypeAdapter;

    .line 346
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$14;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$14;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CHARACTER:Lio/dcloud/uts/gson/TypeAdapter;

    .line 365
    sget-object v1, Ljava/lang/Character;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Character;

    .line 366
    invoke-static {v1, v2, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CHARACTER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 368
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$15;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$15;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->STRING:Lio/dcloud/uts/gson/TypeAdapter;

    .line 388
    new-instance v1, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$16;

    invoke-direct {v1}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$16;-><init>()V

    sput-object v1, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BIG_DECIMAL:Lio/dcloud/uts/gson/TypeAdapter;

    .line 406
    new-instance v1, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$17;

    invoke-direct {v1}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$17;-><init>()V

    sput-object v1, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->BIG_INTEGER:Lio/dcloud/uts/gson/TypeAdapter;

    .line 424
    const-class v1, Ljava/lang/String;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->STRING_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 426
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$18;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$18;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->STRING_BUILDER:Lio/dcloud/uts/gson/TypeAdapter;

    .line 441
    const-class v1, Ljava/lang/StringBuilder;

    .line 442
    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->STRING_BUILDER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 444
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$19;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$19;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->STRING_BUFFER:Lio/dcloud/uts/gson/TypeAdapter;

    .line 459
    const-class v1, Ljava/lang/StringBuffer;

    .line 460
    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->STRING_BUFFER_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 462
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$20;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$20;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->URL:Lio/dcloud/uts/gson/TypeAdapter;

    .line 478
    const-class v1, Ljava/net/URL;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->URL_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 480
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$21;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$21;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->URI:Lio/dcloud/uts/gson/TypeAdapter;

    .line 500
    const-class v1, Ljava/net/URI;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->URI_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 502
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$22;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$22;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->INET_ADDRESS:Lio/dcloud/uts/gson/TypeAdapter;

    .line 518
    const-class v1, Ljava/net/InetAddress;

    .line 519
    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newTypeHierarchyFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->INET_ADDRESS_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 521
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$23;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$23;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->UUID:Lio/dcloud/uts/gson/TypeAdapter;

    .line 536
    const-class v1, Ljava/util/UUID;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->UUID_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 538
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$24;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$24;-><init>()V

    .line 547
    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$24;->nullSafe()Lio/dcloud/uts/gson/TypeAdapter;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CURRENCY:Lio/dcloud/uts/gson/TypeAdapter;

    .line 548
    const-class v1, Ljava/util/Currency;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CURRENCY_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 550
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$25;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$25;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CALENDAR:Lio/dcloud/uts/gson/TypeAdapter;

    .line 615
    const-class v1, Ljava/util/Calendar;

    const-class v2, Ljava/util/GregorianCalendar;

    .line 616
    invoke-static {v1, v2, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactoryForMultipleTypes(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->CALENDAR_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 618
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$26;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$26;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->LOCALE:Lio/dcloud/uts/gson/TypeAdapter;

    .line 653
    const-class v1, Ljava/util/Locale;

    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->LOCALE_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 655
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$27;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$27;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->JSON_ELEMENT:Lio/dcloud/uts/gson/TypeAdapter;

    .line 732
    const-class v1, Lio/dcloud/uts/gson/JsonElement;

    .line 733
    invoke-static {v1, v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->newTypeHierarchyFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->JSON_ELEMENT_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    .line 781
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$28;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$28;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters;->ENUM_FACTORY:Lio/dcloud/uts/gson/TypeAdapterFactory;

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 65
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 66
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public static newFactory(Lio/dcloud/uts/gson/reflect/TypeToken;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<TT:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/gson/reflect/TypeToken<",
            "TTT;>;",
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "TTT;>;)",
            "Lio/dcloud/uts/gson/TypeAdapterFactory;"
        }
    .end annotation

    .line 797
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$29;

    invoke-direct {v0, p0, p1}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$29;-><init>(Lio/dcloud/uts/gson/reflect/TypeToken;Lio/dcloud/uts/gson/TypeAdapter;)V

    return-object v0
.end method

.method public static newFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<TT:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TTT;>;",
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "TTT;>;)",
            "Lio/dcloud/uts/gson/TypeAdapterFactory;"
        }
    .end annotation

    .line 807
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$30;

    invoke-direct {v0, p0, p1}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$30;-><init>(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)V

    return-object v0
.end method

.method public static newFactory(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<TT:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TTT;>;",
            "Ljava/lang/Class<",
            "TTT;>;",
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "-TTT;>;)",
            "Lio/dcloud/uts/gson/TypeAdapterFactory;"
        }
    .end annotation

    .line 820
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$31;

    invoke-direct {v0, p0, p1, p2}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$31;-><init>(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)V

    return-object v0
.end method

.method public static newFactoryForMultipleTypes(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<TT:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TTT;>;",
            "Ljava/lang/Class<",
            "+TTT;>;",
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "-TTT;>;)",
            "Lio/dcloud/uts/gson/TypeAdapterFactory;"
        }
    .end annotation

    .line 835
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$32;

    invoke-direct {v0, p0, p1, p2}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$32;-><init>(Ljava/lang/Class;Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)V

    return-object v0
.end method

.method public static newTypeHierarchyFactory(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)Lio/dcloud/uts/gson/TypeAdapterFactory;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T1:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TT1;>;",
            "Lio/dcloud/uts/gson/TypeAdapter<",
            "TT1;>;)",
            "Lio/dcloud/uts/gson/TypeAdapterFactory;"
        }
    .end annotation

    .line 854
    new-instance v0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$33;

    invoke-direct {v0, p0, p1}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$33;-><init>(Ljava/lang/Class;Lio/dcloud/uts/gson/TypeAdapter;)V

    return-object v0
.end method
