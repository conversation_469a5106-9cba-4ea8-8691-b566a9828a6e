.class public final Lio/dcloud/uts/android/AndroidUTSContextKt;
.super Ljava/lang/Object;
.source "AndroidUTSContext.kt"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010!\n\u0000\n\u0002\u0010\u0004\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u001a\n\u0010\u0000\u001a\u0004\u0018\u00010\u0001H\u0007\u001a\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0003H\u0007\u001a\n\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u0007\u001a\u001a\u0010\u0007\u001a\u00020\u00082\u0010\u0008\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0008\u0018\u00010\nH\u0007\u001a\u001a\u0010\u000b\u001a\u00020\u00082\u0010\u0008\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0008\u0018\u00010\nH\u0007\u001a\u001a\u0010\u000c\u001a\u00020\u00082\u0010\u0008\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0008\u0018\u00010\nH\u0007\u001ae\u0010\r\u001a\u00020\u00082[\u0008\u0002\u0010\t\u001aU\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0012\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020\u00030\u0013\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0014\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020\u00150\u0013\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0016\u0012\u0004\u0012\u00020\u0008\u0018\u00010\u000eH\u0007\u001a[\u0010\u0017\u001a\u00020\u00082Q\u0008\u0002\u0010\t\u001aK\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0012\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0018\u0012\u0015\u0012\u0013\u0018\u00010\u0019\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u001a\u0012\u0004\u0012\u00020\u0008\u0018\u00010\u000eH\u0007\u001a\u001a\u0010\u001b\u001a\u00020\u00082\u0010\u0008\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u0008\u0018\u00010\nH\u0007\u001a \u0010\u001c\u001a\u00020\u00082\u0016\u0008\u0002\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u0008\u0018\u00010\u001dH\u0007\u001a \u0010\u001f\u001a\u00020\u00082\u0016\u0008\u0002\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u0008\u0018\u00010\u001dH\u0007\u001a\u0016\u0010 \u001a\u00020\u00082\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\nH\u0007\u001a\u0016\u0010!\u001a\u00020\u00082\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\nH\u0007\u001a\u0016\u0010\"\u001a\u00020\u00082\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\nH\u0007\u001aa\u0010#\u001a\u00020\u00082W\u0010\t\u001aS\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0012\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020\u00030\u0013\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0014\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020\u00150\u0013\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0016\u0012\u0004\u0012\u00020\u00080\u000eH\u0007\u001aW\u0010$\u001a\u00020\u00082M\u0010\t\u001aI\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0012\u0012\u0013\u0012\u00110\u000f\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u0018\u0012\u0015\u0012\u0013\u0018\u00010\u0019\u00a2\u0006\u000c\u0008\u0010\u0012\u0008\u0008\u0011\u0012\u0004\u0008\u0008(\u001a\u0012\u0004\u0012\u00020\u00080\u000eH\u0007\u001a\u0016\u0010%\u001a\u00020\u00082\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\nH\u0007\u001a\u0016\u0010&\u001a\u00020\u00082\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\nH\u0007\u001a\u001c\u0010\'\u001a\u00020\u00082\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u00080\u001dH\u0007\u001a\u001c\u0010(\u001a\u00020\u00082\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00080\u001dH\u0007\u00a8\u0006)"
    }
    d2 = {
        "getAppContext",
        "Landroid/content/Context;",
        "getResourcePath",
        "",
        "resourceName",
        "getUniActivity",
        "Landroid/app/Activity;",
        "offAppActivityBack",
        "",
        "callback",
        "Lkotlin/Function0;",
        "offAppActivityDestroy",
        "offAppActivityPause",
        "offAppActivityRequestPermissionsResult",
        "Lkotlin/Function3;",
        "",
        "Lkotlin/ParameterName;",
        "name",
        "requestCode",
        "",
        "permissions",
        "",
        "grantResults",
        "offAppActivityResult",
        "resultCode",
        "Landroid/content/Intent;",
        "data",
        "offAppActivityResume",
        "offAppConfigChange",
        "Lkotlin/Function1;",
        "Lio/dcloud/uts/UTSJSONObject;",
        "offAppTrimMemory",
        "onAppActivityBack",
        "onAppActivityDestroy",
        "onAppActivityPause",
        "onAppActivityRequestPermissionsResult",
        "onAppActivityResult",
        "onAppActivityResume",
        "onAppActivityStop",
        "onAppConfigChange",
        "onAppTrimMemory",
        "utsplugin_release"
    }
    k = 0x2
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final getAppContext()Landroid/content/Context;
    .locals 1
    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.getAppContext()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    .line 184
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getHostAppContext()Landroid/content/Context;

    move-result-object v0

    return-object v0
.end method

.method public static final getResourcePath(Ljava/lang/String;)Ljava/lang/String;
    .locals 4
    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.getResourcePath()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "resourceName"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 204
    invoke-static {}, Lio/dcloud/uts/android/AndroidUTSContextKt;->getAppContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, ""

    invoke-static {v0, v1}, Lio/dcloud/common/util/AppRuntime;->isAppResourcesInAssetsPath(Landroid/content/Context;Ljava/lang/String;)Z

    move-result v0

    .line 205
    sget-object v1, Lio/dcloud/common/util/BaseInfo;->sCacheFsAppsPath:Ljava/lang/String;

    .line 206
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v3, Lio/dcloud/common/util/BaseInfo;->sDefaultBootApp:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "/www/"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    .line 207
    new-instance v2, Ljava/lang/StringBuilder;

    const-string v3, "file:///android_asset/"

    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    if-nez v0, :cond_0

    .line 211
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_0
    return-object v2
.end method

.method public static final getUniActivity()Landroid/app/Activity;
    .locals 3
    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.getUniActivity()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    .line 192
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getInstance()Lio/dcloud/feature/uniapp/AbsSDKInstance;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 196
    :cond_0
    invoke-static {}, Lio/dcloud/feature/weex/WeexInstanceMgr;->self()Lio/dcloud/feature/weex/WeexInstanceMgr;

    move-result-object v0

    sget-object v1, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v1}, Lio/dcloud/uts/android/AndroidUTSContext;->getInstance()Lio/dcloud/feature/uniapp/AbsSDKInstance;

    move-result-object v1

    const-string v2, "null cannot be cast to non-null type com.taobao.weex.WXSDKInstance"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v1, Lcom/taobao/weex/WXSDKInstance;

    invoke-virtual {v0, v1}, Lio/dcloud/feature/weex/WeexInstanceMgr;->findWebview(Lcom/taobao/weex/WXSDKInstance;)Lio/dcloud/common/DHInterface/IWebview;

    move-result-object v0

    .line 197
    invoke-interface {v0}, Lio/dcloud/common/DHInterface/IWebview;->getActivity()Landroid/app/Activity;

    move-result-object v0

    return-object v0
.end method

.method public static final offAppActivityBack(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppActivityBack()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 153
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getBackListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 156
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getBackListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppActivityBack$default(Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 150
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppActivityBack(Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public static final offAppActivityDestroy(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppActivityDestroy()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 120
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getDestroyListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 123
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getDestroyListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppActivityDestroy$default(Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 117
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppActivityDestroy(Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public static final offAppActivityPause(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppActivityPause()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 83
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getPauseListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 86
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getPauseListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppActivityPause$default(Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 80
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppActivityPause(Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public static final offAppActivityRequestPermissionsResult(Lkotlin/jvm/functions/Function3;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;-",
            "Ljava/util/List<",
            "Ljava/lang/Number;",
            ">;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppActivityRequestPermissionsResult()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 173
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getPermissionsResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 176
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getPermissionsResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppActivityRequestPermissionsResult$default(Lkotlin/jvm/functions/Function3;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 168
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppActivityRequestPermissionsResult(Lkotlin/jvm/functions/Function3;)V

    return-void
.end method

.method public static final offAppActivityResult(Lkotlin/jvm/functions/Function3;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Landroid/content/Intent;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppActivityResult()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 137
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnActivityResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 140
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnActivityResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppActivityResult$default(Lkotlin/jvm/functions/Function3;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 134
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppActivityResult(Lkotlin/jvm/functions/Function3;)V

    return-void
.end method

.method public static final offAppActivityResume(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppActivityResume()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 99
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getResumeListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 102
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getResumeListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppActivityResume$default(Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 96
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppActivityResume(Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public static final offAppConfigChange(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lio/dcloud/uts/UTSJSONObject;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppConfigChange()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 43
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnConfigChangedListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 46
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnConfigChangedListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppConfigChange$default(Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 40
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppConfigChange(Lkotlin/jvm/functions/Function1;)V

    return-void
.end method

.method public static final offAppTrimMemory(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Number;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.offAppTrimMemory()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    if-nez p0, :cond_0

    .line 66
    sget-object p0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {p0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnTrimMemoryListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->clear()V

    goto :goto_0

    .line 69
    :cond_0
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnTrimMemoryListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public static synthetic offAppTrimMemory$default(Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p1, p1, 0x1

    if-eqz p1, :cond_0

    const/4 p0, 0x0

    .line 62
    :cond_0
    invoke-static {p0}, Lio/dcloud/uts/android/AndroidUTSContextKt;->offAppTrimMemory(Lkotlin/jvm/functions/Function1;)V

    return-void
.end method

.method public static final onAppActivityBack(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppActivityBack()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 146
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getBackListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppActivityDestroy(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppActivityDestroy()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 113
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getDestroyListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppActivityPause(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppActivityPause()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 76
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getPauseListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppActivityRequestPermissionsResult(Lkotlin/jvm/functions/Function3;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;-",
            "Ljava/util/List<",
            "Ljava/lang/Number;",
            ">;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppActivityRequestPermissionsResult()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 164
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getPermissionsResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppActivityResult(Lkotlin/jvm/functions/Function3;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Landroid/content/Intent;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppActivityResult()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 130
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnActivityResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppActivityResume(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppActivityResume()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 92
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getResumeListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppActivityStop(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppActivityStop()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 108
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getStopListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppConfigChange(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lio/dcloud/uts/UTSJSONObject;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppConfigChange()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 33
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnConfigChangedListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static final onAppTrimMemory(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Number;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lkotlin/Deprecated;
        message = "\u5f53\u524d\u65b9\u6cd5\u5df2\u5e9f\u5f03\uff0c\u672a\u6765\u67d0\u4e2a\u65f6\u523b\u5c06\u4f1a\u88ab\u79fb\u9664"
        replaceWith = .subannotation Lkotlin/ReplaceWith;
            expression = "UTSAndroid.onAppTrimMemory()"
            imports = {
                "io.dcloud.uts.UTSAndroid"
            }
        .end subannotation
    .end annotation

    const-string v0, "callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 55
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-virtual {v0}, Lio/dcloud/uts/android/AndroidUTSContext;->getOnTrimMemoryListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method
