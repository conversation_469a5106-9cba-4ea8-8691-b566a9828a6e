var PlusObject=function(plusContext,param){plusContext.__param__=param;var dc_plusobjects={},dc_plusMouldes={};function PlusObject(e,t){for(var n in this.weex=t,this.weex_instance_id=e,this.__HtMl_Id__,this.__io__dc_vue_call_exec_sync="undefined"!=typeof global&&global.__io__dc_vue_call_exec_sync,this.__io__dc_vue_call_exec="undefined"!=typeof global&&global.__io__dc_vue_call_exec,this.weexBridge=t.requireModule("plus"),this.globalEvent=t.requireModule("globalEvent"),dc_plusMouldes)if(!this[n]){var i=dc_plusMouldes[n](plusContext,this,this.require,param[n]);i&&(this[n]=i)}}return PlusObject.prototype.updateConfigInfo=function(){var e=this.weexBridge.getConfigInfo();if(e)for(var t in"string"==typeof e&&(e=JSON.parse(e)),this.__WebVieW_Id__=e.__WebVieW_Id__,this.__HtMl_Id__=e.__HtMl_Id__,e){var n=this[t];if("object"==typeof n&&e[t])if(n.updateInfo)n.updateInfo(e[t]);else{var i=e[t];for(var o in i)n[o]=n[o]||i[o]}}},PlusObject.prototype.require=function(e){if(!this[e]){var t=dc_plusMouldes[e](plusContext,this,this.require,param[e]);t&&(this[e]=t)}return this[e]},PlusObject.prototype.importMoudle=function(name){if(this[name])return this[name];var script=this.weexBridge.importMoudle(name);return eval(script),this.require(name)},PlusObject.register=function(e,t){dc_plusMouldes[e]=t},PlusObject.newPlus=function(e,t){var n=dc_plusobjects[e];return n||((n=new PlusObject(e,t)).updateConfigInfo(),dc_plusobjects[e]=n),n},PlusObject.deletePlus=function(e){dc_plusobjects[e]&&delete dc_plusobjects[e]},PlusObject}(plusContext,param);function WebviewGroupMoudle(e,t,n){t=t;var i="WebviewGroup";function o(e,n){t.webview.NView.prototype.constructor.apply(this,[i]),this.__children=[];var o=[];if(Array.isArray(e))for(var r=0;r<e.length;r++){var s=e[r];s.styles=s.styles||{},s.styles.name=s.id;var a=new t.webview.Webview(s.url,s.styles,!0,s.extras);t.webview.__pushWindow__(a),this.__children.push(a);var c=[a.__uuid__,[s.url,s.styles,a.__callback_id__,location.host+location.pathname,s.extras]];o.push(c)}t.webview.exec(this,"createGroup",[o,n,this.__callback_id__])}var r=o.prototype;return t.tools.extend(r,n.NView.prototype),r.constructor=o,o.prototype.setSelectIndex=function(e){t.webview.exec(this,"setSelectIndex",[e])},o.prototype.children=function(){return this.__children},n.createGroup=function(e,t){return new o(e,t)},o}function LoadWebviewMoudle(e,t,n){var i=n,o="NWindow",r=(t=t).bridge,s=t.tools;function a(){return e.getLocationHerf(t)}function c(e){if(Array.isArray(e))for(var t=0;t<e.length;t++)u(e[t])}function u(e){!function(e){if(e.onclick&&"function"==typeof e.onclick){var n=r.callbackId(function(){e.onclick(e)});e.__cb__={id:n,htmlId:t.__HtMl_Id__}}}(e)}function l(e){if(Array.isArray(e))for(var t,n=0;n<e.length;n++)(t=e[n])&&"input"==t.tag?t.inputStyles&&(t.inputStyles.onComplete&&"function"==typeof t.inputStyles.onComplete&&(t.inputStyles.__onCompleteCallBackId__=r.callbackId(t.inputStyles.onComplete)),t.inputStyles.onFocus&&"function"==typeof t.inputStyles.onFocus&&(t.inputStyles.__onFocusCallBackId__=r.callbackId(t.inputStyles.onFocus)),t.inputStyles.onBlur&&"function"==typeof t.inputStyles.onBlur&&(t.inputStyles.__onBlurCallBackId__=r.callbackId(t.inputStyles.onBlur))):t&&"richtext"==t.tag&&t.richTextStyles&&t.richTextStyles.onClick&&"function"==typeof t.richTextStyles.onClick&&(t.richTextStyles.__onClickCallBackId__=r.callbackId(t.richTextStyles.onClick))}function _(e,n,r,u){if(this.__view_array__=new Array,i.NView.prototype.constructor.apply(this,[o]),this.id=null,n&&n.name&&(this.id=n.name),u)for(var _ in u)this[_]=u[_];!this.id&&e&&(this.id=n.name=e),s.platform==s.IOS&&n&&n.navigationbar&&(this.__navigationbar__=n.navigationbar);var f=[],d=null;if(n&&(d=n.titleNView||n.navigationbar)&&(d.uuid="_Nav_Bar_"+this.__uuid__,f.push(d.uuid),t.nativeObj.__appendSubViewInfo(d),c(d.buttons),l(d.tags)),n&&n.subNViews&&Array.isArray(n.subNViews)){for(var p=0;p<n.subNViews.length;p++){var h=n.subNViews[p];l(h.tags),h.uuid=s.UUID("Nativeview"),f.push(h.uuid),t.nativeObj.__appendSubViewInfo(h)}this.__subNViewsUids__=f,this.checkJSObject=!0,setTimeout(function(){this.checkJSObject=!1,t.nativeObj.__removeSubViewInfos(f)})}r||(i.__pushWindow__(this),i.exec(this,o,[e,n,this.__callback_id__,a(),u]))}var f=_.prototype;function d(){e.__needNotifyNative__&&"touchmove"==this.type&&(e.__needNotifyNative__=i.execSync(t.webview.currentWebview(),"needTouchEvent",[])),f.oldPreventDefault.call(this)}return t.tools.extend(f,i.NView.prototype),f.constructor=_,f.show=function(e,t,n,o){var s;n&&"function"==typeof n&&(s=r.callbackId(function(){n()})),i.exec(this,"show",[e,t,null,s,o])},f.close=function(e,n,o){this===t.webview.__JSON_Window_Stack[t.__HtMl_Id__]&&t.bridge.callbackFromNative(this.__callback_id__,{status:t.bridge.OK,message:{evt:"close"},keepCallback:!0}),i.exec(this,"close",[e,n,o])},f.setStyle=function(e){if(e&&"object"==typeof e){if(e){var t=e.titleNView||e.navigationbar;t&&(l(t.tags),c(t.buttons))}i.exec(this,"setOption",[e])}},f.updateSubNViews=function(e){if(Array.isArray(e))for(var t,n=0;n<e.length;n++)(t=e[n])&&l(t.tags);i.exec(this,"updateSubNViews",[e])},f.nativeInstanceObject=function(){var e=t.ios||t.android;return e&&e.getWebviewById?e.getWebviewById(this.__uuid__):null},f.hide=function(e,t,n){i.exec(this,"hide",[e,t,n])},f.setVisible=function(e){i.exec(this,"setVisible",[e])},f.pause=function(){i.exec(this,"pause",[])},f.resume=function(){i.exec(this,"resume",[])},f.isPause=function(){return i.execSync(this,"isPause",[])},f.isVisible=function(){return i.execSync(this,"isVisible",[])},f.setFixBottom=function(e){i.exec(this,"setFixBottom",[e])},f.getSubNViews=function(){var e=[],n=i.execSync(this,"getSubNViews",[]);if(n&&Array.isArray(n)&&n.length>0){for(var o=0;o<n.length;o++){var r=t.nativeObj.View.__getViewByUidAndCreate(n[o]);e.push(r)}return e}if(this.checkJSObject&&this.__subNViewsUids__)for(o=0;o<this.__subNViewsUids__.length;o++){(r=t.nativeObj.View.__getViewByUidFromCache(this.__subNViewsUids__[o]))&&e.push(r)}return e},f.getNavigationbar=function(){return this.getTitleNView()},f.setTitleNViewButtonBadge=function(e){i.exec(this,"setTitleNViewButtonBadge",[e])},f.removeTitleNViewButtonBadge=function(e){i.exec(this,"removeTitleNViewButtonBadge",[e])},f.showTitleNViewButtonRedDot=function(e){i.exec(this,"showTitleNViewButtonRedDot",[e])},f.hideTitleNViewButtonRedDot=function(e){i.exec(this,"hideTitleNViewButtonRedDot",[e])},f.setTitleNViewSearchInputFocus=function(e){i.exec(this,"setTitleNViewSearchInputFocus",[e])},f.setTitleNViewSearchInputText=function(e){i.exec(this,"setTitleNViewSearchInputText",[e])},f.getTitleNViewSearchInputText=function(){return i.execSync(this,"getTitleNViewSearchInputText")},f.setTitleNViewButtonStyle=function(e,t){void 0!=e&&t&&(u(t),i.exec(this,"setTitleNViewButtonStyle",[e,t]))},f.getTitleNView=function(){var e;if(s.platform==s.IOS){var n="_Nav_Bar_"+this.__uuid__;!(e=t.nativeObj.View.getViewById(n))&&this.__navigationbar__&&((e=new t.nativeObj.View(n,this.__navigationbar__,"",!0)).__uuid__=n)}else{n=this.__uuid__;if(e=t.nativeObj.View.getViewById(n))!e&&this.__navigationbar__&&((e=new t.nativeObj.View(n,this.__navigationbar__,"",!0)).__uuid__=n);else{var o=i.execSync(this,"getTitleNView",[]);o&&((e=new t.nativeObj.View(o.id,o.styles,!0)).__uuid__=o.uuid)}}return e},e.__needNotifyNative__=!1,f.__needTouchEvent__=function(){Event.prototype.preventDefault!==d&&(f.oldPreventDefault=Event.prototype.preventDefault,Event.prototype.preventDefault=d)},f.drag=function(e,n,o){if(e&&e.direction&&"string"==typeof e.direction&&e.moveMode&&"string"==typeof e.moveMode){var s=e.moveMode.toLocaleLowerCase();if("followfinger"==s||"follow"==s||"silent"==s||"bounce"==s){var a=e.direction.toLocaleLowerCase();if("left"==a||"right"==a||"rtl"==a||"ltr"==a){if("rtl"==a&&(e.direction="left"),"ltr"==a&&(e.direction="right"),n){var c=n.view;if(null!=c&&("string"==typeof c||c instanceof t.webview.Webview||c instanceof t.nativeObj.View)&&("string"!=typeof c&&(n.view=c.__uuid__),n.moveMode)){if("string"!=typeof n.moveMode)return;var u=n.moveMode.toLocaleLowerCase();if("follow"!=u&&"silent"!=u)return;if("silent"==s&&"silent"==u)return}}var l,_;o&&"function"==typeof o&&(l=t.webview.currentWebview().__uuid__,_=r.callbackId(o)),i.exec(this,"drag",[e,n,l,_])}}}},f.setJsFile=function(e,t){e&&"string"==typeof e&&i.exec(this,"setPreloadJsFile",[e,t])},f.appendJsFile=function(e){e&&"string"==typeof e&&i.exec(this,"appendPreloadJsFile",[e])},f.setContentVisible=function(e){i.exec(this,"setContentVisible",[e])},f.opener=function(){var e=i.execSync(this,"opener",[]);return e?t.webview._find__Window_By_UUID__(e.uuid,e.id,e.extras):null},f.opened=function(){var e=i.execSync(this,"opened",[]);if(e){for(var n=[],o={},s=0;s<e.length;s++){var a=e[s],c=t.webview.__JSON_Window_Stack[a];c||((c=new t.webview.Webview(null,null,!0,a.extras)).__uuid__=a.uuid,c.id=a.id,r.exec("UI","execMethod",["UI","setcallbackid",[c.__uuid__,[c.__callback_id__]]])),n.push(c),o[c.__uuid__]=c}return n}},f.remove=function(e){var n;if("string"==typeof e)n=e;else{if(!(e instanceof t.webview.Webview))return e instanceof t.nativeObj.View?void i.exec(this,"removeNativeView",[o,e.__uuid__]):void 0;n=e.__uuid__}i.exec(this,"remove",[n])},f.removeFromParent=function(){i.exec(this,"removeFromParent",[])},f.parent=function(){var e=i.execSync(this,"parent",[]);return e?t.webview._find__Window_By_UUID__(e.uuid,e.id,e.extras):null},f.children=function(){for(var e=[],n=i.execSync(this,"children",[]),o=0;o<n.length;o++)e.push(t.webview._find__Window_By_UUID__(n[o].uuid,n[o].id,n[o].extras));return e},f.getURL=function(){return i.execSync(this,"getUrl",[])},f.getTitle=function(){return i.execSync(this,"getTitle",[])},f.getStyle=function(){return i.execSync(this,"getOption")},f.loadURL=function(e,t){e&&"string"==typeof e&&i.exec(this,"load",[e,a(),t])},f.loadData=function(e,t){e&&"string"==typeof e&&i.exec(this,"loadData",[e,t])},f.stop=function(){i.exec(this,"stop",[])},f.reload=function(e){i.exec(this,"reload",[e])},f.draw=function(e,t,n,o){if(s.platform==s.IOS)e.__captureWebview?e.__captureWebview(this.__uuid__,o,t,n):"function"==typeof n&&n({code:-1,message:"Parameter error"});else{var a=function(e){"function"==typeof n&&n(e)};if(e&&e.__id__){var c=r.callbackId(function(){"function"==typeof t&&t()},a);i.exec(this,"draw",[e.__id__,this.__uuid__,c,o])}else a({code:-1,message:"Destroyed objects"})}},f.checkRenderedContent=function(e,t,n){var o=r.callbackId(function(e){"function"==typeof t&&t(e)},function(e){"function"==typeof n&&n(e)});i.exec(this,"checkRenderedContent",[this.__uuid__,o,e])},f.back=function(){i.exec(this,"back",[])},f.forward=function(){i.exec(this,"forward",[])},f.canBack=function(e){var t;e&&"function"==typeof e&&(e&&(t=r.callbackId(function(t){var n={};n.canBack=t,e(n)})),i.exec(this,"canBack",[t]))},f.canForward=function(e){var t;e&&"function"==typeof e&&(e&&(t=r.callbackId(function(t){var n={};n.canForward=t,e(n)})),i.exec(this,"canForward",[t]))},f.clear=function(){i.exec(this,"clear",[])},f.evalJS=function(e){e&&"string"==typeof e&&i.exec(this,"evalJS",[e])},f.evalJSSync=function(e){if(e&&"string"==typeof e)return i.execSync(this,"evalJSSync",[e])},f.test=function(e){i.exec(this,"test",[e])},f.append=function(e){e&&(e instanceof _||e instanceof t.webview.WebviewGroup?(this.__view_array__.push(e),i.exec(this,"append",[e.__IDENTITY__,e.__uuid__])):e instanceof t.nativeObj.View?i.exec(this,"appendNativeView",[e.IDENTITY||o,e.__uuid__]):i.exec(this,"appendNativeView",[e.IDENTITY||o,e.__uuid__||e.id||e._UUID_]))},f.setPullToRefresh=function(e,n){var o;n&&(o=t.bridge.callbackId(n)),this.addEventListener("pulldownrefreshevent",n,!1),i.exec(this,"setPullToRefresh",[e,o])},f.endPullToRefresh=function(){i.exec(this,"endPullToRefresh",[])},f.beginPullToRefresh=function(){i.exec(this,"beginPullToRefresh",[])},f.setBounce=function(e,t){i.exec(this,"setBounce",[e,t])},f.resetBounce=function(){i.exec(this,"resetBounce",[])},f.setBlockNetworkImage=function(e){i.exec(this,"setBlockNetworkImage",[e])},f.captureSnapshot=function(e,n,o){var r="function"!=typeof n?null:function(e){n()},s="function"!=typeof o?null:function(e){o(e)},a=t.bridge.callbackId(r,s);t.tools.platform==t.tools.IOS?r():i.exec(this,"captureSnapshot",[e,a])},f.clearSnapshot=function(e){t.tools.platform!=t.tools.IOS&&i.exec(this,"clearSnapshot",[e])},f.overrideUrlLoading=function(e,t){var n=r.callbackId(function(e){"function"==typeof t&&t(e)});i.exec(this,"overrideUrlLoading",[e,n])},f.overrideResourceRequest=function(e){i.exec(this,"overrideResourceRequest",[e])},f.isHardwareAccelerated=function(){return s.platform!=s.IOS&&i.execSync(this,"isHardwareAccelerated",[])},f.listenResourceLoading=function(e,t){var n=r.callbackId(function(e){"function"==typeof t&&t(e)});i.exec(this,"listenResourceLoading",[e,n])},f.setCssFile=function(e){i.exec(this,"setCssFile",[e])},f.setCssText=function(e){i.exec(this,"setCssText",[e])},f.showBehind=function(e){i.exec(this,"showBehind",[e.__IDENTITY__,e.__uuid__])},f.animate=function(e,t){var n;s.platform!=s.IOS?(t&&(n=r.callbackId(function(){"function"==typeof t&&t()})),i.exec(this,"webview_animate",[e,n])):setTimeout(t,10)},f.interceptTouchEvent=function(e){i.exec(this,"interceptTouchEvent",[e])},f.setFavoriteOptions=function(e){i.exec(this,"setFavoriteOptions",[e])},f.getFavoriteOptions=function(){return i.execSync(this,"getFavoriteOptions")},f.setShareOptions=function(e){i.exec(this,"setShareOptions",[e])},f.getShareOptions=function(){return i.execSync(this,"getShareOptions")},f.getSafeAreaInsets=function(){return t.tools.ANDROID==t.tools.platform?{bottom:0,left:0,right:0,top:0,deviceBottom:0,deviceLeft:0,deviceRight:0,deviceTop:0}:i.execSync(this,"getSafeAreaInsets")},f.restore=function(){i.exec(this,"webview_restore",null)},f.setSoftinputTemporary=function(e){i.exec(this,"setSoftinputTemporary",[e])},_}service.register("weexPlus",{create:function(e,t,n){return{instance:{WeexPlus:function(t){return PlusObject.newPlus(e,t)}}}},refresh:function(e,t,n){},destroy:function(e,t){PlusObject.deletePlus(e)}}),PlusObject.register("tools",function(e,t,n,i){var o={__UUID__:0,UNKNOWN:-1,IOS:0,ANDROID:1,platform:-1,debug:!1,UUID:function(e){return e+this.__UUID__+++(new Date).valueOf()},extend:function(e,t){for(var n in t)e[n]=t[n]},typeName:function(e){return Object.prototype.toString.call(e).slice(8,-1)},isDate:function(e){return"Date"==this.typeName(e)},isArray:function(e){return"Array"==this.typeName(e)},isDebug:function(){return this.debug},stringify:function(t){return e&&e.JSON3?e.JSON3.stringify(t):JSON.stringify(t)},isNumber:function(e){return"number"==typeof e||e instanceof Number},getElementOffsetInWebview:function(e,t){for(var n=0,i=e;i;)n+=i[t],i=i.offsetParent;return n},getElementOffsetXInWebview:function(e){return this.getElementOffsetInWebview(e,"offsetLeft")},getElementOffsetYInWebview:function(e){return this.getElementOffsetInWebview(e,"offsetTop")},execJSfile:function(e){var t=document.createElement("script");t.type="text/javascript",t.src=e,function e(t){var n=document.head,i=document.body;n?n.insertBefore(t,n.firstChild):i?i.insertBefore(t,i.firstChild):setTimeout(function(){e(t)},100)}(t)},copyObjProp2Obj:function(e,t,n){var i=!!Array.isArray(n);for(var o in t){var r=!0;if(i)for(var s=0;s<n.length;s++)if(o==n[s]){r=!1;break}r?e[o]=t[o]:r=!0}},clone:function(e){if(!e||"function"==typeof e||this.isDate(e)||"object"!=typeof e)return e;var t,n;if(this.isArray(e)){for(t=[],n=0;n<e.length;++n)t.push(this.clone(e[n]));return t}for(n in t={},e)n in t&&t[n]==e[n]||(t[n]=o.clone(e[n]));return t}};return o.debug=!(!e.__param__||!e.__param__.debug),o.platform=e.__param__.platform,o}),PlusObject.register("bridge",function(plusContext,plus,require,param){var T=plus.tools;return plus.globalEvent.addEventListener("nativeToUniJs",function(e){if(e)try{eval(e.data)}catch(e){}}),{NO_RESULT:0,OK:1,CLASS_NOT_FOUND_EXCEPTION:2,ILLEGAL_ACCESS_EXCEPTION:3,INSTANTIATION_EXCEPTION:4,MALFORMED_URL_EXCEPTION:5,IO_EXCEPTION:6,INVALID_ACTION:7,JSON_EXCEPTION:8,ERROR:9,callbacks:{},exec:function(e,t,n,i,o){n&&n.push(i),T.IOS==T.platform?plus.weexBridge.exec(T.stringify([plus.__HtMl_Id__,e,t,i||null,n])):T.ANDROID==T.platform&&(plus.__io__dc_vue_call_exec?plus.__io__dc_vue_call_exec(plus.weex_instance_id,T.stringify(n),"pdr:"+T.stringify([e,t,!0])):plus.weexBridge.exec(T.stringify(n),"pdr:"+T.stringify([e,t,!0])))},execSync2:function(e,t,n,i,o){return this.execSync(e,t,n,i,o)},execSync:function(service,action,args,fn,force){var ret;if(T.IOS==T.platform){var json=T.stringify([plus.__HtMl_Id__,service,action,null,args]);ret=plus.weexBridge.execSync(json)}else T.ANDROID==T.platform&&(ret=plus.__io__dc_vue_call_exec_sync?plus.__io__dc_vue_call_exec_sync(plus.weex_instance_id,T.stringify(args),"pdr:"+T.stringify([service,action,!1])):plus.weexBridge.execSync(T.stringify(args),"pdr:"+T.stringify([service,action,!1])));if(fn)return fn(ret);var _ret=void 0;try{_ret=eval(ret)}catch(e){}return _ret},nativeEval:function(e){e()},callbackFromNative:function(e,t){var n=this.callbacks[e];n&&(t.status==this.OK&&n.success?n.success&&n.success(t.message):n.fail&&n.fail(t.message),t.keepCallback||delete this.callbacks[e])},callbackId:function(e,t){var n=T.UUID("plus");return this.callbacks[n]={success:e,fail:t},n}}}),PlusObject.register("obj",function(e,t,n,i){function o(){var e=this;e.__callbacks__={},e.__callback_id__=t.bridge.callbackId(function(t){var n=t.evt,i=t.args,o=e.__callbacks__[n];if(o)for(var r=o.length,s=0;s<r;s++)e.onCallback(o[s],n,i)})}return o.prototype.addEventListener=function(e,t,n){var i=!1;return t&&this.__callbacks__&&(this.__callbacks__[e]||(this.__callbacks__[e]=[],i=!0),this.__callbacks__[e].push(t)),i},o.prototype.removeEventListener=function(e,t){var n=!1;return this.__callbacks__&&this.__callbacks__[e]&&(this.__callbacks__[e].pop(t),(n=0===this.__callbacks__[e].length)&&(this.__callbacks__[e]=null)),n},{Callback:o}}),PlusObject.register("timer",function(e,t,n,i){var o=(t=t).weex.requireModule("timer"),r={setInterval:function(e,n){return o.setInterval(e,n),t.weex.document.taskCenter.callbackManager.lastCallbackId.toString()},clearInterval:function(e){o.clearInterval(e)},setTimeout:function(e,n){return o.setTimeout(e,n),t.weex.document.taskCenter.callbackManager.lastCallbackId.toString()},clearTimeout:function(e){o.clearTimeout(e)}};return t.setInterval=r.setInterval,t.clearInterval=r.clearInterval,t.setTimeout=r.setTimeout,t.clearTimeout=r.clearTimeout,r}),PlusObject.register("accelerometer",function(e,t,n,i){var o=t.bridge,r=t.tools,s="Accelerometer",a=!1,c={},u=[],l=null,_=function(e,t,n){this.xAxis=e,this.yAxis=t,this.zAxis=n};function f(e){var t=o.callbackId(function(e){var t=u.slice(0);l=new _(e.x,e.y,e.z);for(var n=0,i=t.length;n<i;n++)t[n].win(l)},function(e){for(var t=u.slice(0),n=0,i=t.length;n<i;n++)t[n].fail(e)});o.exec(s,"start",[t,e]),a=!0}function d(e,t){return{win:e,fail:t}}function p(e){var t=u.indexOf(e);t>-1&&(u.splice(t,1),0===u.length&&(o.exec(s,"stop",[]),a=!1))}return{getCurrentAcceleration:function(e,t,n){var i=d(function(t){p(i),e(t)},function(e){p(i),t&&t(e)});u.push(i),a||f(-1)},watchAcceleration:function(n,i,o){var s=o&&o.frequency&&"number"==typeof o.frequency?o.frequency:500,_=r.UUID("watch"),h=d(function(){},function(e){p(h),i&&i(e)});return u.push(h),c[_]={timer:(e.setInterval?e:t).setInterval(function(){l&&n(l)},s),listeners:h},a?l&&n(l):f(s),_},clearWatch:function(n){n&&c[n]&&((e.clearInterval?e:t).clearInterval(c[n].timer),p(c[n].listeners),delete c[n])}}}),PlusObject.register("audio",function(e,t,n,i){var o=t.bridge,r=t.tools;t.audio={getRecorder:function(){var e={_Audio_UUID__:r.UUID("Record"),supportedFormats:["amr","3gp","aac"],supportedSamplerates:[44100,16e3,8e3],record:function(e,t,n){var i=o.callbackId(t,n);o.exec("Audio","RecorderExecMethod",["record",[this._Audio_UUID__,i,e]])},stop:function(){o.exec("Audio","RecorderExecMethod",["stop",[this._Audio_UUID__]])},pause:function(){o.exec("Audio","RecorderExecMethod",["pause",[this._Audio_UUID__]])},resume:function(){o.exec("Audio","RecorderExecMethod",["resume",[this._Audio_UUID__]])}};return r.IOS==r.platform&&(e.supportedFormats=["wav","aac","amr","mp3"]),e},createPlayer:function(e){var t={_Player_Param:e,_Audio_Player_UUID_:r.UUID("Player"),play:function(e,t){var n=o.callbackId(e,t);o.exec("Audio","AudioExecMethod",["play",[this._Audio_Player_UUID_,n]])},pause:function(){o.exec("Audio","AudioExecMethod",["pause",[this._Audio_Player_UUID_]])},resume:function(){o.exec("Audio","AudioExecMethod",["resume",[this._Audio_Player_UUID_]])},stop:function(){o.exec("Audio","AudioExecMethod",["stop",[this._Audio_Player_UUID_]])},seekTo:function(e){o.exec("Audio","AudioExecMethod",["seekTo",[this._Audio_Player_UUID_,e]])},getDuration:function(){return o.execSync2("Audio","AudioSyncExecMethod",["getDuration",[this._Audio_Player_UUID_]])},getPosition:function(){return o.execSync2("Audio","AudioSyncExecMethod",["getPosition",[this._Audio_Player_UUID_]])},setRoute:function(e){o.exec("Audio","AudioExecMethod",["setRoute",[this._Audio_Player_UUID_,e]])},setSessionCategory:function(e){o.exec("Audio","AudioExecMethod",["setSessionCategory",[this._Audio_Player_UUID_,e]])},isPaused:function(){return o.execSync2("Audio","AudioSyncExecMethod",["getPaused",[this._Audio_Player_UUID_]])},getBuffered:function(){return o.execSync2("Audio","AudioSyncExecMethod",["getBuffered",[this._Audio_Player_UUID_]])},getStyles:function(e){return o.execSync2("Audio","AudioSyncExecMethod",["getStyles",[this._Audio_Player_UUID_,e]])},setStyles:function(e){o.exec("Audio","AudioExecMethod",["setStyles",[this._Audio_Player_UUID_,e]])},addEventListener:function(e,t){var n=o.callbackId(t);o.exec("Audio","AudioExecMethod",["addEventListener",[this._Audio_Player_UUID_,e,n]])},removeEventListener:function(e){o.exec("Audio","AudioExecMethod",["removeEventListener",[this._Audio_Player_UUID_,e]])},close:function(){o.exec("Audio","AudioExecMethod",["close",[this._Audio_Player_UUID_]])},playbackRate:function(e){o.exec("Audio","AudioExecMethod",["playbackRate",[this._Audio_Player_UUID_,e]])}},n={};return"string"==typeof t._Player_Param?n.src=t._Player_Param:n=t._Player_Param,o.execSync("Audio","AudioSyncExecMethod",["CreatePlayer",[t._Audio_Player_UUID_,n]]),t},ROUTE_SPEAKER:0,ROUTE_EARPIECE:1}}),PlusObject.register("barcode",function(e,t,n,i){var o="barcode",r=(t=t).tools,s=t.bridge,a={};function c(t,n,i,a,c,u){var l=this;l.IDENTITY=o,l.onmarked=null,l.onerror=null,l.isClose=!1,l.__uuid__=r.UUID("bc"),l.callbackId=null;var _=null;c||(c=l.__uuid__),this.callbackId=s.callbackId(function(e){"function"==typeof l.onmarked&&l.onmarked(e.type,e.message,e.file)},function(e){"function"==typeof l.onerror&&l.onerror(e)}),e.document&&(div=document.getElementById(t),null!=div&&void 0!=div&&(div.addEventListener("resize",function(){var e=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight];s.exec(o,"resize",[e])},!1),_=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight]));var f=!0;void 0==u||null==u||u||(f=!1),f&&s.exec(o,"Barcode",[this.__uuid__,this.callbackId,c,_,n,i,a])}var u=c.prototype;u.setStyle=function(e){this.isClose||s.exec(o,"setStyle",[this.__uuid__,e])},u.start=function(e){this.isClose||s.exec(o,"start",[this.__uuid__,e])},u.setFlash=function(e){this.isClose||s.exec(o,"setFlash",[this.__uuid__,e])},u.cancel=function(){this.isClose||s.exec(o,"cancel",[this.__uuid__])},u.close=function(){this.isClose||(s.exec(o,"close",[this.__uuid__]),this.isClose=!0)};var l={Barcode:c,create:function(e,n,i,o){var r=new t.barcode.Barcode(null,n,i,o,e,!0);return a[r.__uuid__]=r,a[r.__uuid__]},getBarcodeById:function(e){if(e&&"string"==typeof e){var n=s.execSync(o,"getBarcodeById",[e]);if(null!=n&&null!=n.uuid){if(a[n.uuid])return a[n.uuid];if(null!=n&&void 0!=n){var i=new t.barcode.Barcode(null,n.filters,n.options,n.autoDecodeCharset,e,!1);return i.__uuid__=n.uuid,s.exec(o,"addCallBack",[i.__uuid__,i.callbackId]),a[i.__uuid__]=i,i}return null}}},scan:function(e,t,n,i,r){var a="function"!=typeof t?null:function(e){t(e.type,e.message,e.file,e.charSet)},c="function"!=typeof n?null:function(e){n(e)},u=s.callbackId(a,c);s.exec(o,"scan",[u,e,i,r])},QR:0,EAN13:1,EAN8:2,AZTEC:3,DATAMATRIX:4,UPCA:5,UPCE:6,CODABAR:7,CODE39:8,CODE93:9,CODE128:10,ITF:11,MAXICODE:12,PDF417:13,RSS14:14,RSSEXPANDED:15};return l}),PlusObject.register("cache",function(e,t,n,i){var o=t.bridge,r="Cache";t.cache={clear:function(e){var t=o.callbackId(function(t){e&&e()},null);o.exec(r,"clear",[t])},calculate:function(e){var t=o.callbackId(function(t){e&&e(t)},null);o.exec(r,"calculate",[t])},setMaxSize:function(e){o.exec(r,"setMaxSize",[e])}}}),PlusObject.register("camera",function(e,t){var n,i=t.require("bridge");function o(){this.index=1,this.__busy__=!1,this.supportedImageResolutions=[],this.supportedVideoResolutions=[],this.supportedImageFormats=[],this.supportedVideoFormats=[]}var r=o.prototype;return r.captureImage=function(e,t,n){var o=this;if(!o.__busy__){var r="function"!=typeof e?null:function(t){o.__busy__=!1,e(t)},s="function"!=typeof t?null:function(e){o.__busy__=!1,t(e)},a=i.callbackId(r,s);n||(n={}),n.index||(n.index=this.index),i.exec("Camera","captureImage",[a,n])}},r.startVideoCapture=function(e,t,n){var o=this;if(!o.__busy__){var r="function"!=typeof e?null:function(t){o.__busy__=!1,e(t)},s="function"!=typeof t?null:function(e){o.__busy__=!1,t(e)},a=i.callbackId(r,s);n||(n={}),n.index||(n.index=this.index),i.exec("Camera","startVideoCapture",[a,n])}},r.stopVideoCapture=function(){i.exec("Camera","stopVideoCapture",[])},{getCamera:function(e){if(n)return n;if((n=new o).index=e,t.tools.ANDROID==t.tools.platform){var r=i.callbackId(function(e){n.supportedImageFormats=e.supportedImageFormats,n.supportedVideoFormats=e.supportedVideoFormats,n.supportedImageResolutions=e.supportedImageResolutions,n.supportedVideoResolutions=e.supportedVideoResolutions},null);return(s=i.execSync("Camera","getCamera",[n.__UUID__,e,r]))&&(n.supportedImageFormats=s.supportedImageFormats,n.supportedVideoFormats=s.supportedVideoFormats,n.supportedImageResolutions=s.supportedImageResolutions,n.supportedVideoResolutions=s.supportedVideoResolutions),n}var s;return(s=i.execSync("Camera","getCamera",[n.__UUID__,e]))?(n.supportedImageFormats=s.supportedImageFormats,n.supportedVideoFormats=s.supportedVideoFormats,n.supportedImageResolutions=s.supportedImageResolutions,n.supportedVideoResolutions=s.supportedVideoResolutions):(n.supportedImageFormats=["png","jpg"],n.supportedImageResolutions=["640*480","1280*720","960*540","high","medium","low"],n.supportedVideoFormats=["mp4"],n.supportedVideoResolutions=["640*480","1280*720","960*540"]),n}}}),PlusObject.register("contacts",function(e,t,n,i){var o,r,s=(t=t).bridge,a=t.tools,c=function(e){this.code=e||null};function u(e,t,n,i,o,r,s,a,c,u,l,_,f,d,p){this.id=e||null,this.rawId=p||null,this.target=0,this.displayName=t||null,this.name=n||null,this.nickname=i||null,this.phoneNumbers=o||null,this.emails=r||null,this.addresses=s||null,this.ims=a||null,this.organizations=c||null,this.birthday=u||null,this.note=l||null,this.photos=_||null,this.categories=f||null,this.urls=d||null}c.UNKNOWN_ERROR=0,c.INVALID_ARGUMENT_ERROR=1,c.TIMEOUT_ERROR=2,c.PENDING_OPERATION_ERROR=3,c.IO_ERROR=4,c.NOT_SUPPORTED_ERROR=5,c.PERMISSION_DENIED_ERROR=20;var l=u.prototype;function _(e){this.type=e}l.remove=function(e,t){var n=t&&function(e){t(new c(e))};if(null===this.id)n(c.UNKNOWN_ERROR);else{var i=s.callbackId(e,n);s.exec("Contacts","remove",[i,this.id,this.target],{cbid:i})}},l.clone=function(){var e=a.clone(this);function t(e){if(e)for(var t=e.length,n=0;n<t;++n)e[n].id=null}return e.id=null,t(e.phoneNumbers),t(e.emails),t(e.addresses),t(e.ims),t(e.organizations),t(e.categories),t(e.photos),t(e.urls),e},l.save=function(e,t){var n=this,i=function(e){t&&t(new c(e))},u=function(e){var t=e.birthday;if(null!==t){if(!a.isDate(t))try{t=new Date(t)}catch(e){t=null}a.isDate(t)&&(t=t.valueOf()),e.birthday=t}return e}(a.clone(this)),l=s.callbackId(function(t){if(t)try{var s=0==n.target?o.create(t):r.create(t);e&&(function(e,t){function n(e,t){if(e&&t)for(var n=e.length,i=0;i<n;++i)t[i].id=e[i].id}t.id=e.id,n(e.phoneNumbers,t.phoneNumbers),n(e.emails,t.emails),n(e.addresses,t.addresses),n(e.ims,t.ims),n(e.organizations,t.organizations),n(e.categories,t.categories),n(e.photos,t.photos),n(e.urls,t.urls)}(function(e){var t=e.birthday;try{a.isDate(t)||(e.birthday=new Date(parseFloat(t)))}catch(e){console.log("Cordova Contact convertIn error: exception creating date.")}return e}(s),n),e(n))}catch(e){console.log(e)}else i(c.UNKNOWN_ERROR)},i);s.exec("Contacts","save",[l,u,this.target],{cbid:l})};var f=_.prototype;f.create=function(e){var t=new u;for(var n in t.target=this.type,e)void 0!==t[n]&&e.hasOwnProperty(n)&&(t[n]=e[n]);return t},f.find=function(e,t,n,i){var r=s.callbackId(function(e){for(var n=[],i=0,r=e.length;i<r;i++)n.push(o.create(e[i]));t(n)},n);s.exec("Contacts","search",[r,e,i],{cbid:r})};var d=t.contacts={getAddressBook:function(e,t,n){0===e&&1===e||(e=0);var i=s.callbackId(function(n){t&&(d.ADDRESSBOOK_PHONE==e?(o=o||new _(0),t(o)):(r=r||new _(1),t(o)))},function(e){n(new c(e))});s.exec("Contacts","getAddressBook",[i,e],{cbid:i})},ADDRESSBOOK_PHONE:0,ADDRESSBOOK_SIM:1}}),PlusObject.register("downloader",function(e,t,n,i){var o=t.bridge,r=t.tools,s={server:"Downloader",getValue:function(e,t){return void 0===e?t:e}};function a(e,t,n){this.type=s.getValue(e,""),this.handles=[],this.capture=s.getValue(n,!1),"function"==typeof t&&this.handles.push(t)}function c(e,t,n){var i=this;i.id=r.UUID("dt"),i.url=s.getValue(e,""),i.downloadedSize=0,i.totalSize=0,i.options=t||{},i.filename=s.getValue(i.options.filename,""),i.method=s.getValue(i.options.method,"GET"),i.timeout=s.getValue(i.options.timeout,120),i.retry=s.getValue(i.options.retry,3),i.retryInterval=s.getValue(i.options.retryInterval,30),i.priority=s.getValue(i.options.priority,1),i.onCompleted=n||null,i.eventHandlers={},i.data=s.getValue(i.options.data,null),i.__requestHeaders__={},i.__responseHeaders__={},i.__noParseResponseHeader__=null,i.__cacheReponseHeaders__={}}a.prototype.fire=function(e){for(var t=0;t<this.handles.length;++t)this.handles[t].apply(this,arguments)};var u=c.prototype;u.getFileName=function(){return this.filename},u.start=function(){o.exec(s.server,"start",[this.id,this.__requestHeaders__])},u.pause=function(){o.exec(s.server,"pause",[this.id])},u.resume=function(){o.exec(s.server,"resume",[this.id])},u.abort=function(){o.exec(s.server,"abort",[this.id])},u.getAllResponseHeaders=function(){if(this.__noParseResponseHeader__)return this.__noParseResponseHeader__;var e="";for(var t in this.__responseHeaders__)e=e+t+" : "+this.__responseHeaders__[t]+"\r\n";return this.__noParseResponseHeader__=e,this.__noParseResponseHeader__},u.getResponseHeader=function(e){if("string"==typeof e){var t=null;if(e=e.toLowerCase(),t=this.__cacheReponseHeaders__[e])return t;for(var n in this.__responseHeaders__){var i=this.__responseHeaders__[n];e===(n=n.toLowerCase())&&(t=t?t+", "+i:i)}return this.__cacheReponseHeaders__[e]=t,t}return""},u.setRequestHeader=function(e,t){if("string"==typeof e&&"string"==typeof t){var n=this.__requestHeaders__[e];this.__requestHeaders__[e]=n?n+", "+t:t}},u.addEventListener=function(e,t,n){if("string"==typeof e&&"function"==typeof t){var i=e.toLowerCase();void 0===this.eventHandlers[i]?this.eventHandlers[i]=new a(e,t,n):this.eventHandlers[i].handles.push(t)}},u.__handlerEvt__=function(e){var t=this;t.filename=s.getValue(e.filename,t.filename),t.state=s.getValue(e.state,t.state),t.downloadedSize=s.getValue(e.downloadedSize,t.downloadedSize),t.totalSize=s.getValue(e.totalSize,t.totalSize),t.__responseHeaders__=s.getValue(e.headers,{});var n=this.eventHandlers.statechanged;n&&n.fire(t,e.status||null),4==t.state&&"function"==typeof t.onCompleted&&t.onCompleted(t,e.status||null)};var l={__taskList__:[],createDownload:function(e,t,n){if("string"==typeof e){var i=new c(e,t,n);return l.__taskList__[i.id]=i,o.exec(s.server,"createDownload",[i]),i}return null},enumerate:function(e,t){var n=o.callbackId(function(t){for(var n=[],i=t.length,o=0,r=l.__taskList__;o<i;o++){var a=t[o];if(a&&a.uuid){var u=r[a.uuid];u||((u=new c).id=a.uuid,r[u.id]=u),u.state=s.getValue(a.state,u.state),u.options=s.getValue(a.options,u.options),u.filename=s.getValue(a.filename,u.filename),u.url=s.getValue(a.url,u.url),u.downloadedSize=s.getValue(a.downloadedSize,u.downloadedSize),u.totalSize=s.getValue(a.totalSize,u.totalSize),u.__responseHeaders__=s.getValue(t.headers,u.__responseHeaders__),n.push(u)}}"function"==typeof e&&e(n)});o.exec(s.server,"enumerate",[n,t])},clear:function(e){var t=-1e4;("number"==typeof e||e instanceof Number)&&(t=e),o.exec(s.server,"clear",[t])},startAll:function(){o.exec(s.server,"startAll",[0])},__handlerEvt__:function(e,t){var n=l.__taskList__[e];n&&(6==t.state&&delete l.__taskList__[e],n.__handlerEvt__(t))}};return l}),PlusObject.register("gallery",function(e,t,n,i){var o=t.bridge,r=0,s=1;function a(e,t){this.code=e||null,this.message=t||""}a.BUSY=1;var c={__galleryStatus:r,onPickImageFinished:null,pick:function(e,t,n){if(s!=c.__galleryStatus){c.__galleryStatus=s;var i=o.callbackId(function(t){if(c.__galleryStatus=r,"function"==typeof e)if(t&&t.multiple){var n={};n.files=t.files,e(n)}else e(t.files[0])},function(e){c.__galleryStatus=r,"function"==typeof t&&t(e)}),u=o.callbackId(function(){"function"==typeof n.onmaxed&&n.onmaxed()});o.exec("Gallery","pick",[i,n,u],{cbid:i})}else setTimeout(function(){"function"==typeof t&&t(new a(a.BUSY,"The album has been opened"))},0)},save:function(e,t,n){if("string"==typeof e){var i=o.callbackId(function(e){"function"==typeof t&&t(e)},function(e){"function"==typeof n&&n(e)});return o.exec("Gallery","save",[e,i],{cbid:i}),!0}return!1},compress:function(e,t,n){if("string"==typeof e){var i=o.callbackId(function(e){"function"==typeof t&&t(e)},function(e){"function"==typeof n&&n(e)});return o.exec("Gallery","compress",[e,i],{cbid:i}),!0}return!1}};return c}),PlusObject.register("geolocation",function(e,t){var n="Geolocation",i=(t=t).bridge,o=t.tools,r={};function s(e,t,n,i,o,r,s){this.latitude=e,this.longitude=t,this.accuracy=void 0!==i?i:null,this.altitude=void 0!==n?n:null,this.heading=void 0!==o?o:null,this.speed=void 0!==r?r:null,0!==this.speed&&null!==this.speed||(this.heading=NaN),this.altitudeAccuracy=void 0!==s?s:null}function a(e,t){e?(this.coordsType=e.coordsType,this.address=e.address,this.addresses=e.addresses,this.coords=new s(e.latitude,e.longitude,e.altitude,e.accuracy,e.heading,e.velocity,e.altitudeAccuracy)):this.coords=new s,this.timestamp=void 0!==t?t:(new Date).getTime()}function c(e,t){this.code=e||null,this.message=t||""}function u(e){var t={maximumAge:1e3,enableHighAccuracy:!1,timeout:1/0,geocode:!0};return e&&(void 0!==e.maximumAge&&!isNaN(e.maximumAge)&&e.maximumAge>0&&(t.maximumAge=e.maximumAge),void 0!==e.enableHighAccuracy&&(t.enableHighAccuracy=e.enableHighAccuracy),void 0===e.timeout||isNaN(e.timeout)||(e.timeout<0?t.timeout=0:t.timeout=e.timeout),e.coordsType&&(t.coordsType=e.coordsType),e.provider&&(t.provider=e.provider),void 0!==e.geocode&&(t.geocode=e.geocode)),t}function l(n,i){var o;return o=e.setTimeout?e.setTimeout(function(){_(o),o=null,n(new c(c.TIMEOUT,"Position retrieval timed out."))},i):t.setTimeout(function(){_(o),n(new c(c.TIMEOUT,"Position retrieval timed out."))},i)}function _(n){e.clearTimeout?!0!==n&&e.clearTimeout(n):!0!==n&&t.clearTimeout(n)}c.PERMISSION_DENIED=1,c.POSITION_UNAVAILABLE=2,c.TIMEOUT=3,c.UNKNOWN_ERROR=4;var f=t.geolocation={lastPosition:null,getCurrentPosition:function(e,t,n){d(e,t,n,!0)},watchPosition:function(s,p,h){h=u(h);var y=o.UUID("timer");r[y]=d(s,p,h,!1);var v=function(e){_(r[y].timer),r[y].isStop=!0;var t=new c(e.code,e.message);p&&p(t)},g=i.callbackId(function(e){if(!r[y].isStop){_(r[y].timer),h.timeout!==1/0&&(r[y].timer=l(v,h.timeout));var t=new a({latitude:e.latitude,longitude:e.longitude,altitude:e.altitude,accuracy:e.accuracy,heading:e.heading,velocity:e.velocity,coordsType:e.coordsType,address:e.address,addresses:e.addresses,altitudeAccuracy:e.altitudeAccuracy},void 0===e.timestamp?(new Date).getTime():e.timestamp instanceof Date?e.timestamp.getTime():e.timestamp);f.lastPosition=t,s(t)}},v);return i.exec(n,"watchPosition",[g,y,h.enableHighAccuracy,h.coordsType,h.provider,h.geocode,h.timeout,h.maximumAge],{cbid:g,l:e.getLocationHerf(t)}),y},clearWatch:function(e){e&&void 0!==r[e]&&(r[e].isStop=!0,_(r[e].timer),i.exec(n,"clearWatch",[e]))}};function d(o,r,s,d){s=u(s);var p={timer:null,isStop:!1},h=function(e){_(p.timer),p.timer=null;var t=new c(e.code,e.message);r&&r(t)};if(f.lastPosition&&s.maximumAge&&(new Date).getTime()-f.lastPosition.timestamp<=s.maximumAge)o(f.lastPosition);else if(0===s.timeout)h(new c(c.TIMEOUT,"timeout value in PositionOptions set to 0 and no cached Position object available, or cached Position object's age exceeds provided PositionOptions' maximumAge parameter."));else if(s.timeout!==1/0?p.timer=l(h,s.timeout):p.timer=!0,d){var y=i.callbackId(function(e){if(_(p.timer),p.timer){var t=new a({latitude:e.latitude,longitude:e.longitude,altitude:e.altitude,accuracy:e.accuracy,heading:e.heading,velocity:e.velocity,coordsType:e.coordsType,address:e.address,addresses:e.addresses,altitudeAccuracy:e.altitudeAccuracy},void 0===e.timestamp?(new Date).getTime():e.timestamp instanceof Date?e.timestamp.getTime():e.timestamp);f.lastPosition=t,o(t)}},h);i.exec(n,"getCurrentPosition",[y,s.enableHighAccuracy,s.maximumAge,s.coordsType,s.provider,s.geocode,s.timeout],{cbid:y,l:e.getLocationHerf(t)})}return p}return f}),PlusObject.register("io",function(e,t,n,i){var o=t.bridge,r=(t.tools,[]),s={NATIVEF:"File",exec:function(e,t,n,i){var r=o.callbackId(e,t);o.exec(s.NATIVEF,n,[r,i])}};function a(e){this.code=e.code||null,this.message=e.message||""}function c(e){var t="unknown error";switch(e){case a.NOT_FOUND_ERR:t="file not found";break;case a.SECURITY_ERR:t="not authorized";break;case a.ABORT_ERR:t="cancel";break;case a.NOT_READABLE_ERR:t="not allowed to read";break;case a.ENCODING_ERR:t="Coding error";break;case a.NO_MODIFICATION_ALLOWED_ERR:t="no modification allowed";break;case a.INVALID_STATE_ERR:t="invalid state";break;case a.SYNTAX_ERR:t="syntax error";break;case a.INVALID_MODIFICATION_ERR:t="invalid modification";break;case a.QUOTA_EXCEEDED_ERR:t="execution error";break;case a.TYPE_MISMATCH_ERR:t="type mismatch";break;case a.PATH_EXISTS_ERR:t="the path exists"}return{code:e,message:t}}function u(e,t){t=t||{},this.type=e,this.bubbles=!1,this.cancelBubble=!1,this.cancelable=!1,this.lengthComputable=!1,this.loaded=t.loaded||0,this.total=t.total||0,this.target=t.target||null}function l(e,t,n,i,o){this.size=o||0,this.type=n||null,this.name=e||"",this.lastModifiedDate=new Date(i)||null,this.fullPath=t||null}a.NOT_FOUND_ERR=1,a.SECURITY_ERR=2,a.ABORT_ERR=3,a.NOT_READABLE_ERR=4,a.ENCODING_ERR=5,a.NO_MODIFICATION_ALLOWED_ERR=6,a.INVALID_STATE_ERR=7,a.SYNTAX_ERR=8,a.INVALID_MODIFICATION_ERR=9,a.QUOTA_EXCEEDED_ERR=10,a.TYPE_MISMATCH_ERR=11,a.PATH_EXISTS_ERR=12;var _=l.prototype;function f(e,t,n,i,o,r){this.isFile=void 0!==e&&e,this.isDirectory=void 0!==t&&t,this.name=n||"",this.fullPath=i||"",this.fileSystem=o||null,this.__PURL__=r||"",this.__remoteURL__=r?"http://localhost:13131/"+r:""}_.slice=function(e,t,n){var i=this.size>0?this.size-1:0,o=0,r=i;if(arguments.length&&(o=e<0?Math.max(i+e,0):Math.min(i,e)),arguments.length>=2&&(r=t<0?Math.max(i+t,0):Math.min(t,i)),r<o)return null;var s=new l(this.name,this.fullPath,this.type,this.lastModifiedDate,r-o+1);return s.start=o,s.end=r,s},_.close=function(){};var d=f.prototype;function p(e,t,n,i){d.constructor.apply(this,[!0,!1,e,t,n,i])}function h(e,t,n,i){d.constructor.apply(this,[!1,!0,e,t,n,i])}d.getMetadata=function(e,t,n){var i="function"!=typeof e?null:function(t){var n=new function(e){this.modificationTime=void 0!==e?new Date(e):null,this.size=0,this.directoryCount=0,this.fileCount=0}(t.lastModifiedDate);n.size=t.size,n.directoryCount=t.directoryCount,n.fileCount=t.fileCount,e(n)},o="function"!=typeof t?null:function(e){t(new a(e))};s.exec(i,o,"getMetadata",[this.fullPath,n])},d.setMetadata=function(e,t,n){s.exec(e,t,"setMetadata",[this.fullPath,n])},d.moveTo=function(e,t,n,i){var o=this,r=function(e){"function"==typeof i&&i(new a(e))};if(e){var u=this.fullPath,l=t||this.name;s.exec(function(e){if(e){if("function"==typeof n){var t=e.isDirectory?new h(e.name,e.fullPath,o.fileSystem,e.remoteURL):new p(e.name,e.fullPath,o.fileSystem,e.remoteURL);try{n(t)}catch(e){}}}else r(c(a.NOT_FOUND_ERR))},r,"moveTo",[u,e.fullPath,l])}else r(c(a.NOT_FOUND_ERR))},d.copyTo=function(e,t,n,i){var o=this,r=function(e){"function"==typeof i&&i(new a(e))};if(e){var u=this.fullPath,l=t||this.name;s.exec(function(e){if(e){if("function"==typeof n){var t=e.isDirectory?new h(e.name,e.fullPath,e.fileSystem,o.remoteURL):new p(e.name,e.fullPath,o.fileSystem,e.remoteURL);try{n(t)}catch(e){}}}else r(c(a.NOT_FOUND_ERR))},r,"copyTo",[u,e.fullPath,l])}else r(c(a.NOT_FOUND_ERR))},d.remove=function(e,t){var n="function"!=typeof t?null:function(e){t(new a(e))};s.exec(e,n,"remove",[this.fullPath])},d.toURL=function(){return this.__PURL__},d.toLocalURL=function(){return"file://"+this.fullPath},d.toRemoteURL=function(){return this.__remoteURL__},d.getParent=function(e,t){var n=this,i="function"!=typeof e?null:function(t){var i=new h(t.name,t.fullPath,n.fileSystem,t.remoteURL);e(i)},o="function"!=typeof t?null:function(e){t(new a(e))};s.exec(i,o,"getParent",[this.fullPath])},p.prototype=new f,p.prototype.constructor=p,p.prototype.createWriter=function(e,t){this.file(function(n){var i=new m(n);null===i.fileName||""===i.fileName?"function"==typeof t&&t(new a(c(a.INVALID_STATE_ERR))):"function"==typeof e&&e(i)},t)},p.prototype.file=function(e,t){var n="function"!=typeof e?null:function(t){var n=new l(t.name,t.fullPath,t.type,t.lastModifiedDate,t.size);e(n)},i="function"!=typeof t?null:function(e){t(new a(e))};s.exec(n,i,"getFileMetadata",[this.fullPath])};var y=new f;function v(e,t){this.path=e||null,this.__fileSystem__=t||null}function g(){this.fileName="",this.readyState=0,this.result=null,this.error=null,this.onloadstart=null,this.onprogress=null,this.onload=null,this.onabort=null,this.onerror=null,this.onloadend=null}h.prototype=y,y.constructor=h,y.createReader=function(){return new v(this.fullPath,this.fileSystem)},y.getDirectory=function(e,t,n,i){var o=this,r="function"!=typeof n?null:function(e){var t=new h(e.name,e.fullPath,o.fileSystem,e.remoteURL);n(t)},c="function"!=typeof i?null:function(e){i(new a(e))};s.exec(r,c,"getDirectory",[this.fullPath,e,t])},y.removeRecursively=function(e,t){var n="function"!=typeof t?null:function(e){t(new a(e))};s.exec(e,n,"removeRecursively",[this.fullPath])},y.getFile=function(e,t,n,i){var o=this,r="function"!=typeof n?null:function(e){var t=new p(e.name,e.fullPath,o.fileSystem,e.remoteURL);n(t)},c="function"!=typeof i?null:function(e){i(new a(e))};s.exec(r,c,"getFile",[this.fullPath,e,t])},v.prototype.readEntries=function(e,t){var n=this,i="function"!=typeof e?null:function(t){for(var i=[],o=0;o<t.length;o++){var r=null;t[o].isDirectory?r=new h(t[o].name,t[o].fullPath,n.__fileSystem__,t[o].remoteURL):t[o].isFile&&(r=new p(t[o].name,t[o].fullPath,n.__fileSystem__,t[o].remoteURL)),i.push(r)}e(i)},o="function"!=typeof t?null:function(e){t(new a(e))};s.exec(i,o,"readEntries",[this.path])},g.EMPTY=0,g.LOADING=1,g.DONE=2;var b=g.prototype;function m(e){this.fileName="",this.readyState=0,this.result=null,this.length=0,e&&(this.fileName=e.fullPath||e,this.length=e.size||0),this.position=0,this.error=null,this.onwritestart=null,this.onprogress=null,this.onwrite=null,this.onabort=null,this.onsuccess=null,this.onerror=null,this.onwriteend=null}b.abort=function(){this.result=null,this.readyState!=g.DONE&&this.readyState!=g.EMPTY&&(this.readyState=g.DONE,"function"==typeof this.onabort&&this.onabort(new u("abort",{target:this})),"function"==typeof this.onloadend&&this.onloadend(new u("loadend",{target:this})))},b.readAsText=function(e,t){if(this.fileName="",void 0===e.fullPath?this.fileName=e:this.fileName=e.fullPath,this.readyState==g.LOADING)throw new a(a.INVALID_STATE_ERR);this.readyState=g.LOADING,"function"==typeof this.onloadstart&&this.onloadstart(new u("loadstart",{target:this}));var n=t||"UTF-8",i=this;s.exec(function(e){i.readyState!==g.DONE&&(i.result=e,"function"==typeof i.onload&&i.onload(new u("load",{target:i})),i.readyState=g.DONE,"function"==typeof i.onloadend&&i.onloadend(new u("loadend",{target:i})))},function(e){i.readyState!==g.DONE&&(i.readyState=g.DONE,i.result=null,i.error=new a(e),"function"==typeof i.onerror&&i.onerror(new u("error",{target:i})),"function"==typeof i.onloadend&&i.onloadend(new u("loadend",{target:i})))},"readAsText",[this.fileName,n,e.start,e.end])},b.readAsDataURL=function(e){if(this.fileName="",void 0===e.fullPath?this.fileName=e:this.fileName=e.fullPath,this.readyState==g.LOADING)throw new a(a.INVALID_STATE_ERR);this.readyState=g.LOADING,"function"==typeof this.onloadstart&&this.onloadstart(new u("loadstart",{target:this}));var t=this;s.exec(function(e){t.readyState!==g.DONE&&(t.readyState=g.DONE,t.result=e,"function"==typeof t.onload&&t.onload(new u("load",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new u("loadend",{target:t})))},function(e){t.readyState!==g.DONE&&(t.readyState=g.DONE,t.result=null,t.error=new a(e),"function"==typeof t.onerror&&t.onerror(new u("error",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new u("loadend",{target:t})))},"readAsDataURL",[this.fileName,e.start,e.end])},b.readAsBase64=function(e){if(this.fileName="",void 0===e.fullPath?this.fileName=e:this.fileName=e.fullPath,this.readyState==g.LOADING)throw new a(a.INVALID_STATE_ERR);this.readyState=g.LOADING,"function"==typeof this.onloadstart&&this.onloadstart(new u("loadstart",{target:this}));var t=this;s.exec(function(e){t.readyState!==g.DONE&&(t.readyState=g.DONE,t.result=e,"function"==typeof t.onload&&t.onload(new u("load",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new u("loadend",{target:t})))},function(e){t.readyState!==g.DONE&&(t.readyState=g.DONE,t.result=null,t.error=new a(e),"function"==typeof t.onerror&&t.onerror(new u("error",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new u("loadend",{target:t})))},"readAsBase64",[this.fileName,e.start,e.end])},b.readAsArrayBuffer=function(e){},m.INIT=0,m.WRITING=1,m.DONE=2;var I=m.prototype;function x(e,t){this.name=e||null,this.root=null,t&&(this.root=new h(t.name,t.fullPath,this,t.remoteURL))}function S(e,t,n){var i="function"!=typeof e.complete?function(){}:e.complete,r="function"!=typeof e.success?i:function(t){e.success(t),i(t)},a="function"!=typeof e.fail?i:function(t){e.fail(t),i(t)};callbackID=o.callbackId(r,a),o.exec(s.NATIVEF,t,[callbackID,n()])}return I.abort=function(){if(this.readyState===m.DONE||this.readyState===m.INIT)throw new a(c(a.INVALID_STATE_ERR));this.error=new a(c(a.ABORT_ERR)),this.readyState=m.DONE,"function"==typeof this.onabort&&this.onabort(new u("abort",{target:this})),"function"==typeof this.onwriteend&&this.onwriteend(new u("writeend",{target:this}))},I.write=function(e){var t=this;if("string"!=typeof e)throw new a(a.TYPE_MISMATCH_ERR);if(this.readyState===m.WRITING)throw new a(a.INVALID_STATE_ERR);this.readyState=m.WRITING,"function"==typeof t.onwritestart&&t.onwritestart(new u("writestart",{target:t})),s.exec(function(e){t.readyState!==m.DONE&&(t.position+=e,t.length+=e,t.readyState=m.DONE,"function"==typeof t.onwrite&&t.onwrite(new u("write",{target:t})),"function"==typeof t.onsuccess&&t.onsuccess(new u("success",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new u("writeend",{target:t})))},function(e){t.readyState!==m.DONE&&(t.readyState=m.DONE,t.error=new a(e),"function"==typeof t.onerror&&t.onerror(new u("error",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new u("writeend",{target:t})))},"write",[this.fileName,e,this.position])},I.writeAsBinary=function(e){var t=this;if("string"!=typeof e)throw new a(a.TYPE_MISMATCH_ERR);if(this.readyState===m.WRITING)throw new a(a.INVALID_STATE_ERR);this.readyState=m.WRITING,"function"==typeof t.onwritestart&&t.onwritestart(new u("writestart",{target:t})),s.exec(function(e){t.readyState!==m.DONE&&(t.position+=e,t.length+=e,t.readyState=m.DONE,"function"==typeof t.onwrite&&t.onwrite(new u("write",{target:t})),"function"==typeof t.onsuccess&&t.onsuccess(new u("success",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new u("writeend",{target:t})))},function(e){t.readyState!==m.DONE&&(t.readyState=m.DONE,t.error=new a(e),"function"==typeof t.onerror&&t.onerror(new u("error",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new u("writeend",{target:t})))},"writeAsBinary",[this.fileName,e,this.position])},I.seek=function(e){if(this.readyState===m.WRITING)throw new a(a.INVALID_STATE_ERR);(e||0===e)&&(e<0?this.position=Math.max(e+this.length,0):e>this.length?this.position=this.length:this.position=e)},I.truncate=function(e){if(this.readyState===m.WRITING)throw new a(a.INVALID_STATE_ERR);this.readyState=m.WRITING;var t=this;"function"==typeof t.onwritestart&&t.onwritestart(new u("writestart",{target:this})),s.exec(function(e){t.readyState!==m.DONE&&(t.readyState=m.DONE,t.length=e,t.position=Math.min(t.position,e),"function"==typeof t.onwrite&&t.onwrite(new u("write",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new u("writeend",{target:t})))},function(e){t.readyState!==m.DONE&&(t.readyState=m.DONE,t.error=new a(e),"function"==typeof t.onerror&&t.onerror(new u("error",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new u("writeend",{target:t})))},"truncate",[this.fileName,e,this.position])},{FileSystem:x,DirectoryEntry:h,DirectoryReader:v,FileReader:g,FileWriter:m,requestFileSystem:function(n,i,o){var u=function(e){"function"==typeof o&&o(new a(e))};if(n<1||n>4)u(c(a.SYNTAX_ERR));else{var l=r[n],_=function(e){e?"function"==typeof i&&(l||(l=new x(e.name,e.root),r[n]=l),i(l)):u(c(a.NOT_FOUND_ERR))};l?e.setTimeout?e.setTimeout(_(l),0):t.setTimeout(_(l),0):s.exec(_,u,"requestFileSystem",[n])}},resolveLocalFileSystemURL:function(e,t,n){var i=function(e){n&&n(new a(e))};"string"==typeof e?s.exec(function(e){var n;if(e){if(t){var o=r[e.type];o||(o=new x(e.fsName,e.fsRoot),r[e.type]=o),n=e.isDirectory?new h(e.name,e.fullPath,o,e.remoteURL):new p(e.name,e.fullPath,o,e.remoteURL),t(n)}}else i(c(a.NOT_FOUND_ERR))},i,"resolveLocalFileSystemURL",[e]):setTimeout(function(){i(c(a.ENCODING_ERR))},0)},convertLocalFileSystemURL:function(e){return o.execSync(s.NATIVEF,"convertLocalFileSystemURL",[e])},convertAbsoluteFileSystem:function(e){return o.execSync(s.NATIVEF,"convertAbsoluteFileSystem",[e])},getImageInfo:function(e){var t="function"!=typeof e.complete?function(){}:e.complete,n="function"!=typeof e.success?t:function(n){e.success(n),t(n)},i="function"!=typeof e.fail?t:function(n){e.fail(n),t(n)},r="string"!=typeof e.savePath?"":e.savePath;callbackID=o.callbackId(n,i),o.exec(s.NATIVEF,"getImageInfo",[callbackID,e.src,r])},getFileInfo:function(e){S(e,"getFileInfo",function(){return{filePath:e.filePath,digestAlgorithm:e.digestAlgorithm}})},getAudioInfo:function(e){S(e,"getAudioInfo",function(){return{filePath:e.filePath}})},getVideoInfo:function(e){S(e,"getVideoInfo",function(){return{filePath:e.filePath}})},chooseFile:function(e,t,n){var i="function"!=typeof t?null:function(e){t(e)},r="function"!=typeof n?null:function(e){n(e)};callbackID=o.callbackId(i,r),o.exec(s.NATIVEF,"chooseFile",[callbackID,e])},PRIVATE_WWW:1,PRIVATE_DOC:2,PUBLIC_DOCUMENTS:3,PUBLIC_DOWNLOADS:4}}),PlusObject.register("maps",function(e,t,n,i){var o=t.bridge,r=t.tools,s="Maps",a="createObject",c="updateObject",u="updateObjectSYNC",l="execMethod",_={callback:[],pushCallback:function(e,t,n){this.callback[e]={fun:t,nokeep:n}},execCallback:function(e,t){this.callback[e]&&(this.callback[e].fun&&this.callback[e].fun(e,t),this.callback[e].nokeep&&delete this.callback[e])}},f={};function d(e,t,n,i){return r.ANDROID==r.platform?o.exec(s,a,[r.stringify([e,t,n,i])],null):o.exec(s,a,[e,t,n,i],null)}function p(e,t,n){return r.ANDROID==r.platform?o.exec(s,c,[r.stringify([e,[t,n]])],null):o.exec(s,c,[e,[t,n]],null)}function h(e,t,n){return r.ANDROID==r.platform?o.exec(s,l,[r.stringify([e,[t,n]])],null):o.exec(s,l,[e,[t,n]],null)}function y(n,i,o,a,c){i=function(e){var t={zoom:12,type:"MAPTYPE_NORMAL",traffic:!1,zoomControls:!1};return e&&e.center instanceof I&&(t.center=e.center),e&&"number"==typeof e.zoom&&e.zoom<=22&&e.zoom>=1&&(t.zoom=e.zoom),!e||"MAPTYPE_NORMAL"!=e.type&&"MAPTYPE_SATELLITE"!=e.type||(t.type=e.type),e&&"boolean"==typeof e.traffic&&(t.traffic=e.traffic),e&&"boolean"==typeof e.zoomControls&&(t.zoomControls=e.zoomControls),e&&"string"==typeof e.position&&(t.position=e.position),e&&(t.top=e.top,t.left=e.left,t.width=e.width,t.height=e.height),t}(i);var u=this;if(this.IDENTITY=s,this._UUID_=c||r.UUID("map"),this._map_id_=o,this._ui_div_id_=n,this.__showUserLocationVisable__=!1,this.center=i.center?i.center:new t.maps.Point(116.39716,39.91669),this.zoom=i.zoom,this.userLocation=null,this.mapType=i.type,this.zoomControlsVisable=i.zoomControls,this.trafficVisable=i.traffic,this.visable=!0,this.onclick=function(e){},this.onstatuschanged=function(){},_.pushCallback(this._UUID_,function(e,t){if("click"==t.callbackType)u.onclick&&u.onclick(t.payload);else if("change"==t.callbackType&&(u&&(u.zoom=t.zoom),u.onstatuschanged)){var n={};n.target=u,n.zoom=t.zoom,n.center=new I(t.center.long,t.center.lat),t.northease&&(n.bounds=new m(new I(t.northease.long,t.northease.lat),new I(t.southwest.long,t.southwest.lat))),u.onstatuschanged(n)}}),!a){var l=e.document&&e.document.getElementById(this._ui_div_id_),h=[i];l&&(t.tools.platform==t.tools.ANDROID?document.addEventListener("plusorientationchange",function(){setTimeout(function(){var e=[l.offsetLeft,l.offsetTop,l.offsetWidth,l.offsetHeight];p(u._UUID_,"resize",e)},200)},!1):l.addEventListener("resize",function(){var e=[l.offsetLeft,l.offsetTop,l.offsetWidth,l.offsetHeight];p(u._UUID_,"resize",e)},!1),h=[i,l.offsetLeft,l.offsetTop,l.offsetWidth,l.offsetHeight]),f[this._UUID_]=d(this._UUID_,"mapview",h,this._map_id_)}}var v=y.prototype;function g(e){this._UUID_=r.UUID("Bubble"),this.label="string"==typeof e?e:"",this.icon=null,this.marker=null,this.__contentImage=null,this.__contentImageAsDataURL=null,this.onclick=function(e){}}v.close=function(){h("map","close",this._UUID_),f[this._UUID_]&&delete f[this._UUID_]},v.centerAndZoom=function(e,t){if(e instanceof I&&"number"==typeof t){this.center=e,this.zoom=t;var n=[e,t];p(this._UUID_,"centerAndZoom",n)}},v.setCenter=function(e){if(e instanceof I){this.center=e;var t=[e];p(this._UUID_,"setCenter",t)}},v.getCenter=function(){return this.center},v.setZoom=function(e){"number"==typeof e&&(this.zoom=e,p(this._UUID_,"setZoom",[e]))},v.resize=function(){var t=e.document&&e.document.getElementById(this._ui_div_id_),n=[null];t?(n=[t.offsetLeft,t.offsetTop,t.offsetWidth,t.offsetHeight],p(this._UUID_,"resize",n)):console.log("map The 'resize'method is not supported in custom component mode. Please use the 'setstyles' method")},v.getZoom=function(){return this.zoom},v.setMapType=function(e){"MAPTYPE_NORMAL"!=e&&"MAPTYPE_SATELLITE"!=e||(this.mapType=e,p(this._UUID_,"setMapType",[e]))},v.getMapType=function(){return this.mapType},v.showUserLocation=function(e){if("boolean"==typeof e&&this.__showUserLocationVisable__!=e){this.__showUserLocationVisable__=e;var t=[e];p(this._UUID_,"showUserLocation",t)}},v.isShowUserLocation=function(){return this.__showUserLocationVisable__},v.getUserLocation=function(e){if("function"==typeof e){var n=r.UUID("callback");_.pushCallback(n,function(t,n){e&&e(n.state,n.point)},!0);var i=[n,t.__HtMl_Id__];return p(this._UUID_,"getUserLocation",i),!0}return!1},v.getCurrentCenter=function(e){if("function"==typeof e){function n(t,n){e&&e(n.state,n.point)}var i=o.callbackId(n);_.pushCallback(i,n,!0);var r=[i,t.__HtMl_Id__];return p(this._UUID_,"getCurrentCenter",r),!0}return!1},v.setTraffic=function(e){if("boolean"==typeof e&&e!=this.trafficVisable){this.trafficVisable=e;var t=[e];p(this._UUID_,"setTraffic",t)}},v.isTraffic=function(){return this.trafficVisable},v.showZoomControls=function(e){if("boolean"==typeof e&&e!=this.zoomControlsVisable){this.zoomControlsVisable=e;var t=[e];p(this._UUID_,"showZoomControls",t)}},v.isShowZoomControls=function(){return this.zoomControlsVisable},v.getBounds=function(){var e,t,n,i=(e=this._UUID_,t="getBounds",n=[],r.ANDROID==r.platform?o.execSync(s,u,[r.stringify([e,[t,n]])],null):o.execSync(s,u,[e,[t,n]],null));return new m(new I(i.northease.longitude,i.northease.latitude),new I(i.southwest.longitude,i.southwest.latitude))},v.reset=function(){p(this._UUID_,"reset",[null])},v.show=function(){if(1!=this.visable){this.visable=!0;var t=e.document&&e.document.getElementById(this._ui_div_id_),n=[null];t&&(t.style.display="",n=[t.offsetLeft,t.offsetTop,t.offsetWidth,t.offsetHeight]),p(this._UUID_,"show",n)}},v.hide=function(){0!=this.visable&&(this.visable=!1,e.document&&e.document.getElementById(this._ui_div_id_)&&(document.getElementById(this._ui_div_id_).style.display="none"),p(this._UUID_,"hide",[null]))},v.addOverlay=function(e){if(e instanceof C||e instanceof A||e instanceof R||e instanceof P||e instanceof D){var t=[e._UUID_];return p(this._UUID_,"addOverlay",t),!0}return!1},v.removeOverlay=function(e){if(e instanceof C||e instanceof A||e instanceof R||e instanceof P||e instanceof D){var t=[e._UUID_];return p(this._UUID_,"removeOverlay",t),!0}return!1},v.clearOverlays=function(){p(this._UUID_,"clearOverlays",[null])},y.calculateDistance=function(e,t,n,i){var r=o.callbackId(function(e){"function"==typeof n&&n({distance:e})},function(e){"function"==typeof i&&i(e)});o.exec(s,"calculateDistance",[e,t,r])},y.calculateArea=function(e,t,n){var i=o.callbackId(function(e){"function"==typeof t&&t({area:e})},function(e){"function"==typeof n&&n(e)});o.exec(s,"calculateArea",[e,i])},y.convertCoordinates=function(e,t,n,i){var r=o.callbackId(function(e){if("function"==typeof n){var t={};t.coord=new I(e.long,e.lat),t.coordType=e.type,n(t)}},function(e){"function"==typeof i&&i(e)});o.exec(s,"convertCoordinates",[e,t,r])},y.geocode=function(e,t,n,i){var r=o.callbackId(function(e){if("function"==typeof n){var t={};t.coord=new I(e.long,e.lat),t.address=e.addr,t.coordType=e.type,n(t)}},function(e){"function"==typeof i&&i(e)});o.exec(s,"geocode",[e,t,r])},y.reverseGeocode=function(e,t,n,i){var r=o.callbackId(function(e){if("function"==typeof n){var t={};t.coord=new I(e.long,e.lat),t.address=e.addr,t.coordType=e.type,n(t)}},function(e){"function"==typeof i&&i(e)});o.exec(s,"reverseGeocode",[e,t,r])};var b=g.prototype;function m(e,t,n,i){"number"==typeof e&&"number"==typeof t&&"number"==typeof n&&"number"==typeof i?(this.northease=new I(e,t),this.northeast=new I(e,t),this.southwest=new I(n,i)):e instanceof I&&t instanceof I&&(this.northease=e,this.northeast=e,this.southwest=t)}function I(e,t){this.longitude=e,this.latitude=t}b.setIcon=function(e){"string"==typeof e&&(this.icon=e,this.marker&&p(this.marker._UUID_,"setBubbleIcon",[this.icon]))},b.loadImage=function(e){this.__contentImage=e,this.__contentImageAsDataURL=null,this.marker&&p(this.marker._UUID_,"loadImage",[e])},b.loadImageDataURL=function(e){this.__contentImage=null,this.__contentImageAsDataURL=e,this.marker&&p(this.marker._UUID_,"loadImageDataURL",[e])},b.getLabel=function(){return this.label},b.setLabel=function(e){"string"==typeof e&&(this.label=e,this.marker&&p(this.marker._UUID_,"setBubbleLabel",[this.label]))},b.belongMarker=function(){return this.marker},m.prototype.setNorthEase=function(e){e instanceof I&&(this.northease=e,this.northeast=e)},m.prototype.setNorthEast=function(e){e instanceof I&&(this.northease=e,this.northeast=e)},m.prototype.getNorthEase=function(){return this.northease},m.prototype.getNorthEast=function(){return this.northeast},m.prototype.setSouthWest=function(e){e instanceof I&&(this.southwest=e)},m.prototype.getSouthWest=function(){return this.southwest},m.prototype.contains=function(e){return e instanceof I&&(e.longitude<=this.northease.longitude&&e.longitude>=this.southwest.longitude&&e.latitude<=this.northease.latitude&&e.latitude>=this.southwest.latitude)},m.prototype.equals=function(e){return e instanceof m&&(this.northease.equals(e.northease)&&this.southwest.equals(e.southwest))},m.prototype.getCenter=function(){var e=(this.northease.longitude-this.southwest.longitude)/2,t=(this.northease.latitude-this.southwest.latitude)/2;return new I(e+this.southwest.longitude,t+this.southwest.latitude)};var x=I.prototype;function S(){this._UUID_=null,this.visable=!0}x.setLng=function(e){this.longitude=e},x.getLng=function(){return this.longitude},x.setLat=function(e){this.latitude=e},x.getLat=function(){return this.latitude},x.equals=function(e){return this.longitude==e.longitude&&this.latitude==e.latitude};var w=S.prototype;function D(e){var t=this;this._UUID_=r.UUID("marker"),this.point=e,this.icon="",this.caption="",this.bubble=null,this.canDraggable=!1,this.onclick=function(e){},this.onDrag=function(e){},_.pushCallback(this._UUID_,function(e,n){"bubbleclick"==n.type?t.bubble&&t.bubble.onclick&&t.bubble.onclick(t.bubble):"markerclick"==n.type?t.onclick&&t.onclick(t):"onDrag"==n.type&&(t.point=n.pt,t.onDrag(t))}),d(this._UUID_,"marker",[e])}w.show=function(){1!=this.visable&&(this.visable=!0,p(this._UUID_,"show",["true"]))},w.hide=function(){0!=this.visable&&(this.visable=!1,p(this._UUID_,"hide",["false"]))},w.isVisible=function(){return this.visable},w.bringToTop=function(){p(this._UUID_,"bringToTop",[])},D.prototype=new S;var k=D.prototype;function U(){this.strokeColor="#FFFFFF",this.strokeOpacity=1,this.fillColor="#FFFFFF",this.fillOpacity=1,this.lineWidth=5,this.visable=!0}k.constructor=D,k.setPoint=function(e){if(e instanceof I){this.point=e;var t=[e];p(this._UUID_,"setPoint",t)}},k.getPoint=function(){return this.point},k.setIcon=function(e){"string"==typeof e&&(this.icon=e,p(this._UUID_,"setIcon",[e]))},k.setLabel=function(e){"string"==typeof e&&(this.caption=e,p(this._UUID_,"setLabel",[e]))},k.getLabel=function(){return this.caption},k.setBubble=function(e,t){if(e instanceof g){var n=e.marker;if(n&&n!=this){n.bubble=null;var i=[null,null,null,null,!1];p(n._UUID_,"setBubble",i)}e.marker=this,this.bubble=e;i=[this.bubble.label,this.bubble.icon,this.bubble.__contentImageAsDataURL,this.bubble.__contentImage,t];p(this._UUID_,"setBubble",i)}else null==e&&p(this._UUID_,"setBubble",[null,null,null,null,t])},k.hideBubble=function(){this.bubble&&p(this._UUID_,"hideBubble",[])},k.getBubble=function(){return this.bubble},k.setDraggable=function(e){e!=this.canDraggable&&(this.canDraggable=!this.canDraggable,p(this._UUID_,"setDraggable",[this.canDraggable]))},k.isDraggable=function(){return this.canDraggable},k.setIcons=function(e,t){p(this._UUID_,"setIcons",[e,t])},U.prototype=new S;var T=U.prototype;function C(e,t){this.center=e,this.radius=t,this._UUID_=r.UUID("circle"),d(this._UUID_,"circle",[e,t])}T.constructor=U,T.setStrokeColor=function(e){"string"==typeof e&&(this.strokeColor=e,p(this._UUID_,"setStrokeColor",[e]))},T.getStrokeColor=function(){return this.strokeColor},T.setStrokeOpacity=function(e){"number"==typeof e&&(e<0?e=0:e>1&&(e=1),this.strokeOpacity=e,p(this._UUID_,"setStrokeOpacity",[e]))},T.getStrokeOpacity=function(){return this.strokeOpacity},T.setFillColor=function(e){"string"==typeof e&&(this.fillColor=e,p(this._UUID_,"setFillColor",[e]))},T.getFillColor=function(){return this.fillColor},T.setFillOpacity=function(e){"number"==typeof e&&(e<0?e=0:e>1&&(e=1),this.fillOpacity=e,p(this._UUID_,"setFillOpacity",[e]))},T.getFillOpacity=function(){return this.fillOpacity},T.setLineWidth=function(e){"number"==typeof e&&(e<0&&(e=0),this.lineWidth=e,p(this._UUID_,"setLineWidth",[e]))},T.getLineWidth=function(){return this.lineWidth},C.prototype=new U;var E=C.prototype;function A(e){this.path=e,this._UUID_=r.UUID("polygon"),d(this._UUID_,"polygon",[e])}E.constructor=C,E.setCenter=function(e){e instanceof I&&(this.center=e,p(this._UUID_,"setCenter",[e]))},E.getCenter=function(){return this.center},E.setRadius=function(e){"number"==typeof e&&e>=0&&(this.radius=e,p(this._UUID_,"setRadius",[e]))},E.getRadius=function(){return this.radius},A.prototype=new U;var N=A.prototype;function R(e){this.path=e,this._UUID_=r.UUID("polyline"),d(this._UUID_,"polyline",[e])}N.constructor=A,N.setPath=function(e){this.path=e,p(this._UUID_,"setPath",[e])},N.getPath=function(){return this.path},R.prototype=new U;var O=R.prototype;function P(e,t,n){this._UUID_=r.UUID("route"),this.startPoint=e,this.endPoint=t,this.pointCount=0,this.pointList=[],this.distance=0,this.routeTip="",void 0===n&&d(this._UUID_,"route",[e,t,n])}function B(){this.__state__=0,this.__type__=1,this.startPosition=null,this.endPosition=null,this.routeNumber=0,this.routeList=[]}function L(){this.__state__=0,this.__type__=0,this.totalNumber=0,this.currentNumber=0,this.pageNumber=0,this.pageIndex=0,this.poiList=[]}function V(e){var t=this;this._UUID_=r.UUID("search"),this.pageCapacity=10,this.map=e,this.onPoiSearchComplete=function(e,t){},this.onRouteSearchComplete=function(e,t){},_.pushCallback(this._UUID_,function(e,n){0==n.__type__?t.onPoiSearchComplete&&t.onPoiSearchComplete(n.__state__,n):1==n.__type__&&t.onRouteSearchComplete&&t.onRouteSearchComplete(n.__state__,n)}),d(this._UUID_,"search",[null])}O.constructor=R,O.setPath=function(e){this.path=e,p(this._UUID_,"setPath",[e])},O.getPath=function(){return this.path},P.prototype=new S,P.prototype.constructor=P,B.prototype.getRoute=function(e){return e>=0&&e<this.routeNumber?this.routeList[e]:null},L.prototype.getPosition=function(e){return e>=0&&e<this.currentNumber?this.poiList[e]:null};var M=V.prototype;function F(e,t,n,i){return new y(void 0,t,e,n,i)}M.setPageCapacity=function(e){this.pageCapacity=e;var t=[e];p(this._UUID_,"setPageCapacity",t)},M.getPageCapacity=function(){return this.pageCapacity},M.poiSearchInCity=function(e,t,n){if("string"==typeof e&&"string"==typeof t){var i=[e,t,n];return p(this._UUID_,"poiSearchInCity",i),!0}return!1},M.poiSearchNearBy=function(e,t,n,i){if("string"==typeof e&&t instanceof I&&"number"==typeof n){var o=[e,t,n,i];return p(this._UUID_,"poiSearchNearBy",o),!0}return!1},M.poiSearchInbounds=function(e,t,n,i){if("string"==typeof e&&t instanceof I&&n instanceof I){var o=[e,t,n,i];return p(this._UUID_,"poiSearchInbounds",o),!0}return!1},M.setTransitPolicy=function(e){var t=[e];p(this._UUID_,"setTransitPolicy",t)},M.transitSearch=function(e,t,n){if((e instanceof I||"string"==typeof e)&&(t instanceof I||"string"==typeof t)&&"string"==typeof n){var i=[e,t,n];return p(this._UUID_,"transitSearch",i),!0}return!1},M.setDrivingPolicy=function(e){var t=[e];p(this._UUID_,"setDrivingPolicy",t)},M.drivingSearch=function(e,t,n,i){if((e instanceof I||"string"==typeof e)&&(n instanceof I||"string"==typeof n)&&"string"==typeof t&&"string"==typeof i){var o=[e,t,n,i];return p(this._UUID_,"drivingSearch",o),!0}return!1},M.walkingSearch=function(e,t,n,i){if((e instanceof I||"string"==typeof e)&&(n instanceof I||"string"==typeof n)&&"string"==typeof t&&"string"==typeof i){var o=[e,t,n,i];return p(this._UUID_,"walkingSearch",o),!0}return!1},v.setStyles=function(e){r.ANDROID==r.platform?json_map=o.exec(s,"setStyles",[r.stringify([this._UUID_,e])],null):json_map=o.exec(s,"setStyles",[this._UUID_,e],null)},t.maps={Map:y,openSysMap:function(e,t,n){e instanceof I&&n instanceof I&&h("map","openSysMap",[e,t,n])},MapType:{MAPTYPE_SATELLITE:"MAPTYPE_SATELLITE",MAPTYPE_NORMAL:"MAPTYPE_NORMAL"},Marker:D,Bubble:g,Point:I,Bounds:m,Circle:C,Polygon:A,Polyline:R,Position:function(e){this.point=e,this.address="",this.city="",this.name="",this.phone="",this.postcode=""},Route:P,Search:V,SearchPolicy:{TRANSIT_TIME_FIRST:"TRANSIT_TIME_FIRST",TRANSIT_TRANSFER_FIRST:"TRANSIT_TRANSFER_FIRST",TRANSIT_WALK_FIRST:"TRANSIT_WALK_FIRST",TRANSIT_FEE_FIRST:"TRANSIT_FEE_FIRST",DRIVING_TIME_FIRST:"DRIVING_TIME_FIRST",DRIVING_NO_EXPRESSWAY:"DRIVING_NO_EXPRESSWAY",DRIVING_FEE_FIRST:"DRIVING_FEE_FIRST"},__SearchRouteResult__:B,__SearchPoiResult__:L,__bridge__:_,create:F,getMapById:function(e){var t;if(t=r.ANDROID==r.platform?o.execSync(s,"getMapById",[r.stringify([e])],null):o.execSync(s,"getMapById",[e],null)){var n=f[t.uuid];return n||(n=F(e,t.options,"no",t.uuid),f[t.uuid]=n),n}return null}}}),PlusObject.register("messaging",function(e,t,n,i){var o=t.bridge;function r(e){this.__hasPendingOperation__=!1,this.to=[],this.cc=[],this.bcc=[],this.subject="",this.body="",this.bodyType="text",this.silent=!1,this.attachment=[],this.type=e}r.prototype.addAttachment=function(e){"string"==typeof e&&this.attachment.push(e)},t.messaging={createMessage:function(e){return new r(e)},sendMessage:function(e,t,n){if(e instanceof r){var i="function"!=typeof t?null:function(){e.__hasPendingOperation__=!1,t()},s="function"!=typeof n?null:function(t){e.__hasPendingOperation__=!1,n(t)};if(e.__hasPendingOperation__)return void s({code:2,message:"sending"});e.__hasPendingOperation__=!0;var a=o.callbackId(i,s);o.exec("Messaging","sendMessage",[a,e],{cbid:a})}},listenMessage:function(e,t){var n="function"!=typeof e?null:function(t){e(t)},i="function"!=typeof t?null:function(e){t(e)},r=o.callbackId(n,i);o.exec("Messaging","listenMessage",[r],{cbid:r})},TYPE_SMS:1,TYPE_MMS:2,TYPE_EMAIL:3}}),PlusObject.register("orientation",function(e,t,n,i){var o=(t=t).bridge,r=t.tools,s="Orientation",a=!1,c={},u=[],l=null,_=function(e,t,n,i,o,r){this.alpha=e,this.beta=t,this.gamma=n,this.magneticHeading=i,this.trueHeading=o,this.headingAccuracy=r},f=void 0,d=void 0,p=void 0;function h(e){var t=u.slice(0);l=new _(e.alpha,e.beta,e.gamma,f,d,p);for(var n=0,i=t.length;n<i;n++)t[n].win(l)}function y(){var e=o.callbackId(function(e){f=e.magneticHeading,d=e.trueHeading,p=e.headingAccuracy,h(e)},function(e){h(e)});o.exec(s,"start",[e]),a=!0}function v(e,t){return{win:e,fail:t}}function g(e){var t=u.indexOf(e);t>-1&&(u.splice(t,1),0===u.length&&(o.exec(s,"stop",[]),a=!1))}var b={getCurrentOrientation:function(e,t){var n;n=v(function(t){g(n),e(t)},function(e){g(n),t&&t(e)}),u.push(n),a||y()},watchOrientation:function(n,i,o){var s=o&&o.frequency&&("number"==typeof o.frequency||o.frequency instanceof Number)?o.frequency:500,_=r.UUID("watch"),f=v(function(){},function(e){g(f),i&&i(e)});return u.push(f),c[_]={timer:(e.setInterval?e:t).setInterval(function(){l&&n(l)},s),listeners:f},a?l&&n(l):y(),_},clearWatch:function(n){n&&c[n]&&((e.clearInterval?e:t).clearInterval(c[n].timer),g(c[n].listeners),delete c[n])}};t.orientation=b}),PlusObject.register("payment",function(e,t,n,i){var o="Payment",r=t.bridge;function s(){function e(e){"object"==typeof e&&(e.errCode=e.code,e.errMsg=e.message||"")}this.id="",this.description="",this.serviceReady=!0,this.installService=function(){r.exec(o,"installService",[this.id])},this.appStoreReceipt=function(){return r.execSync(o,"appStoreReceipt",[this.id])},this.finishTransaction=function(t,n,i){if("appleiap"!==this.id){i({errorcode:"-3"})}var s="function"!=typeof n?null:function(e){n(e)},a="function"!=typeof i?null:function(t){e(t),i(t)},c=r.callbackId(s,a);r.exec(o,"finishTransactionRequest",[this.id,c,t])},this.restoreCompletedTransactions=function(e,t,n){this.restoreComplateRequest(e,t,n)},this.restoreComplateRequest=function(t,n,i){if("appleiap"!==this.id){i({errorcode:"-3"})}var s="function"!=typeof n?null:function(e){n(e)},a="function"!=typeof i?null:function(t){e(t),i(t)},c=r.callbackId(s,a);r.exec(o,"restoreComplateRequest",[this.id,c,t])},this.requestProduct=function(e,t,n){this.requestOrder(e,t,n)},this.requestOrder=function(t,n,i){if("appleiap"===this.id){var s="function"!=typeof n?null:function(e){n(e)},a="function"!=typeof i?null:function(t){e(t),i(t)},c=r.callbackId(s,a);r.exec(o,"requestOrder",[this.id,c,t])}else{i({errorcode:"-3"})}},this.isReadyToPay=function(e,n,i){if(t.tools.ANDROID==t.tools.platform){var s="function"!=typeof n?null:function(e){n(e)},a="function"!=typeof i?null:function(e){i(e)},c=r.callbackId(s,a);r.exec(o,"isReadyToPay",[this.id,e,c])}}}var a={Channel:s,getChannels:function(e,t){var n="function"!=typeof e?null:function(t){for(var n=[],i=t.length,o=0;o<i;o++){var r=new a.Channel;r.id=t[o].id,r.description=t[o].description,r.serviceReady=t[o].serviceReady,n[o]=r}e(n)},i="function"!=typeof t?null:function(e){t(e)},s=r.callbackId(n,i);r.exec(o,"getChannels",[s])},request:function(n,i,a,c){var u="function"!=typeof a?null:function(e){a(e)},l="function"!=typeof c?null:function(e){c(e)};if(n instanceof s){var _=r.callbackId(u,l);r.exec(o,"request",[n.id,i,_])}else(e.setTimeout?e:t).setTimeout(function(){l({code:62e3})},0)}};t.payment=a}),PlusObject.register("push",function(e,t,n,i){var o=t.bridge,r=t.tools;function s(){}s.prototype.setPushChannel=function(e){t.tools.ANDROID==t.tools.platform&&o.exec("Push","setPushChannel",[e])},s.prototype.getAllChannels=function(){return t.tools.ANDROID==t.tools.platform?o.execSync("Push","getAllChannels",[]):[]},t.push={__isUniPush__:i&&i.__isUniPush__,getClientInfo:function(){return this.__isUniPush__?l().getClientInfo():o.execSync2("Push","getClientInfo",[])},getClientInfoAsync:function(e,t){if(this.__isUniPush__)l().getClientInfoAsync(function(n){0==n.ret?"function"==typeof e&&e(n.payload):"function"==typeof t&&t(n.payload)});else{var n=o.callbackId(function(t){"function"==typeof e&&e(t)},function(e){"function"==typeof t&&t(e)});o.exec("Push","getClientInfoAsync",[n])}},createMessage:function(e,n,i){if(r.platform==r.IOS)this.__isUniPush__?l().createMessage({content:e,payload:n,options:i}):o.exec("Push","createMessage",[e,n,i]);else{if(i&&i.icon&&(i.icon=t.io.convertLocalFileSystemURL(i.icon)),i&&i.when){var s=i.when;"[object Date]"===Object.prototype.toString.call(s)?i.when=s.getTime():"string"==typeof s&&(isNaN(parseInt(s))||(i.when=parseInt(s)))}var a;e=new function(e,t,n){this.__UUID__=null,this.message=e,this.Payload=t,this.options=n}(e,n,i);a=this.__isUniPush__?l().createMessage(e):o.execSync("Push","createMessage",[e]),e.__UUID__=a}},clear:function(){this.__isUniPush__?l().clear():o.exec("Push","clear",[])},addEventListener:function(e,n,i){if(this.__isUniPush__)l().addPushEvent(t.__HtMl_Id__,e,n);else{var s=r.UUID(e);u.pushCallback_Push(s,n,i),o.exec("Push","addEventListener",[t.__HtMl_Id__,s,e])}},remove:function(e){this.__isUniPush__&&r.platform==r.ANDROID?l().remove(e.__UUID__):o.exec("Push","remove",[e.__UUID__])},getAllMessage:function(){return this.__isUniPush__&&r.platform==r.ANDROID?l().getAllMessage():o.execSync("Push","getAllMessage",[])},setAutoNotification:function(e){this.__isUniPush__&&r.platform==r.ANDROID?l().setAutoNotification(e):o.exec("Push","setAutoNotification",[e])},getChannelManager:function(){return new s}};var a,c,u=(a=[],{pushCallback_Push:function(e,t,n){a[e]={fun:t,nokeep:n}},execCallback_Push:function(e,t,n){a[e]&&a[e].fun&&a[e].fun(n)}});function l(){return c||((c=t.weex.requireModule("UniPush")).initialize(),r.platform==r.ANDROID&&t.webview.currentWebview().addEventListener("close",function(){c.removePushEvent(t.__HtMl_Id__)})),c}t.push.__Mkey__Push__=u}),PlusObject.register("runtime",function(e,t,n,i){var o=t.bridge;return{appid:null,arguments:(i=i||{}).arguments||null,version:i.version||null,innerVersion:i.innerVersion||null,uniVersion:i.uniVersion||null,launchLoadedTime:null,launcher:null,origin:null,processId:null,startupTime:null,restart:function(){o.exec("Runtime","restart",[])},install:function(e,t,n,i){var r=o.callbackId(n,i);o.exec("Runtime","install",[e,r,t])},getProperty:function(e,t){var n=o.callbackId(t);o.exec("Runtime","getProperty",[e,n])},quit:function(){o.exec("Runtime","quit",[])},openURL:function(e,t,n){var i=o.callbackId(null,function(e){"function"==typeof t&&t(e)});o.exec("Runtime","openURL",[e,i,n])},launchApplication:function(e,t){var n=o.callbackId(null,function(e){"function"==typeof t&&t(e)});o.exec("Runtime","launchApplication",[e,n])},setBadgeNumber:function(e,t){"number"==typeof e&&o.exec("Runtime","setBadgeNumber",[e,t])},openFile:function(e,t,n){var i=o.callbackId(null,function(e){"function"==typeof n&&n(e)});o.exec("Runtime","openFile",[e,t,i])},openDocument:function(e,t,n,i){var r=o.callbackId(function(e){"function"==typeof n&&n(e)},function(e){"function"==typeof i&&i(e)});o.exec("Runtime","openDocument",[e,t,r])},showPrivacyDialog:function(e){t.tools.ANDROID==t.tools.platform&&this.execCallback(e,"showPrivacyDialog")},execCallback:function(e,t){var n="function"!=typeof e.complete?function(){}:e.complete,i="function"!=typeof e.success?n:function(t){e.success(t),n(t)},r="function"!=typeof e.fail?n:function(t){e.fail(t),n(t)};callbackID=o.callbackId(i,r),o.exec("Runtime",t,[callbackID,e])},isStreamValid:function(){return o.execSync("Runtime","isStreamValid")},openWeb:function(e){return o.exec("Runtime","openWeb",[e])},isApplicationExist:function(e){return null!==e&&void 0!==e&&"string"!=typeof e&&o.execSync("Runtime","isApplicationExist",[e])},processDirectPage:function(){return o.execSync("Runtime","processDirectPage",[])},isCustomLaunchPath:function(){return o.execSync("Runtime","isCustomLaunchPath")},updateInfo:function(e){e&&(this.appid=e.appid,this.arguments=e.arguments,this.version=e.version,this.versionCode=e.versionCode,this.innerVersion=e.innerVersion,this.uniVersion=e.uniVersion,this.launchLoadedTime=e.launchLoadedTime,this.launcher=e.launcher,this.origin=e.origin,this.processId=e.processId,this.startupTime=e.startupTime,this.channel=e.channel)},getDCloudId:function(){return o.execSync("Runtime","getDCloudId")},agreePrivacy:function(){o.exec("Runtime","agreePrivacy",[])},disagreePrivacy:function(){o.exec("Runtime","disagreePrivacy",[])},isAgreePrivacy:function(){return o.execSync("Runtime","isAgreePrivacy",[])},downloadFile:function(e,t,n){if("string"==typeof e.url)if(e.url.length>10&&"blob:file:"==e.url.substring(0,10)){var i=new XMLHttpRequest;i.open("GET",e.url,!0),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.responseType="blob",i.onload=function(i){if(200==this.status){var r=this.response;try{const i=new FileReader;i.onload=function(i){var r=o.callbackId(t,n);o.exec("Runtime","downloadBlob",[i.target.result,e.fileName,r])},i.readAsDataURL(r),i.onerror=function(){n({code:-1,message:"blob error"})}}catch(e){n(s={code:-1,message:e})}}else{var s={code:-1,message:this.status};n(s)}},i.onerror=function(e){n({code:-1,message:"data error"})},i.send()}else{var r=document.createElement("a");r.href=e.url+"&DCLOUD_DOWNLOAD_BLOB:"+e.fileName,r.download=e.fileName,r.click()}else try{const i=new FileReader;i.onload=function(i){var r=o.callbackId(t,n);o.exec("Runtime","downloadBlob",[i.target.result,e.fileName,r])},i.readAsDataURL(e.url),i.onerror=function(){n({code:-1,message:"blob error"})}}catch(e){n({code:-1,message:e})}}}}),PlusObject.register("share",function(e,t,n,i){var o=t.bridge,r=t.tools,s="Share",a={};function c(e,t,n,i){this.id=e,this.description=t,this.authenticated=n,this.accessToken=i,this.nativeClient=!1}var u=c.prototype;function l(n,i){var a=this;a.__UUID__=r.UUID("Authorize"),a.__componentid__=n,a.display=i,a.onloaded=null,a.onauthenticated=null,a.onerror=null,a.__top__=0,a.__left__=0,a.__width__=0,a.__height__=0;var c=e.document&&e.document.getElementById(a.__componentid__);c&&(a.__left__=c.offsetLeft,a.__top__=c.offsetTop,a.__width__=c.offsetWidth,a.__height__=c.offsetHeight);var u=function(e){"function"==typeof a.onerror&&a.onerror(e)},l=o.callbackId(function(e){"load"==e.evt?"function"==typeof a.onloaded&&a.onloaded():"auth"==e.evt&&"function"==typeof a.onauthenticated&&t.share.getServices(function(t){for(var n=0;n<t.length;n++){var i=t[n];if(i.id==e.type){i.authenticated=e.authenticated,i.accessToken=e.accessToken,a.onauthenticated(i);break}}},function(e){u(e)})},u);o.exec(s,"create",[a.__UUID__,l,a.display,a.__left__,a.__top__,a.__width__,a.__height__])}u.authorize=function(e,t,n){var i=this,r="function"!=typeof e?null:function(t){i.authenticated=t.authenticated,i.accessToken=t.accessToken,e(i)},a="function"!=typeof t?null:function(e){t(e)},c=o.callbackId(r,a);o.exec(s,"authorize",[c,this.id,n])},u.forbid=function(){this.authenticated=!1,this.accessToken=null,o.exec(s,"forbid",[this.id])},u.send=function(e,t,n){var i="function"!=typeof t?null:function(e){t()},r="function"!=typeof n?null:function(e){n(e)},a=o.callbackId(i,r);o.exec(s,"send",[a,this.id,e])},u.launchMiniProgram=function(e,t,n){var i="function"!=typeof t?null:function(e){t(e)},r="function"!=typeof n?null:function(e){n(e)},a=o.callbackId(i,r);o.exec(s,"launchMiniProgram",[a,this.id,e])},u.openCustomerServiceChat=function(e,t,n){var i="function"!=typeof t?null:function(e){t(e)},r="function"!=typeof n?null:function(e){n(e)},a=o.callbackId(i,r);o.exec(s,"openCustomerServiceChat",[a,this.id,e])},l.prototype.load=function(e){this.id=e,o.exec(s,"load",[this.__UUID__,e])},l.prototype.setVisible=function(e){o.exec(s,"setVisible",[this.__UUID__,e])};var _={Authorize:l,getServices:function(e,t){var n="function"!=typeof e?null:function(t){for(var n=[],i=0;i<t.length;i++){var o=t[i];if(o.id){var r=a[o.id];r||(r=new c),r.id=o.id,r.description=o.description,r.authenticated=o.authenticated,r.accessToken=o.accessToken,r.nativeClient=o.nativeClient,a[o.id]=r,n.push(r)}}e(n)},i="function"!=typeof t?null:function(e){t(e)},r=o.callbackId(n,i);o.exec(s,"getServices",[r])},sendWithSystem:function(e,t,n){var i="function"!=typeof t?null:function(e){t()},r="function"!=typeof n?null:function(e){n(e)},a=o.callbackId(i,r);o.exec(s,"sendWithSystem",[a,e])}};t.share=_}),PlusObject.register("speech",function(e,t,n,i){var o=t.bridge,r={startRecognize:function(e,t,n){var i="function"!=typeof t?null:function(e){t(e)},r="function"!=typeof n?null:function(e){n(e)},s=o.callbackId(i,r),a={};if(e.onstart){var c="function"!=typeof e.onstart?null:function(){e.onstart()};a.onstart=o.callbackId(c)}if(e.onend){var u="function"!=typeof e.onend?null:function(){e.onend()};a.onend=o.callbackId(u)}o.exec("Speech","startRecognize",[s,e,a])},stopRecognize:function(){o.exec("Speech","stopRecognize",[])},addEventListener:function(e,n,i){var r;if(n){var s=function(e){"function"==typeof n&&n(e)};n.listener=s,r=o.callbackId(s)}o.exec("Speech","addEventListener",[e,r,t.__HtMl_Id__])}};t.speech=r}),PlusObject.register("statistic",function(e,t,n,i){var o=t.bridge,r="Statistic";t.statistic={eventTrig:function(e,t){o.exec(r,"eventTrig",[e,t])},eventStart:function(e,t){o.exec(r,"eventStart",[e,t])},eventEnd:function(e,t){o.exec(r,"eventEnd",[e,t])},eventDuration:function(e,t,n){o.exec(r,"eventDuration",[e,t,n])}}}),PlusObject.register("storage",function(e,t){var n=t.bridge;return{getLength:function(){return n.execSync2("Storage","getLength",[null])},getAllKeys:function(){return n.execSync2("Storage","getAllKeys",[null])},getAllKeysAsync:function(e,t){if(e&&"function"==typeof e){var i="function"!=typeof t?null:function(e){t(e)},o=[n.callbackId(e,i)];n.exec("Storage","getAllKeysAsync",o)}},getItem:function(e){return"string"==typeof e&&n.execSync2("Storage","getItem",[e],function(e){var t=e.indexOf(":");return"null"===e.substr(0,t)?null:e.substr(t+1)})},getItemAsync:function(e,t,i){if("string"==typeof e&&t&&"function"==typeof t){var o="function"!=typeof i?null:function(e){i(e)},r=[n.callbackId(t,o),e];n.exec("Storage","getItemAsync",r)}},setItem:function(e,t){return"string"==typeof e&&"string"==typeof t&&n.execSync2("Storage","setItem",[e,t])},setItemAsync:function(e,t,i,o){if("string"==typeof e&&"string"==typeof t&&i&&"function"==typeof i){var r="function"!=typeof o?null:function(e){o(e)},s=[n.callbackId(i,r),e,t];n.exec("Storage","setItemAsync",s)}},removeItem:function(e){return"string"==typeof e&&n.execSync2("Storage","removeItem",[e])},removeItemAsync:function(e,t,i){if("string"==typeof e&&t&&"function"==typeof t){var o="function"!=typeof i?null:function(e){i(e)},r=[n.callbackId(t,o),e];n.exec("Storage","removeItemAsync",r)}},clear:function(){return n.execSync2("Storage","clear",[null])},clearAsync:function(e,t){if(e&&"function"==typeof e){var i="function"!=typeof t?null:function(e){t(e)},o=[n.callbackId(e,i)];n.exec("Storage","clearAsync",o)}},key:function(e){return"number"==typeof e&&n.execSync2("Storage","key",[e])}}}),PlusObject.register("uploader",function(e,t,n,i){var o=t.bridge,r=t.tools,s={UUID:function(){return r.UUID("uploader")},server:"Uploader",getValue:function(e,t){return void 0===e?t:e}};function a(e,t,n){this.type=s.getValue(e,""),this.handles=[],this.capture=s.getValue(n,!1),"function"==typeof t&&this.handles.push(t)}function c(e,t,n){this.__UUID__=s.UUID(),this.url=s.getValue(e,""),this.options=t||{},this.uploadedSize=0,this.totalSize=0,this.responseText="",this.method=s.getValue(this.options.method,"GET"),this.chunkSize=s.getValue(this.options.chunkSize,0),this.timeout=s.getValue(this.options.timeout,120),this.retry=s.getValue(this.options.retry,3),this.retryInterval=s.getValue(this.options.retryInterval,30),this.priority=s.getValue(this.options.priority,1),this.onCompleted=n||null,this.eventHandlers={},this.__requestHeaders__={},this.__responseHeaders__={},this.__noParseResponseHeader__=null,this.__cacheReponseHeaders__={}}return a.prototype.fire=function(e){for(var t=0;t<this.handles.length;++t)this.handles[t].apply(this,arguments)},c.prototype.addFile=function(e,t){return"string"==typeof e&&"object"==typeof t&&(o.exec(s.server,"addFile",[this.__UUID__,e,t]),!0)},c.prototype.addData=function(e,t){return"string"==typeof e&&"string"==typeof t&&(o.exec(s.server,"addData",[this.__UUID__,e,t]),!0)},c.prototype.getAllResponseHeaders=function(){if(this.__noParseResponseHeader__)return this.__noParseResponseHeader__;var e="";for(var t in this.__responseHeaders__)e=e+t+" : "+this.__responseHeaders__[t]+"\r\n";return this.__noParseResponseHeader__=e,this.__noParseResponseHeader__},c.prototype.getResponseHeader=function(e){if("string"==typeof e){var t=null;if(e=e.toLowerCase(),t=this.__cacheReponseHeaders__[e])return t;for(var n in this.__responseHeaders__){var i=this.__responseHeaders__[n];e===(n=n.toLowerCase())&&(t=t?t+", "+i:i)}return this.__cacheReponseHeaders__[e]=t,t}return""},c.prototype.setRequestHeader=function(e,t){if("string"==typeof e&&"string"==typeof t){var n=this.__requestHeaders__[e];this.__requestHeaders__[e]=n?n+", "+t:t}},c.prototype.start=function(){o.exec(s.server,"start",[this.__UUID__,this.__requestHeaders__])},c.prototype.pause=function(){o.exec(s.server,"pause",[this.__UUID__])},c.prototype.resume=function(){o.exec(s.server,"resume",[this.__UUID__])},c.prototype.abort=function(){o.exec(s.server,"abort",[this.__UUID__])},c.prototype.addEventListener=function(e,t,n){if("string"==typeof e&&"function"==typeof t){var i=e.toLowerCase();void 0===this.eventHandlers[i]?this.eventHandlers[i]=new a(e,t,n):this.eventHandlers[i].handles.push(t)}},c.prototype.__handlerEvt__=function(e){var t=this;t.state=s.getValue(e.state,t.state),t.uploadedSize=s.getValue(e.uploadedSize,t.uploadedSize),t.totalSize=s.getValue(e.totalSize,t.totalSize),t.__responseHeaders__=s.getValue(e.headers,{}),4==t.state&&"function"==typeof t.onCompleted&&(t.responseText=s.getValue(e.responseText,t.responseText),t.onCompleted(t,e.status||null));var n=this.eventHandlers.statechanged;n&&n.fire(t,void 0===e.status?null:e.status)},{__taskList__:{},createUpload:function(e,t,n){if("string"==typeof e){var i=new c(e,t,n);return this.__taskList__[i.__UUID__]=i,o.exec(s.server,"createUpload",[i]),i}return null},enumerate:function(e,t){var n=this.__taskList__,i=o.callbackId(function(t){for(var i=0;i<t.length;i++){var o=t[i];if(o&&o.uuid){var r=n[o.uuid];r||((r=new c).__UUID__=o.uuid,n[r.__UUID__]=r),r.state=s.getValue(o.state,r.state),r.options=s.getValue(o.options,r.options),r.url=s.getValue(o.url,r.url),r.uploadedSize=s.getValue(o.uploadedSize,r.uploadedSize),r.totalSize=s.getValue(o.totalSize,r.totalSize),r.responseText=s.getValue(o.responseText,r.responseText),r.__responseHeaders__=s.getValue(t.headers,r.__responseHeaders__)}}var a=[];for(var u in n)a.push(n[u]);"function"==typeof e&&e(a)});o.exec(s.server,"enumerate",[i])},clear:function(e){var t=4;"number"==typeof e&&(t=e),o.exec(s.server,"clear",[t])},startAll:function(){o.exec(s.server,"startAll",[0])},__handlerEvt__:function(e,t){var n=this.__taskList__[e];n&&n.__handlerEvt__(t)}}}),PlusObject.register("net",function(e,t,n,i){var o=t.bridge,r=t.tools;function s(){}function a(){this.__init__(),this.__UUID__=r.UUID("xhr")}s.Timeout=0,s.Other=1,a.Uninitialized=0,a.Open=1,a.Sent=2,a.Receiving=3,a.Loaded=4,a.__F__="XMLHttpRequest";var c=a.prototype;return c.__init__=function(){this.readyState=a.Uninitialized,this.responseText="",this.responseXML=null,this.status=a.Uninitialized,this.statusText=null,this.onreadystatechange,this.responseType=null,this.response=null,this.withCredentials=!0,this.timeout=12e4,this.__noParseResponseHeader__=null,this.__requestHeaders__={},this.__responseHeaders__={},this.__cacheReponseHeaders__={},this.__progessEvent__=new function(e,t,n,i){this.target=e,this.lengthComputable=t,this.loaded=n,this.total=i}(this,!1,0,0),Object.defineProperty(this.__progessEvent__,"target",{enumerable:!1})},c.abort=function(){this.readyState>a.Uninitialized&&("function"==typeof this.onabort&&this.onabort(this.__progessEvent__),this.__init__(),o.exec(a.__F__,"abort",[this.__UUID__]))},c.getAllResponseHeaders=function(){if(this.readyState>=a.Receiving){if(this.__noParseResponseHeader__)return this.__noParseResponseHeader__;var e="";for(var t in this.__responseHeaders__)e=e+t+": "+this.__responseHeaders__[t]+"\r\n";return this.__noParseResponseHeader__=e,this.__noParseResponseHeader__}return null},c.getResponseHeader=function(e){if("string"==typeof e&&this.readyState>=a.Receiving){var t=null;if(e=e.toLowerCase(),t=this.__cacheReponseHeaders__[e])return t;for(var n in this.__responseHeaders__){var i=this.__responseHeaders__[n];e===(n=n.toLowerCase())&&(t=t?t+", "+i:i)}return this.__cacheReponseHeaders__[e]=t,t}return null},c.setRequestHeader=function(e,t){if("string"==typeof e&&"string"==typeof t&&a.Open==this.readyState){var n=this.__requestHeaders__[e];this.__requestHeaders__[e]=n?n+", "+t:t}},c.open=function(e,t,n,i){a.Open!=this.readyState&&a.Loaded!=this.readyState||this.__init__(),a.Uninitialized==this.readyState&&(this.readyState=a.Open,o.exec(a.__F__,"open",[this.__UUID__,e,function(e){return"string"==typeof e?0==(e=e.replace(/(^\s*)|(\s*$)/g,"")).indexOf("http://")||0==e.indexOf("https://")?e:e=0==e.indexOf("/")?location.origin+e:location.origin+location.pathname+e:""}(t),n,i,this.timeout]),"function"==typeof this.onreadystatechange&&this.onreadystatechange())},c.overrideMimeType=function(e){o.exec(a.__F__,"overrideMimeType",[this.__UUID__,e])},c.send=function(e){var t=this;if(a.Open==this.readyState){this.readyState=a.Sent,"function"==typeof this.onloadstart&&this.onloadstart(t.__progessEvent__);var n=o.callbackId(function(e){if(a.Receiving==e.readyState)a.Sent==t.readyState?(t.readyState=a.Receiving,t.status=e.status,t.statusText=e.statusText,t.__responseHeaders__=e.header,t.__progessEvent__.lengthComputable=e.lengthComputable,t.__progessEvent__.total=e.totalSize):a.Receiving==t.readyState&&(t.responseText=e.responseText,t.__progessEvent__.loaded=e.revSize),"function"==typeof t.onreadystatechange&&t.onreadystatechange(),"function"==typeof t.onprogress&&t.onprogress(t.__progessEvent__);else if(a.Loaded==e.readyState){t.readyState=a.Loaded,e.status&&(t.status=e.status);try{if(t.responseText){var n=new DOMParser;t.responseXML=n.parseFromString(t.responseText,"text/xml")}}catch(e){t.responseXML=null}try{if("document"==t.responseType){n=new DOMParser;t.response=t.responseXML}else"json"==t.responseType&&(t.response=JSON.parse(t.responseText))}catch(e){t.response=null}"function"==typeof t.onreadystatechange&&t.onreadystatechange(),e.error==s.Timeout?"function"==typeof t.ontimeout&&t.ontimeout(t.__progessEvent__):e.error==s.Other?"function"==typeof t.onerror&&t.onerror(t.__progessEvent__):"function"==typeof t.onload&&t.onload(t.__progessEvent__),"function"==typeof t.onloadend&&t.onloadend(t.__progessEvent__)}});return o.exec(a.__F__,"send",[this.__UUID__,n,e,this.__requestHeaders__]),void("function"==typeof this.onreadystatechange&&this.onreadystatechange())}throw new Error("XMLHttpRequest not open")},{XMLHttpRequest:a}}),PlusObject.register("zip",function(e,t,n,i){var o=t.bridge;return{compressVideo:function(e,t,n){var i=o.callbackId(function(e){if(t){var n={tempFilePath:e.tempFilePath,size:e.size};t(n)}},n);o.exec("Zip","compressVideo",[e,i])},decompress:function(e,t,n,i){var r=o.callbackId(n,i);o.exec("Zip","decompress",[e,t,r])},compress:function(e,t,n,i){var r=o.callbackId(n,i);o.exec("Zip","compress",[e,t,r])},compressImage:function(e,t,n){var i=o.callbackId(function(e){if(t){var n={target:e.path,width:e.w,height:e.h,size:e.size};t(n)}},n);o.exec("Zip","compressImage",[e,i])}}}),PlusObject.register("proximity",function(e,t){var n=t.bridge,i=t.tools,o="Proximity",r=!1,s={},a=[];function c(e){var t=a.indexOf(e);t>-1&&(a.splice(t,1),0===a.length&&(n.exec(o,"stop",[]),r=!1))}var u={getCurrentProximity:function(e,t){var r=n.callbackId(function(t){i.IOS==i.platform&&(t=0==t?0:1/0),e&&e(t)},function(e){t&&t(e)});n.exec(o,"getCurrentProximity",[r])},watchProximity:function(e,t){var u,l=i.UUID("watch"),_={win:function(t){i.IOS==i.platform&&(t=0==t?0:1/0),e&&e(t)},fail:function(e){c(_),t&&t(e)}};return a.push(_),s[l]={listeners:_},r||(u=n.callbackId(function(e){for(var t=a.slice(0),n=0,i=t.length;n<i;n++)t[n].win(e)},function(e){for(var t=a.slice(0),n=0,i=t.length;n<i;n++)t[n].fail(e)}),n.exec(o,"start",[u]),r=!0),l},clearWatch:function(e){e&&s[e]&&(c(s[e].listeners),delete s[e])}};t.proximity=u}),PlusObject.register("ios",function(plusContext,plus){var bridge=plus.bridge,tools=plus.tools,_Server="Invocation",_importHash=[],_frameObjHash={},_currentFrameObj=null,iosTools={},classTemplate={};iosTools.undefObjectHash={};var jsbDef="";function JNBType(e,t){this.type=e,this.value=t}function JSBBaseObject(){this.__TYPE__="JSBObject",this.__UUID__=tools.UUID("JSBaseObject")}function JSImplements(e,t){e=__compatibilityNamespace(e);var n=this;this.__UUID__=tools.UUID("JSImplements"),this.callback=t,this.callbackId=bridge.callbackId(function(e){for(var t=e.arguments,i=[],o=0;o<t.length;o++)i.push(plus.ios.__Tool.New(t[o],!0));n.callback[e.method].apply(n,i)},null);var i=[];for(var o in t)i.push(o);var r=bridge.execSync2(_Server,"implements",[this.__UUID__,e,i,this.callbackId],null,!0);return plus.ios.__Tool.New(r,!0)}function JSBridge(){this.__Tool=iosTools,this.__JSBBaseObject=JSBBaseObject}plus.tools.IOS==plus.tools.platform?jsbDef+="plus.ios.":plus.tools.ANDROID==plus.tools.platform&&(jsbDef+="plus.android."),iosTools.process=function(e){for(var t=[],n=0;n<e.length;n++)t.push(this.warp(e[n]));return t},iosTools.attach=function(e,t){var n=this.undefObjectHash[e];if(n&&t){for(var i=0;i<n.length;i++)n[i].__proto__=t.prototype;delete this.undefObjectHash.className}},iosTools.New=function(e,t){var n=null;if(e){if("object"==e.type){var i=classTemplate.isImport(e.className);if(i)n=new i(t);else{if(e.superClassNames)for(var o=0;o<e.superClassNames.length&&!(i=classTemplate.isImport(e.superClassNames[o]));o++);if(i)n=new i(t);else{n=new JSBBaseObject;var r=this.undefObjectHash[e.className];r||(this.undefObjectHash[e.className]=r=[]),r.push(n)}}return n.className=e.className,n.__UUID__=e.value,n}if("struct"==e.type)return new JNBType(e.type,e.value);if(Array.isArray(e.value))for(o=0;o<e.value.length;o++)e.value[o]=iosTools.New(e.value[o],t);return e.value}return null},iosTools.handleClassName=function(e){return e.replace("$",".")},iosTools.saveContent=function(e,t){bridge.execSync2(_Server,"__saveContent",[e,t],null,!0)},iosTools.warp=function(e){var t={};if(e&&"JSBObject"==e.__TYPE__)t.type="object",t.value=e.__UUID__;else if(e instanceof JNBType){if(t.type=e.type,t.value=e.value,0==e.type.indexOf("@block")){t.type=e.type;var n=bridge.callbackId(function(t){e.value;for(var n=[],i=0;i<t.length;i++)n.push(plus.ios.__Tool.New(t[i],!0));e.value.apply(this,n)});t.value=n,alert(t.value)}}else if(void 0===e||null==e)t.type="null",t.value=e;else if("string"==typeof e||"String"==typeof e)t.type="string",t.value=e;else if("number"==typeof e)t.type="number",t.value=e;else if("boolean"==typeof e)t.type="boolean",t.value=e;else if("function"==typeof e){t.type="block";n=bridge.callbackId(function(t){for(var n=[],i=0;i<t.length;i++)n.push(plus.ios.__Tool.New(t[i],!0));e.apply(this,n)});t.value=n}else plus.tools.IOS==plus.tools.platform&&"object"==typeof e?(t.type="jsonObject",t.value=e):t=e;return t},JNBType.prototype.constructor=JNBType,JSBBaseObject.prototype.plusSetAttribute=function(){var e=null;try{for(var t=[],n=1;n<arguments.length;n++)t.push(iosTools.warp(arguments[n]));e=plus.bridge.execSync2(_Server,"__plusSetAttribute",[this.__UUID__,arguments[0],t],null,!0),e=iosTools.New(e,!0)}catch(e){throw e}return e},JSBBaseObject.prototype.plusGetAttribute=function(e){var t=null;try{t=plus.bridge.execSync2(_Server,"__plusGetAttribute",[this.__UUID__,e],null,!0),t=plus.ios.__Tool.New(t,!0)}catch(e){throw e}return t},JSBBaseObject.prototype.importClass=function(){return plus.android.importClass(this)},JSBBaseObject.prototype.plusCallMethod=function(e){var t=null;try{var n="",i=[],o=0;for(var r in e){if("string"!=typeof r)return;var s=e[r];if(0==o){if(n=r,void 0===s){o++;break}n+=":"}else n+=r+":";i.push(s),o++}if(0==o)return;i=plus.ios.__Tool.process(i);t=plus.bridge.execSync2(_Server,"__exec",[this.__UUID__,n,i],null,!0),t=plus.ios.__Tool.New(t,!0)}catch(e){throw e}return t},JSImplements.prototype=new JSBBaseObject,JSImplements.prototype.constructor=JSImplements,classTemplate.hashTable={},classTemplate.importClass=function(e,t){var n=this.isImport(e);if(n)return n;for(var i=this.newClassDefine(e,t),o=bridge.execSync2(_Server,"import",[e],null,!0),r=o.ClassMethod,s=0;s<r.length;s++)i+=this.AddMethodToClass(e,r[s],!0);var a=o.InstanceMethod;for(s=0;s<a.length;s++)i+=this.AddMethodToClass(e,a[s]);if(plus.tools.ANDROID==plus.tools.platform){var c=o.ClassConstKeys,u=o.ClassConstValues;for(s=0;s<c.length;s++)i+=this.AddStaticConstToClass(e,c[s],u[s])}return this.hashTable[e]=n=classTemplate.createClass(e,i),iosTools.attach(e,n),n},classTemplate.isImport=function(e){return classTemplate.hashTable[e]},classTemplate.newClassDefine=function(e,t){var n,i,o="",r=e;e=iosTools.handleClassName(e),t&&(t=iosTools.handleClassName(t)),i=jsbDef+t;for(var s=(n=jsbDef+e).split("."),a="plus",c=1;c<s.length-1;c++)o+="if(!"+(a=a+"."+s[c])+")",o+=a+"={};";return o+=a+"."+s[s.length-1]+"=",o+="function(nocreate) {            this.__UUID__ = plus.tools.UUID('JSB');            this.__TYPE__ = 'JSBObject';            var args = plus.ios.__Tool.process(arguments);            if ( nocreate && plus.tools.IOS == plus.tools.platform ) {} else {                plus.bridge.execSync2('"+_Server+"', '__Instance',[this.__UUID__, '"+r+"',args],null,true);            }        };",(t=t?i:"plus.ios.__JSBBaseObject")&&(o+=n+".prototype = new "+t+"('__super__constructor__');",o+=n+".prototype.constructor = "+n+";"),o},classTemplate.createClass=function(className,javasrcipt){className=iosTools.handleClassName(className);var newCls="(function(plus){"+javasrcipt+"return "+jsbDef+className+";})(plus);";return eval(newCls)},classTemplate.AddStaticConstToClass=function(e,t,n){var i;return e=iosTools.handleClassName(e),Array.isArray(n)&&0==n.length?(i=jsbDef+e+"."+t+"=[];",i+=jsbDef+e+".prototype."+t+"=[];"):(i=jsbDef+e+"."+t+"="+n+";",i+=jsbDef+e+".prototype."+t+"="+n+";"),i},classTemplate.AddMethodToClass=function(e,t,n){var i,o=e;e=iosTools.handleClassName(e);var r="";if(plus.tools.IOS==plus.tools.platform){var s=t.split(":"),a=s.length;if(a>0)for(var c=0;c<a;c++)r+=s[c];else r=s}else r=t;return i=n?jsbDef+e+"."+r:jsbDef+e+".prototype."+r,i+=" = function (){            var ret = null;            try {                var args = plus.ios.__Tool.process(arguments);",i+=n?"ret = plus.bridge.execSync2('"+_Server+"', '__execStatic', ['"+o+"', '"+t+"', args],null,true);":"ret = plus.bridge.execSync2('"+_Server+"', '__exec', [this.__UUID__, '"+t+"', args],null,true);",i+="ret = plus.ios.__Tool.New(ret, true);            } catch (e) {                throw e;            }            return ret;        };"},JSBridge.prototype.requestPermissions=function(e,t,n){if(Array.isArray(e)){var i=bridge.callbackId(t,n);bridge.exec(_Server,"requestPermissions",[i,e])}else{n({code:"-1",message:"Request permission must be array type"})}},JSBridge.prototype.checkPermission=function(e,t,n){if(Array.isArray(e)){n()}else{var i=bridge.callbackId(t,n);bridge.exec(_Server,"checkPermission",[i,e])}},JSBridge.prototype.importClass=function(e){var t,n;if((e=__compatibilityNamespace(e)).__TYPE__){if(!e.className)return;n=e.__UUID__,t=e,e=e.className}var i=classTemplate.isImport(e);if(i)return i;var o=bridge.execSync2(_Server,"__inheritList",[e,n],null,!0);if(o){for(var r=o.length,s=r-1;s>=0;s--)i=s==r-1?classTemplate.importClass(o[s],null):classTemplate.importClass(o[s],o[s+1]);return t&&(t.__proto__=i.prototype),i}return null},JSBridge.prototype.invoke=function(e,t){for(var n=null,i=[],o=2;o<arguments.length;o++)i.push(plus.ios.__Tool.warp(arguments[o]));if("string"==typeof e)try{n=plus.bridge.execSync2(_Server,"__execStatic",[e,t,i],null,!0)}catch(e){throw e}else e&&"JSBObject"==e.__TYPE__?n=plus.bridge.execSync2(_Server,"__exec",[e.__UUID__,t,i],null,!0):null==e&&"string"==typeof t&&(n=plus.bridge.execSync2(_Server,"__execCFunction",[t,i],null,!0));return n=plus.ios.__Tool.New(n,!0)},JSBridge.prototype.autoCollection=function(e){e&&"JSBObject"==e.__TYPE__&&plus.bridge.execSync2(_Server,"__autoCollection",[e.__UUID__],null,!0)},JSBridge.prototype.setAttribute=function(e,t,n){"function"==typeof e||e&&"JSBObject"==e.__TYPE__&&e.plusSetAttribute(t,n)},JSBridge.prototype.getAttribute=function(e,t){if("function"==typeof e);else if(e&&"JSBObject"==e.__TYPE__)return e.plusGetAttribute(t);return null},JSBridge.prototype.load=function(e){plus.bridge.execSync2(_Server,"__loadDylib",[e],null,!0)},JSBridge.prototype.newObject=function(e,t){var n=null;if("string"==typeof e){for(var i=[],o=1;o<arguments.length;o++)i.push(plus.ios.__Tool.warp(arguments[o]));n=plus.bridge.execSync2(_Server,"__newObject",[e,i],null,!0)}return(n=plus.ios.__Tool.New(n,!0))||new JNBType(e,t)},JSBridge.prototype.currentWebview=function(){if(!_currentFrameObj){var e=plus.bridge.execSync2(_Server,"currentWebview",[],null,!0);_currentFrameObj=plus.ios.__Tool.New(e,!0)}return _currentFrameObj},JSBridge.prototype.getWebviewById=function(e){if(e===plus.__HtMl_Id__)return this.currentWebview();var t=_frameObjHash[e];return t||(t=plus.bridge.execSync2(_Server,"getWebviewById",[e],null,!0),(t=plus.ios.__Tool.New(t,!0))&&(_frameObjHash[e]=t)),t},JSBridge.prototype.deleteObject=function(e){bridge.execSync2(_Server,"__release",[e.__UUID__],null,!0)},JSBridge.prototype.implements=function(e,t){return new JSImplements(e,t)};var __compatibilityNS={"io.dcloud.adapter.":"io.dcloud.common.adapter.","io.dcloud.android.content.":"io.dcloud.feature.internal.reflect.","io.dcloud.app.":"io.dcloud.common.app.","io.dcloud.constant.":"io.dcloud.common.constant.","io.dcloud.core.":"io.dcloud.common.core.","io.dcloud.DHInterface.":"io.dcloud.common.DHInterface.","io.dcloud.net.":"io.dcloud.common.util.net.","io.dcloud.permission.":"io.dcloud.common.core.permission.","io.dcloud.sdk.":"io.dcloud.feature.internal.sdk.","io.dcloud.splash.":"io.dcloud.feature.internal.splash.","io.dcloud.ui.":"io.dcloud.common.core.ui.","io.dcloud.util.":"io.dcloud.common.util."};function __compatibilityNamespace(e){if("string"!=typeof e||tools.platform==tools.IOS)return e;var t,n;for(var i in __compatibilityNS)if(0==e.indexOf(i)){t=i,n=__compatibilityNS[i];break}return n&&e?e.replace(t,n):e}plus.ios=plus.android=new JSBridge,plus.ios.import=plus.ios.importClass,plus.tools.ANDROID==plus.tools.platform&&(plus.android.runtimeMainActivity=function(){var e;if(plus.android.__runtimeMainActivity__)return plus.android.__runtimeMainActivity__;var t=plus.bridge.callbackId(function(t){e.onActivityResult&&e.onActivityResult(t[0],t[1],t[2])}),n=(e=plus.bridge.execSync2(_Server,"getContext",[t],null,!0)).className;return e.superClassNames=[],e=plus.ios.__Tool.New(e,!0),plus.android.importClass(n),plus.android.__runtimeMainActivity__=e,e})}),PlusObject.register("nativeUI",function(e,t,n,i){var o=t.bridge,r="NativeUI",s=t.bridge,a=t.tools;function c(e){o.execSync(r,"setUiStyle",[e])}function u(e){this.onCancel=null,this.onConfirm=null,this.onChange=null;var t=this,n=s.callbackId(function(){"function"==typeof t.onCancel&&t.onCancel()}),i=s.callbackId(function(e){"function"==typeof t.onConfirm&&t.onConfirm(e)}),a=s.callbackId(function(e){"function"==typeof t.onChange&&t.onChange(e)});o.exec(r,"showPicker",[e,n,i,a])}function l(e,n){this.__uuid__=t.tools.UUID("WaitingView"),this.onclose=null;var i=this,a=s.callbackId(function(){"function"==typeof i.onclose&&i.onclose()});o.exec(r,"WaitingView",[this.__uuid__,[e,n,a]])}function _(e,n,i){var r;this.__uuid__=t.tools.UUID("NativeObj_"),i&&(r=s.callbackId(function(e){var t={};t.index=e,i(t)})),o.exec("NativeUI",e,[t.__HtMl_Id__,[n,r,this.__uuid__]])}return u.prototype.update=function(e){o.exec(r,"updatePicker",[e])},l.prototype.close=function(){o.exec(r,"WaitingView_close",[this.__uuid__])},l.prototype.setTitle=function(e){o.exec(r,"WaitingView_setTitle",[this.__uuid__,[e]])},_.prototype.close=function(){o.exec("NativeUI","_NativeObj_close",[this.__uuid__])},{pickTime:function(e,n,i){if(e&&"function"==typeof e){var a=!1;if("object"==typeof i){var c=i.time;"[object Date]"===Object.prototype.toString.call(c)&&(i.__hours=c.getHours(),i.__minutes=c.getMinutes(),a=!0)}var u="function"!=typeof e?null:function(t){var n=void 0!==t?new Date(t):null,i={};i.date=n,e(i)},l="function"!=typeof n?null:function(e){n(e)},_=s.callbackId(u,l);o.exec(r,"pickTime",[t.__HtMl_Id__,[_,i]]),a&&(delete i.__hours,delete i.__minutes)}},pickDate:function(e,n,i){if(e&&"function"==typeof e){var c={};i&&("[object Date]"===Object.prototype.toString.call(i.minDate)?(c.startYear=i.minDate.getFullYear(),c.startMonth=i.minDate.getMonth(),c.startDay=i.minDate.getDate()):a.isNumber(i.startYear)&&(c.startYear=i.startYear,c.startMonth=0,c.startDay=1),"[object Date]"===Object.prototype.toString.call(i.maxDate)?(c.endYear=i.maxDate.getFullYear(),c.endMonth=i.maxDate.getMonth(),c.endDay=i.maxDate.getDate()):a.isNumber(i.endYear)&&(c.endYear=i.endYear,c.endMonth=11,c.endDay=31),"[object Date]"===Object.prototype.toString.call(i.date)&&(c.setYear=i.date.getFullYear(),c.setMonth=i.date.getMonth(),c.setDay=i.date.getDate()),c.popover=i.popover,c.title=i.title);var u="function"!=typeof e?null:function(t){var n=void 0!==t?new Date(t):null,i={};i.date=n,e(i)},l="function"!=typeof n?null:function(e){n(e)},_=s.callbackId(u,l);o.exec(r,"pickDate",[t.__HtMl_Id__,[_,c]])}},alert:function(e,n,i,a){var c,u;e&&(u="string"!=typeof e?e.toString():e,n&&(c=s.callbackId(function(e){n(e)})),o.exec(r,"alert",[t.__HtMl_Id__,[u,c,i,a]]))},confirm:function(e,n,i,a){var c,u;e&&(u="string"!=typeof e?e.toString():e,n&&(c=s.callbackId(function(e){var t={};t.index=e,n(t)})),o.exec(r,"confirm",[t.__HtMl_Id__,[u,c,i,a]]))},showWaiting:function(e,t){return new l(e,t)},prompt:function(e,n,i,a,c){var u,l;e&&(u="string"!=typeof e?e.toString():e,n&&(l=s.callbackId(function(e){e.value=e.message,n(e)})),o.exec(r,"prompt",[t.__HtMl_Id__,[u,l,i,a,c]]))},toast:function(e,n){var i;e&&(i="string"!=typeof e?e.toString():e,o.exec(r,"toast",[t.__HtMl_Id__,[i,n]]))},closeToast:function(){o.exec(r,"closeToast",[t.__HtMl_Id__,[]])},showMenu:function(e,n,i){var a;e&&(e.onclick&&(e.__plus__onclickCallbackId=s.callbackId(e.onclick)),i&&(a=s.callbackId(function(e){var t={};t.index=e.index,t.target=n[e.index],i(t)})),o.exec(r,"showMenu",[t.__HtMl_Id__,[e,n,a]]))},hideMenu:function(){o.exec(r,"hideMenu",[t.__HtMl_Id__])},isTitlebarVisible:function(){return o.execSync(r,"isTitlebarVisible",[t.__HtMl_Id__])},setTitlebarVisible:function(e){return o.exec(r,"setTitlebarVisible",[t.__HtMl_Id__,[e]])},getTitlebarHeight:function(){return o.execSync(r,"getTitlebarHeight",[t.__HtMl_Id__])},actionSheet:function(e,t){return new _("actionSheet",e,t)},closeWaiting:function(){s.exec(r,"closeWaiting",[])},setUiStyle:c,setUIStyle:c,previewImage:function(e,t){var n=t,i=s.callbackId(function(e){"function"==typeof n.onLongPress&&n.onLongPress(e)});s.exec(r,"previewImage",[this.__uuid__,[e,t,i]])},closePreviewImage:function(){s.exec(r,"closePreviewImage",[this.__uuid__])},showPicker:function(e){return new u(e)}}}),PlusObject.register("navigator",function(plusContext,plus,require,param){var bridge=plus.bridge,T=plus.tools,_PLUSNAME="Navigator";return{closeSplashscreen:function(){bridge.exec(_PLUSNAME,"closeSplashscreen",[0])},updateSplashscreen:function(e){bridge.exec(_PLUSNAME,"updateSplashscreen",[e])},isFullscreen:function(){return bridge.execSync(_PLUSNAME,"isFullScreen",[0])},setFullscreen:function(e){bridge.exec(_PLUSNAME,"setFullscreen",[e])},isImmersedStatusbar:function(){return bridge.execSync(_PLUSNAME,"isImmersedStatusbar",[])},getStatusbarHeight:function(e){return this.__statusBarHeight__||(this.__statusBarHeight__=bridge.execSync(_PLUSNAME,"getStatusbarHeight",[])),this.__statusBarHeight__},setStatusBarBackground:function(e){bridge.exec(_PLUSNAME,"setStatusBarBackground",[e])},getStatusBarBackground:function(){return bridge.execSync(_PLUSNAME,"getStatusBarBackground",[])},setStatusBarStyle:function(e){bridge.exec(_PLUSNAME,"setStatusBarStyle",[e])},getStatusBarStyle:function(){return bridge.execSync(_PLUSNAME,"getStatusBarStyle",[])},getUiStyle:function(){return bridge.execSync(_PLUSNAME,"getUiStyle",[])},setUserAgent:function(e,t){bridge.execSync(_PLUSNAME,"setUserAgent",[e,t])},getUserAgent:function(){return bridge.execSync(_PLUSNAME,"getUserAgent",[],function(r){try{var reslut=eval(r);return null==reslut?null:r}catch(e){return r}return r})},removeCookie:function(e){bridge.exec(_PLUSNAME,"removeCookie",[e])},removeSessionCookie:function(e,t){bridge.exec(_PLUSNAME,"removeSessionCookie",[])},removeAllCookie:function(e,t){bridge.exec(_PLUSNAME,"removeAllCookie",[])},setCookie:function(e,t){bridge.exec(_PLUSNAME,"setCookie",[e,t])},getCookie:function(url){return bridge.execSync(_PLUSNAME,"getCookie",[url],function(r){try{var reslut=eval(r);return null==reslut?null:r}catch(e){return r}return r})},setLogs:function(e){bridge.exec(_PLUSNAME,"setLogs",[e])},isLogs:function(){return bridge.execSync(_PLUSNAME,"isLogs",[0])},createShortcut:function(e,t,n){var i=bridge.callbackId(t,n);bridge.exec(_PLUSNAME,"createShortcut",[e,i])},hasShortcut:function(e,t){if(T.platform!=T.IOS){var n=bridge.callbackId(t);bridge.exec(_PLUSNAME,"hasShortcut",[e,n])}},checkPermission:function(e){return bridge.execSync(_PLUSNAME,"checkPermission",[e])},requestPermission:function(e,t){var n=bridge.callbackId(t);return bridge.exec(_PLUSNAME,"requestPermission",[e,n])},isBackground:function(){return bridge.execSync(_PLUSNAME,"isBackground",[])},hasNotchInScreen:function(){return bridge.execSync(_PLUSNAME,"hasNotchInScreen",[])},hasSplashscreen:function(){return bridge.execSync(_PLUSNAME,"hasSplashscreen",[])},hideSystemNavigation:function(){return bridge.exec(_PLUSNAME,"hideSystemNavigation",[])},showSystemNavigation:function(){return bridge.exec(_PLUSNAME,"showSystemNavigation",[])},getSafeAreaInsets:function(){return plus.tools.ANDROID==plus.tools.platform?{bottom:0,left:0,right:0,top:0,deviceBottom:0,deviceLeft:0,deviceRight:0,deviceTop:0}:bridge.execSync(_PLUSNAME,"getSafeAreaInsets",[])},getOrientation:function(){return bridge.execSync(_PLUSNAME,"getOrientation",[])},getUIStyle:function(){return bridge.execSync(_PLUSNAME,"getUiStyle",[])},isRoot:function(){return plus.tools.ANDROID!=plus.tools.platform&&bridge.execSync(_PLUSNAME,"isRoot",[])},isSimulator:function(){return bridge.execSync(_PLUSNAME,"isSimulator",[])},getSignature:function(){return bridge.execSync(_PLUSNAME,"getSignature",[])}}}),PlusObject.register("key",function(e,t){var n=(t=t).bridge,i=t.tools;return keyEvent={},keyEvent.backbutton="back",keyEvent.menubutton="menu",keyEvent.searchbutton="search",keyEvent.volumeupbutton="volumeup",keyEvent.volumedownbutton="volumedown",keyEvent.keyup="keyup",keyEvent.keydown="keydown",keyEvent.longpressed="longpressed",t.key={addEventListener:function(e,i,o){if(e&&i&&"string"==typeof e&&"function"==typeof i){var r=t.webview.__currentWebview();if(t.obj.Callback.prototype.addEventListener.apply(r,[keyEvent[e],function(e){var t={};t.keycode=e.keyType,t.keyCode=e.keyCode,t.keyType=e.keyType,i(t)},o])){var s=[keyEvent[e],t.__HtMl_Id__];n.exec("UI","execMethod",[r.__IDENTITY__,"addEventListener",[r.__uuid__,s]])}}},removeEventListener:function(e,i){if(e&&i&&"string"==typeof e&&"function"==typeof i){var o=t.webview.__currentWebview();if(t.obj.Callback.prototype.removeEventListener.apply(o,[keyEvent[e],i])){var r=[keyEvent[e],t.__HtMl_Id__];o=t.webview.__currentWebview();n.exec("UI","execMethod",[o.__IDENTITY__,"removeEventListener",[o.__uuid__,r]])}}},setAssistantType:function(o){if(i.platform==i.IOS)e.__keyboardAssist.setInputType(o);else{var r=[o,t.__HtMl_Id__],s=t.webview.currentWebview();n.exec("UI","execMethod",[s.__IDENTITY__,"setAssistantType",[s.__uuid__,r]])}},hideSoftKeybord:function(){if(i.platform==i.IOS)n.exec("Runtime","hideSoftKeybord");else{var e=[t.__HtMl_Id__],o=t.webview.__currentWebview();n.exec("UI","execMethod",[o.__IDENTITY__,"hideSoftKeybord",[o.__uuid__,e]])}},showSoftKeybord:function(){var e=[t.__HtMl_Id__],i=t.webview.__currentWebview();n.exec("UI","execMethod",[i.__IDENTITY__,"showSoftKeybord",[i.__uuid__,e]])},setVolumeButtonEnabled:setVolumeButtonEnabled=function(e){if(i.ANDROID==i.platform){var o=[e],r=t.webview.currentWebview();n.exec("UI","execMethod",[r.__IDENTITY__,"setVolumeButtonEnabled",[r.__uuid__,o]])}}}}),PlusObject.register("webview",function(e,t,n){var i=(t=t).bridge,o="UI",r="execMethod",s="syncExecMethod",a={},c=t.bridge,u=t.tools;function l(e,t,n){i.exec(o,r,[e.__IDENTITY__,t,[e.__uuid__,n]])}function _(e){this.__IDENTITY__=e,this.__uuid__=t.tools.UUID(e),this.id,t.obj.Callback.call(this)}function f(){return u.platform!=u.IOS&&i.execSync(o,s,[o,"defaultHardwareAccelerated",[]])}function d(e){if(e&&"string"==typeof e){var n=i.execSync(o,s,[o,"findWindowByName",[t.__HtMl_Id__,[e]]]);if(n){var c=a[n.uuid];return null==c&&((c=new t.webview.Webview(null,null,!0,n.extras)).__uuid__=n.uuid,c.id=n.id,i.exec(o,r,[o,"setcallbackid",[c.__uuid__,[c.__callback_id__]]]),a[c.__uuid__]=c),c}for(var u in a){var l=a[u];if(l&&l.id===e)return l}return null}}function p(){var e=a[t.__HtMl_Id__];if(null==e||void 0===e){var n=i.execSync2(o,s,[o,"currentWebview",[t.__HtMl_Id__]]);n&&((e=new t.webview.Webview(null,null,!0,n.extras)).__uuid__=t.__HtMl_Id__,e.id=n.id,a[e.__uuid__]=e,i.exec(o,r,[o,"setcallbackid",[e.__uuid__,[e.__callback_id__]]]))}return e}_.prototype.getMetrics=function(e){e&&l(this,"getMetrics",[c.callbackId(function(t){var n={};n.canForward=t,e(n)}),t.__HtMl_Id__])},_.prototype.onCallback=function(e,n,i){if("popGesture"==n){var o=i.private_args,r={target:t.webview._find__Window_By_UUID__(o.uuid,o.id,o.extras),type:i.type,progress:i.progress};"result"in i&&(r.result=i.result),i=r}else(i=i||{}).target=this;e(i)},_.prototype.addEventListener=function(e,n,i){t.obj.Callback.prototype.addEventListener.apply(this,[e,n,i])&&l(this,"addEventListener",[e,t.__HtMl_Id__])},_.prototype.removeEventListener=function(e,n){t.obj.Callback.prototype.removeEventListener.apply(this,[e,n])&&l(this,"removeEventListener",[e,t.__HtMl_Id__])};var h={open:function(e,n,i,o,r,s,a){var c=t.webview.create(e,n,i,a);return c.show(o,r,s),c},show:function(e,n,i,o,r){var s=null;if("string"==typeof e)s=d(e);else{if(!(e instanceof t.webview.Webview))return;s=e}s&&s.show(n,i,o,r)},hide:function(e,n,i,o){var r=null;if("string"==typeof e)r=d(e);else{if(!(e instanceof t.webview.Webview))return;r=e}r&&r.hide(n,i,o)},NView:_,createGroup:null,getWapLaunchWebview:function(){if(u.IOS==u.platform)return null;var e=i.execSync(o,s,[o,"getWapLaunchWebview",[]]);if(e){var n=a[e.uuid];return null==n&&((n=new t.webview.Webview(null,null,!0,e.extras)).__uuid__=e.uuid,n.id=e.id,i.exec(o,r,[o,"setcallbackid",[n.__uuid__,[n.__callback_id__]]]),a[n.__uuid__]=n),n}return null},getLaunchWebview:function(){var e=i.execSync2(o,s,[o,"getLaunchWebview",[]]);if(e){var n=a[e.uuid];return null==n&&((n=new t.webview.Webview(null,null,!0,e.extras)).__uuid__=e.uuid,n.id=e.id,i.exec(o,r,[o,"setcallbackid",[n.__uuid__,[n.__callback_id__]]]),a[n.__uuid__]=n),n}},getSecondWebview:function(){var e=i.execSync2(o,s,[o,"getSecondWebview",[]]);if(e){var n=a[e.uuid];return null==n&&((n=new t.webview.Webview(null,null,!0,e.extras)).__uuid__=e.uuid,n.id=e.id,i.exec(o,r,[o,"setcallbackid",[n.__uuid__,[n.__callback_id__]]]),a[n.__uuid__]=n),n}},getWebviewById:d,getTopWebview:function(){var e=i.execSync(o,s,[o,"getTopWebview",[t.__HtMl_Id__]]);if(e){var n=a[e.uuid];return null==n&&((n=new t.webview.Webview(null,null,!0,e.extras)).__uuid__=e.uuid,n.id=e.id,i.exec(o,r,[o,"setcallbackid",[n.__uuid__,[n.__callback_id__]]]),a[n.__uuid__]=n),n}return null},close:function(e,n,i){var o=null;if("string"==typeof e)o=d(e);else{if(!(e instanceof t.webview.Webview))return;o=e}o&&o.close(n,i)},create:function(e,n,i,o){return(i=i||{}).name=n,new t.webview.Webview(e,i,!1,o)},prefetchURL:function(e){i.exec(o,r,[o,"prefetchURL",[t.__HtMl_Id__,[e]]])},prefetchURLs:function(e){i.exec(o,r,[o,"prefetchURLs",[t.__HtMl_Id__,[e]]])},currentWebview:p,__currentWebview:p,postMessageToUniNView:function(e,n){var s=a[t.__HtMl_Id__];i.exec(o,r,[o,"postMessageToUniNView",[s.__uuid__,[e,n]]])},__callNativeModuleSync__:function(e,n){var r=a[t.__HtMl_Id__];return i.execSync2(o,s,[o,"__callNativeModuleSync",[r.__uuid__,[e,n]]])},all:function(){for(var e=i.execSync(o,s,[o,"enumWindow",[t.__HtMl_Id__]]),n=[],c={},u=0;u<e.length;u++){var l=e[u],_=a[l.uuid];_||((_=new t.webview.Webview(null,null,!0,l.extras)).__uuid__=l.uuid,_.id=l.id,i.exec(o,r,[o,"setcallbackid",[_.__uuid__,[_.__callback_id__]]])),n.push(_),c[_.__uuid__]=_}return a=c,n},getDisplayWebview:function(){var e=i.execSync(o,s,[o,"getDisplayWebview",[t.__HtMl_Id__]]),n=[];if(!e)return n;for(var c={},u=0;u<e.length;u++){var l=e[u],_=a[l.uuid];_?_.id||(_.id=l.id):((_=new t.webview.Webview(null,null,!0,l.extras)).__uuid__=l.uuid,_.id=l.id,i.exec(o,r,[o,"setcallbackid",[_.__uuid__,[_.__callback_id__]]])),n.push(_),c[_.__uuid__]=_}return a=c,n},defauleHardwareAccelerated:function(){return f()},defaultHardwareAccelerated:f,exec:l,execSync:function(e,t,n){return i.execSync(o,s,[e.__IDENTITY__,t,[e.__uuid__,n]])},_find__Window_By_UUID__:function(e,n,s){if(e&&"string"==typeof e){var c=a[e];return c||((c=new t.webview.Webview(null,null,!0,s)).__uuid__=e,c.id__=n,c.id=n,i.exec(o,r,[o,"setcallbackid",[c.__uuid__,[c.__callback_id__]]]),a[e]=c),c}},__pushWindow__:function(e){a[e.__uuid__]=e},__popWindow__:function(e){delete a[e.__uuid__]},__JSON_Window_Stack:a,__Webview_LoadEvent_CallBack_:function(e){var t=a[e.WebviewID];t&&("onloading"==e.Event?null!=t.onloading&&t.onloading({target:t}):"onclose"==e.Event?(null!=t.onclose&&t.onclose({target:t}),delete a[e.WebviewID]):"onerror"==e.Event?null!=t.onerror&&t.onerror({target:t}):"onloaded"==e.Event&&null!=t.onloaded&&t.onloaded({target:t}))},startAnimation:function(e,n,s){var a,u;e&&e.view&&e.styles&&e.styles.toLeft&&("string"==typeof e.view||e.view instanceof t.webview.Webview)&&(e.view instanceof t.webview.Webview&&(e.view=e.view.__uuid__),n&&n.view&&("string"==typeof n.view||n.view instanceof t.webview.Webview)&&n.view instanceof t.webview.Webview&&(n.view=n.view.__uuid__),s&&"function"==typeof s&&(a=t.webview.currentWebview().__uuid__,u=c.callbackId(function(e){e.target=t.webview._find__Window_By_UUID__(e.target.uuid),s(e)})),i.exec(o,r,[o,"startAnimation",[t.__HtMl_Id__,[e,n,a,u]]]))}};return h.WebviewGroup=LoadWebviewMoudle(e,t,h),h.Webview=LoadWebviewMoudle(e,t,h),h}),PlusObject.register("oauth",function(e,t,n,i){var o=t.bridge;function r(){this.id="",this.description="",this.nativeClient=!1,this.authResult=null,this.userInfo=null,this.appleInfo=null,this.univerifyInfo=null}r.prototype.getCheckBoxState=function(){if("univerify"==this.id)return o.execSync2("OAuth","getCheckBoxState",[this.id])},r.prototype.closeAuthView=function(){"univerify"==this.id&&o.exec("OAuth","closeAuthView",[this.id],{sid:this.id})},r.prototype.preLogin=function(e,t){var n="function"!=typeof e?null:function(t){e({})},i="function"!=typeof t?null:function(e){t(e)};if("univerify"==this.id){var r=o.callbackId(n,i);o.exec("OAuth","preLogin",[this.id,r],{cbid:r,sid:this.id})}else{t({code:10012,message:"univerify only"})}},r.prototype.login=function(e,n,i){if(i&&"object"==typeof i){var r=i.univerifyStyle;if(r&&"object"==typeof r){var s=r.buttons;s&&"object"==typeof s&&function(e){if(Array.isArray(e))for(var n=0;n<e.length;n++)!function(e){if(e.onclick&&"function"==typeof e.onclick){var n=o.callbackId(function(){e.onclick(e)});e.__cb__={id:n,htmlId:t.__HtMl_Id__}}}(e[n])}(s.list)}}var a=this,c="function"!=typeof e?null:function(t){var n={};n.target=a,a.univerifyInfo=t.univerifyInfo,a.authResult=t.authResult,a.userInfo=t.userInfo,a.appleInfo=t.appleInfo,a.extra=t.extra,e(n)},u="function"!=typeof n?null:function(e){n(e)},l=o.callbackId(c,u);o.exec("OAuth","login",[this.id,l,i],{cbid:l,sid:this.id})},r.prototype.authorize=function(e,t,n){var i=this,r="function"!=typeof e?null:function(t){var n={};(n=t).target=i,e(n)},s="function"!=typeof t?null:function(e){t(e)};if("weixin"==i.id){var a=o.callbackId(r,s);o.exec("OAuth","authorize",[this.id,a,n],{cbid:a,sid:this.id})}else{t({code:10012,message:"WeChat only"})}},r.prototype.logout=function(e,t){var n=this,i="function"!=typeof e?null:function(t){var i={};n.authResult=null,n.userInfo=null,n.appleInfo=null,n.extra=t.extra,i.target=n,e(i)},r="function"!=typeof t?null:function(e){t(e)};this.authResult=null,this.userInfo=null,this.appleInfo=null;var s=o.callbackId(i,r);o.exec("OAuth","logout",[this.id,s],{cbid:s,sid:this.id})},r.prototype.getUserInfo=function(e,t){var n=this,i="function"!=typeof e?null:function(t){var i={};n.authResult=t.authResult,n.userInfo=t.userInfo,n.appleInfo=t.appleInfo,n.extra=t.extra,i.target=n,e(i)},r="function"!=typeof t?null:function(e){t(e)},s=o.callbackId(i,r);o.exec("OAuth","getUserInfo",[this.id,s],{cbid:s,sid:this.id})},r.prototype.addPhoneNumber=function(e,t){var n=this,i="function"!=typeof e?null:function(t){var i={};n.authResult=t.authResult,n.userInfo=t.userInfo,n.appleInfo=t.appleInfo,n.extra=t.extra,i.target=n,e(i)},r="function"!=typeof t?null:function(e){t(e)},s=o.callbackId(i,r);o.exec("OAuth","addPhoneNumber",[this.id,s],{cbid:s,sid:this.id})};var s={AuthService:r,getServices:function(e,t){var n="function"!=typeof e?null:function(t){for(var n=[],i=t.length,o=0;o<i;o++){var r=new s.AuthService;r.id=t[o].id,r.description=t[o].description,r.authResult=t[o].authResult,r.userInfo=t[o].userInfo,r.appleInfo=t[o].appleInfo,r.univerifyInfo=t[o].univerifyInfo,r.nativeClient=t[o].nativeClient,n[o]=r}e(n)},i="function"!=typeof t?null:function(e){t(e)},r=o.callbackId(n,i);o.exec("OAuth","getServices",[r])}};t.oauth=s}),PlusObject.register("nativeObj",function(e,t,n,i){var o="NativeObj",r=t.bridge,s=t.tools,a={},c={},u={},l={};function _(e,t,n){this.__id__=s.UUID("Bitmap"),this.id=e,this.type="bitmap",n||(c[this.__id__]=this,r.exec(o,"Bitmap",[this.__id__,e,t]))}function f(e){if(Array.isArray(e))for(var t,n=0;n<e.length;n++)(t=e[n])&&"input"==t.tag?t.inputStyles&&(t.inputStyles.onComplete&&"function"==typeof t.inputStyles.onComplete&&(t.inputStyles.__onCompleteCallBackId__=r.callbackId(t.inputStyles.onComplete)),t.inputStyles.onFocus&&"function"==typeof t.inputStyles.onFocus&&(t.inputStyles.__onFocusCallBackId__=r.callbackId(t.inputStyles.onFocus)),t.inputStyles.onBlur&&"function"==typeof t.inputStyles.onBlur&&(t.inputStyles.__onBlurCallBackId__=r.callbackId(t.inputStyles.onBlur))):t&&"richtext"==t.tag&&t.richTextStyles&&t.richTextStyles.onClick&&"function"==typeof t.richTextStyles.onClick&&(t.richTextStyles.__onClickCallBackId__=r.callbackId(t.richTextStyles.onClick))}function d(e,t,n,i,c){this.__id__=e,this.id=e,this.__uuid__=s.UUID("NativeView"),this.type="nativeView",this.IDENTITY=o,i||(f(n),r.exec(o,"View",[this.__id__,this.__uuid__,t,n,c]),a[this.__uuid__]=this)}function p(e,t){var n=null;for(var i in l){var o=l[i];if(t){if(o.uid&&o.uid==e||o.uuid&&o.uuid==e){n=o;break}}else if(o.id==e){n=o;break}}return n}function h(e){var n=a[e.uuid];if(!n){if(e.type&&"ImageSlider"==e.type)n=new t.nativeObj.ImageSlider(e.id,e.styles,"",!0);else{var i=u[e.type];n=i?new i(e.id,e.styles,"",!0):new t.nativeObj.View(e.id,e.styles,"",!0)}n.__uuid__=e.uuid,a[n.__uuid__]=n}return n}return _.prototype.clear=function(){r.exec(o,"clear",[this.__id__]),this.__id__=void 0,c[this.__id__]&&delete c[this.__id__]},_.prototype.recycle=function(){r.exec(o,"bitmapRecycle",[this.__id__])},_.prototype.load=function(e,t,n){var i=function(e){"function"==typeof n&&n(e)};if(this.__id__){var s=r.callbackId(function(){"function"==typeof t&&t()},i);r.exec(o,"load",[this.__id__,e,s])}else i({code:-1,message:"Destroyed objects"})},_.prototype.loadBase64Data=function(e,t,n){var i=function(e){"function"==typeof n&&n(e)};if(this.__id__){var s=r.callbackId(function(){"function"==typeof t&&t()},i);r.exec(o,"loadBase64Data",[this.__id__,e,s])}else i({code:-1,message:"Destroyed objects"})},_.prototype.save=function(e,t,n,i){var s=function(e){"function"==typeof i&&i(e)};if(this.__id__){var a=r.callbackId(function(e){if("function"==typeof n){var t={target:e.path,width:e.w,height:e.h,size:e.size};n(t)}},s);r.exec(o,"save",[this.__id__,e,t,a])}else s({code:-1,message:"Destroyed objects"})},_.prototype.__captureWebview=function(e,t,n,i){var s=function(e){"function"==typeof i&&i(e)};if(this.__id__){var a=r.callbackId(function(){"function"==typeof n&&n()},s);r.exec(o,"captureWebview",[this.__id__,e,a,t])}else s({code:-1,message:"Destroyed objects"})},_.prototype.toBase64Data=function(){return this.__id__?r.execSync(o,"toBase64Data",[this.__id__]):null},_.getItems=function(){for(var e=[],t=r.execSync(o,"getItems",[]),n=0;n<t.length;n++){var i=t[n].__id__,s=c[i];if(!s){var a=new _(t[n].id,null,!0);a.__id__=i,c[i]=a,s=a}e.push(s)}return e},_.getBitmapById=function(e){var t=r.execSync(o,"getBitmapById",[e]);if(t){var n=c[t.__id__];if(!n){var i=new _(t.id,null,!0);i.__id__=t.__id__,c[t.__id__]=i,n=i}return n}return null},d.prototype.setImages=function(e){r.exec(o,"setImages",[this.__id__,this.__uuid__,e])},d.prototype.currentImageIndex=function(){return r.execSync(o,"currentImageIndex",[this.__id__,this.__uuid__])},d.prototype.addImages=function(e){r.exec(o,"addImages",[this.__id__,this.__uuid__,e])},d.prototype.drawBitmap=function(e,t,n,i){r.exec(o,"drawBitmap",[this.__id__,this.__uuid__,e,t,n,i])},d.prototype.drawText=function(e,t,n,i){r.exec(o,"drawText",[this.__id__,this.__uuid__,e,t,n,i])},d.prototype.drawRichText=function(e,t,n,i){n&&n.onClick&&"function"==typeof n.onClick&&(n.__onClickCallBackId__=r.callbackId(n.onClick)),r.exec(o,"drawRichText",[this.__id__,this.__uuid__,e,t,n,i])},d.prototype.drawInput=function(e,t,n){t.onComplete&&"function"==typeof t.onComplete&&(t.__onCompleteCallBackId__=r.callbackId(t.onComplete)),t.onFocus&&"function"==typeof t.onFocus&&(t.__onFocusCallBackId__=r.callbackId(t.onFocus)),t.onBlur&&"function"==typeof t.onBlur&&(t.__onBlurCallBackId__=r.callbackId(t.onBlur)),r.exec(o,"drawInput",[this.__id__,this.__uuid__,e,t,n])},d.prototype.getInputValueById=function(e){return r.execSync(o,"getInputValueById",[this.__id__,this.__uuid__,e])},d.prototype.getInputFocusById=function(e){return r.execSync(o,"getInputFocusById",[this.__id__,this.__uuid__,e])},d.prototype.setInputFocusById=function(e,t){r.exec(o,"setInputFocusById",[this.__id__,this.__uuid__,e,t])},d.prototype.show=function(){r.exec(o,"show",[this.__id__,this.__uuid__])},d.prototype.setStyle=function(e){r.exec(o,"setStyle",[this.__id__,this.__uuid__,e])},d.prototype.hide=function(){r.exec(o,"hide",[this.__id__,this.__uuid__])},d.prototype.clear=function(){this.close()},d.prototype.close=function(){r.exec(o,"view_close",[this.__id__,this.__uuid__]),delete a[this.__uuid__]},d.prototype.animate=function(e,t){var n;t&&(n=r.callbackId(function(){"function"==typeof t&&t()})),r.exec(o,"view_animate",[this.__id__,this.__uuid__,e,n])},d.prototype.reset=function(){r.exec(o,"view_reset",[this.__id__,this.__uuid__])},d.prototype.restore=function(){r.exec(o,"view_restore",[this.__id__,this.__uuid__])},d.prototype.isVisible=function(){return r.execSync(o,"isVisible",[this.__id__,this.__uuid__])},d.prototype.drawRect=function(e,t,n){r.exec(o,"view_drawRect",[this.__id__,this.__uuid__,e,t,n])},d.prototype.interceptTouchEvent=function(e){r.exec(o,"interceptTouchEvent",[this.__id__,this.__uuid__,e])},d.prototype.addEventListener=function(e,t){var n,i=this;if(t){var s=function(e){"function"==typeof t&&(e.target=i,t(e))};t.callback=s,n=r.callbackId(s)}r.exec(o,"addEventListener",[this.__id__,this.__uuid__,e,n])},d.prototype.setEventListener=function(e,t){var n,i=this;if(t){var s=function(e){"function"==typeof t&&(e.target=i,t(e))};t.callback=s,n=r.callbackId(s)}r.exec(o,"setEventListener",[this.__id__,this.__uuid__,e,n])},d.prototype.setTouchEventRect=function(e){r.exec(o,"setTouchEventRect",[this.__id__,this.__uuid__,e])},d.getViewById=function(e){var t=r.execSync(o,"getViewById",[e]);return t||(t=p(e,!1)),t?h(t):null},d.__getViewByUidAndCreate=function(e){return h(e)},d.__getViewByUidFromCache=function(e){var t=p(e,!0);return t?h(t):null},d.startAnimation=function(e,t,n,i){var s,a,c;e&&t&&(a=t instanceof d?{viewId:t.__uuid__}:{uuid:t.bitmap?t.bitmap.__id__:null,texts:{value:t.text,textStyles:t.textStyles,textRect:t.textRect}},n&&(c=n instanceof d?{viewId:n.__uuid__}:{uuid:n.bitmap?n.bitmap.__id__:null,texts:{value:n.text,textStyles:n.textStyles,textRect:n.textRect}}),i&&(s=r.callbackId(function(){"function"==typeof i&&i()})),r.exec(o,"startAnimation",[e,a,c,s]))},d.clearAnimation=function(e){e||(e="none"),r.exec(o,"clearAnimation",[e])},d.prototype.clearRect=function(e,t){r.exec(o,"view_clearRect",[this.__id__,this.__uuid__,e,t])},d.prototype.draw=function(e){f(e),r.exec(o,"view_draw",[this.__id__,this.__uuid__,e])},{Bitmap:_,View:d,ImageSlider:function(e,t,n,i){var o=new d(e,t,n,i,"ImageSlider");return o.type="ImageSlider",o},__createNView:function(e,n,i,o){var r=new t.nativeObj.View(n,i,o);return r.__uuid__=e,a[r.__uuid__]=r,r},__appendSubViewInfo:function(e){l[e.uuid]=e},__removeSubViewInfos:function(e){for(var t=0;t<e.length;t++)delete l[e[t]]},__regChildViews:function(e,t){e&&"function"==typeof t&&(u[e]=t)}}}),PlusObject.register("stream",function(e,t){var n=t.bridge,i=t.tools;t.stream={open:function(e,t,i){var o=n.callbackId(t,i);n.exec("Stream","open",[e,o])},setRestoreState:function(e){n.exec("Stream","setRestoreState",[e])},preload:function(e){n.exec("Stream","preload",[e])},list:function(e,t,i){var o=n.callbackId(t,i);n.exec("Stream","list",[e,o])},remove:function(e){n.exec("Stream","remove",[e])},freetrafficRequest:function(e,t,o){if(i.platform!=i.IOS){var r=n.callbackId(t,o);n.exec("Stream","freetrafficRequest",[e,r])}else"function"==typeof o&&o({code:-3,message:"Not Supported"})},freetrafficBind:function(e,t,o){if(i.platform!=i.IOS){var r=n.callbackId(t,o);n.exec("Stream","freetrafficBind",[e,r])}else"function"==typeof o&&o({code:-3,message:"Not Supported"})},freetrafficRelease:function(e,t,o){if(i.platform!=i.IOS){var r=n.callbackId(t,o);n.exec("Stream","freetrafficRelease",[e,r])}else"function"==typeof o&&o({code:-3,message:"Not Supported"})},freetrafficInfo:function(e,t){if(i.platform!=i.IOS){var o=n.callbackId(e,t);n.exec("Stream","freetrafficInfo",[o])}else"function"==typeof t&&t({code:-3,message:"Not Supported"})},freetrafficIsValid:function(){if(i.platform!=i.IOS)return n.exec("Stream","freetrafficIsValid",null);"function"==typeof errorCallback&&errorCallback({code:-3,message:"Not Supported"})},activate:function(){i.platform!=i.IOS?n.exec("Stream","activate",null):"function"==typeof errorCallback&&errorCallback({code:-3,message:"Not Supported"})}}}),PlusObject.register("device",function(e,t,n,i){var o=t.require("bridge");return{imei:i&&i.imei||"",imsi:i&&i.imsi||[],model:i&&i.model||"",vendor:i&&i.vendor||"",uuid:i&&i.uuid||"",dial:function(e,t){o.exec("Device","dial",[e,t])},beep:function(e){o.exec("Device","beep",[e])},vibrate:function(e){o.exec("Device","vibrate",[e])},setWakelock:function(e){o.exec("Device","setWakelock",[e])},isWakelock:function(){return o.execSync("Device","isWakelock",[])},setVolume:function(e){o.execSync("Device","setVolume",[e])},getVolume:function(){return o.execSync("Device","getVolume",[])},execCallback:function(e,t){var n="function"!=typeof e.complete?function(){}:e.complete,i="function"!=typeof e.success?n:function(t){e.success(t),n(t)},r="function"!=typeof e.fail?n:function(t){e.fail(t),n(t)};callbackID=o.callbackId(i,r),o.exec("Device",t,[callbackID])},getInfo:function(e){this.execCallback(e,"getInfo")},getOAID:function(e){this.execCallback(e,"getOAID")},getVAID:function(e){this.execCallback(e,"getVAID")},getAAID:function(e){this.execCallback(e,"getAAID")},isCaptured:function(){return o.execSync("Device","isCaptured",[])},updateInfo:function(e){e&&(this.imei=e.imei,this.imsi=e.imsi,this.model=e.model,this.vendor=e.vendor,this.uuid=e.uuid)},getDeviceId:function(){return o.execSync("Device","getDeviceId",[])}}}),PlusObject.register("os",function(e,t,n,i){return{language:(i=i||{}).language||null,version:i.version||null,name:i.name||null,vendor:i.vendor||null,updateInfo:function(e){e&&(this.language=e.language,this.version=e.version,this.name=e.name,this.vendor=e.vendor)}}}),PlusObject.register("screen",function(e,t,n,i){var o=t.require("bridge");return{resolutionHeight:(i=i||{}).resolutionHeight||null,resolutionWidth:i.resolutionWidth||null,scale:i.scale||null,dpiX:i.dpiX||null,dpiY:i.dpiY||null,height:i.height||null,width:i.width||null,setBrightness:function(e,t){o.execSync("Device","setBrightness",[e,t])},lockOrientation:function(e){o.exec("Device","lockOrientation",[e])},unlockOrientation:function(){o.exec("Device","unlockOrientation",[])},getBrightness:function(e){return o.execSync("Device","getBrightness",[e])},getCureentSize:function(){return o.execSync("Device","getCurrentSize",[])},getCurrentSize:function(){return o.execSync("Device","getCurrentSize",[])},updateInfo:function(e){e&&(this.resolutionHeight=e.resolutionHeight,this.resolutionWidth=e.resolutionWidth,this.scale=e.scale,this.dpiX=e.dpiX,this.dpiY=e.dpiY,this.width=e.width,this.height=e.height)}}}),PlusObject.register("display",function(e,t,n,i){return{resolutionHeight:(i=i||{}).resolutionHeight||null,resolutionWidth:i.resolutionWidth||null,updateInfo:function(e){e&&(this.resolutionHeight=e.resolutionHeight,this.resolutionWidth=e.resolutionWidth)}}}),PlusObject.register("networkinfo",function(e,t,n,i){var o=t.require("bridge");return{CONNECTION_TYPE:i.CONNECTION_TYPE,CONNECTION_UNKNOW:0,CONNECTION_NONE:1,CONNECTION_ETHERNET:2,CONNECTION_WIFI:3,CONNECTION_CELL2G:4,CONNECTION_CELL3G:5,CONNECTION_CELL4G:6,CONNECTION_CELL4G:7,getCurrentType:function(){return o.execSync("Device","getCurrentType",null)},getCurrentAPN:function(){return o.execSync("Device","getCurrentAPN",null)},isSetProxy:function(){return o.execSync("Device","isSetProxy",null)}}}),PlusObject.register("fingerprint",function(e,t,n,i){var o=(t=t).bridge;t.fingerprint={isSupport:function(){return o.execSync("fingerprint","isSupport")},isEnrolledFingerprints:function(){return o.execSync("fingerprint","isEnrolledFingerprints")},isKeyguardSecure:function(){return o.execSync("fingerprint","isKeyguardSecure")},authenticate:function(e,t,n){var i="function"!=typeof e?null:function(t){e(t)},r="function"!=typeof t?null:function(e){t(new function(e,t){this.code=e,this.message=t,this.UNSUPPORT=1,this.KEYGUARD_INSECURE=2,this.FINGERPRINT_UNENROLLED=3,this.AUTHENTICATE_MISMATCH=4,this.AUTHENTICATE_OVERLIMIT=5,this.CANCEL=6,this.UNKNOWN_ERROR=7}(e.code,e.message))};callbackID=o.callbackId(i,r),o.exec("fingerprint","authenticate",[callbackID,n])},cancel:function(){return o.exec("fingerprint","cancel")}}}),PlusObject.register("video",function(e,t){t.__Media_Live__Push__=function(){var e=[];return{pushCallback_LivePush:function(t,n,i,o,r){e[r]={fun:n,nokeep:i,pushobj:o}},execCallback_LivePush:function(t,n,i){if(e[i]){var o={};o.type=t,o.target=e[i].pushobj,o.detail=n,e[i].fun&&e[i].fun(o)}}}}();var n=t.bridge,i="VideoPlayer",o="LivePusher",r=t.tools,s={},a={};function c(e){return r.getElementOffsetXInWebview(e)}function u(e){return r.getElementOffsetYInWebview(e)}function l(o,s,a,l){this.id=r.UUID("dt"),this.IDENTITY=i,this.userId=a,div=e.document&&document.getElementById(o);var _=this.id;if(!l){var f=[];div&&(t.tools.platform==t.tools.ANDROID?document.addEventListener("plusorientationchange",function(){setTimeout(function(){var e=[c(div),u(div),div.offsetWidth,div.offsetHeight];n.exec(i,"resize",[_,e])},200)},!1):div.addEventListener("resize",function(){var e=[c(div),u(div),div.offsetWidth,div.offsetHeight];n.exec(i,"resize",[_,e])},!1),f=[c(div),u(div),div.offsetWidth,div.offsetHeight]),n.exec(i,"VideoPlayer",[this.id,f,s,a])}}var _=l.prototype;function f(i,r,s,a){this.IDENTITY="LivePusher",this.id=s,this.__uuid__=t.tools.UUID("LivePusher"),this.options=r,this.onCapture=null,me=this;var l=null;null!=s&&void 0!=s||(this.id=this.__uuid__);var _=e.document&&document.getElementById(i);null!=_&&void 0!=_&&(t.tools.ANDROID==t.tools.platform?window.onresize=function(){var e=document.getElementById(i),t=[c(e),u(e),e.offsetWidth,e.offsetHeight];n.exec(o,"resize",[me.__uuid__,t])}:t.tools.IOS==t.tools.platform&&_.addEventListener("resize",function(){var e=[c(_),u(_),_.offsetWidth,_.offsetHeight];n.exec(o,"resize",[me.__uuid__,e])},!1),l=[c(_),u(_),_.offsetWidth,_.offsetHeight]);var f=!0;void 0==a||null==a||a||(f=!1),f&&n.exec(o,"LivePusher",[this.__uuid__,this.id,l,r])}_.play=function(){n.exec(i,"VideoPlayer_play",[this.id])},_.pause=function(){n.exec(i,"VideoPlayer_pause",[this.id])},_.stop=function(){n.exec(i,"VideoPlayer_stop",[this.id])},_.close=function(){n.exec(i,"VideoPlayer_close",[this.id])},_.sendDanmu=function(e){n.exec(i,"VideoPlayer_sendDanmu",[this.id,e])},_.seek=function(e){n.exec(i,"VideoPlayer_seek",[this.id,e])},_.playbackRate=function(e){n.exec(i,"VideoPlayer_playbackRate",[this.id,e])},_.requestFullScreen=function(e){n.exec(i,"VideoPlayer_requestFullScreen",[this.id,e])},_.exitFullScreen=function(){n.exec(i,"VideoPlayer_exitFullScreen",[this.id])},_.hide=function(){n.exec(i,"VideoPlayer_hide",[this.id])},_.show=function(){n.exec(i,"VideoPlayer_show",[this.id])},_.hideStatusBar=function(){n.exec(i,"VideoPlayer_hideStatusBar",[this.id])},_.showStatusBar=function(){n.exec(i,"VideoPlayer_showStatusBar",[this.id])},_.setOptions=function(e){n.exec(i,"VideoPlayer_setOptions",[this.id,e])},_.setStyles=_.setOptions,_.addEventListener=function(e,o){var r,s=this;if(o){var a=function(e){"function"==typeof o&&(e.target=s,o(e))};o.callback=a,r=n.callbackId(a)}n.exec(i,"VideoPlayer_addEventListener",[this.id,e,r,t.__HtMl_Id__])};var d=f.prototype;function p(e,o){o&&o.onCapture&&"function"==typeof o.onCapture&&(o.__onCaptureCallbackId__=n.callbackId(o.onCapture));var r=document.getElementById(e);t.tools.ANDROID==t.tools.platform?window.onresize=function(){var t=document.getElementById(e),o=[c(t),u(t),t.offsetWidth,t.offsetHeight];n.exec(i,"resize",o)}:t.tools.IOS==t.tools.platform&&r.addEventListener("resize",function(){var e=[c(r),u(r),r.offsetWidth,r.offsetHeight];n.exec(i,"resize",e)},!1);var s=[r.offsetLeft,r.offsetTop,r.offsetWidth,r.offsetHeight];n.exec(i,"VideoCapture",[s,o])}d.start=function(e,t){var i="function"!=typeof e?null:function(t){e()},r="function"!=typeof t?null:function(e){t(e)},s=n.callbackId(i,r);n.exec(o,"start",[this.__uuid__,s])},d.preview=function(){n.exec(o,"preview",[this.__uuid__])},d.stop=function(e){n.exec(o,"stop",[this.__uuid__,e])},d.pause=function(){n.exec(o,"pause",[this.__uuid__])},d.close=function(){n.exec(o,"close",[this.__uuid__])},d.resume=function(){n.exec(o,"resume",[this.__uuid__])},d.close=function(){n.exec(o,"close",[this.__uuid__])},d.setStyles=function(e){n.exec(o,"setOptions",[this.__uuid__],e)},d.setOptions=function(e){n.exec(o,"setOptions",[this.__uuid__],e)},d.switchCamera=function(e){n.exec(o,"switchCamera",[this.__uuid__])},d.addEventListener=function(e,i,r){var s,a=this;if(i){var c=function(e){"function"==typeof callback&&(e.target=a,i(e))};i.listener=c,s=n.callbackId(c)}t.__Media_Live__Push__.pushCallback_LivePush(e,i,r,this,s),n.exec(o,"addEventListener",[this.__uuid__,t.__HtMl_Id__,e,s])},d.snapshot=function(e,t){var i="function"!=typeof e?null:function(t){e(t)},r="function"!=typeof t?null:function(e){t(e)},s=n.callbackId(i,r);n.exec(o,"snapshot",[this.__uuid__,s])};var h=p.prototype;function y(e,t){t&&t.video&&(t.video.onCaptionChanged&&"function"==typeof t.video.onCaptionChanged&&(t.video.__onCaptionChangedCallbackId__=n.callbackId(t.onCapture)),t.video.onWatermarkChanged&&"function"==typeof t.video.onWatermarkChanged&&(t.video.__onWatermarkChangedCallbackId__=n.callbackId(t.onCapture))),div=document.getElementById(e),div.addEventListener("resize",function(){var e=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight];n.exec(_BARCODE,"resize",[e])},!1);var o=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight];n.exec(i,"VideoEditor",[o,t])}h.setFilter=function(e){n.exec(i,"VideoCapture_setFilter",[e])},h.setFacing=function(e){n.exec(i,"VideoCapture_setFacing",[e])},h.setResolution=function(e){n.exec(i,"VideoCapture_setResolution",[e])},h.start=function(){n.exec(i,"VideoCapture_start",[])},h.stop=function(){n.exec(i,"VideoCapture_stop",[])},h.getSupportedResolutions=function(){return n.execSync(i,"VideoCapture_getSupportedResolutions",[])},h.close=function(){n.exec(i,"VideoCapture_close",[]),this.isClose=!0};var v=y.prototype;v.setCaption=function(e,t){n.exec(i,"VideoEditor_setCaption",[e,t])},v.setWatermark=function(e,t){n.exec(i,"VideoEditor_setWatermark",[e,t])},v.setIndex=function(e){n.exec(i,"VideoEditor_setIndex",[e])},v.play=function(){n.exec(i,"VideoEditor_play",[])},v.pause=function(){n.exec(i,"VideoEditor_pause",[])},v.close=function(){n.exec(i,"VideoEditor_close",[]),this.isClose=!0};var g={VideoPlayer:l,createVideoPlayer:function(e,t){var n=new l(null,t,e);return a[n.id]=n,a[n.id]},getVideoPlayerById:function(e){if(e&&"string"==typeof e){var o=n.execSync(i,"getVideoPlayerById",[e]);if(null!=o&&null!=o.uid){if(a[o.uid])return a[o.uid];if(null!=o&&void 0!=o){var r=new t.video.VideoPlayer(null,null,o.name,!0);return r.id=o.uid,a[o.uid]=r,r}return null}}},createLivePusher:function(e,n,i){var o=!0;void 0==i||null==i||i||(o=!1);var r=new t.video.LivePusher(null,n,e,o);return s[r.__uuid__]=r,s[r.__uuid__]},getLivePusherById:function(e){if(e&&"string"==typeof e){var i=n.execSync(o,"getLivePusherById",[e]);if(null!=i&&void 0!=i&&i.uuid){if(s[i.uuid])return s[i.uuid];var r=new t.video.LivePusher(null,null,e,!1);return r.__uuid__=i.uuid,s[i.uuid]=r,r}return null}},VideoCapture:p,VideoEditor:y,LivePusher:f,getSupportedResolutions:function(){return n.execSync(i,"getSupportedResolutions",[])},getVideoInfo:function(e,t){var o=null;t&&"function"==typeof t&&(o=n.callbackId(t)),n.exec(i,"getVideoInfo",[e,o])}};t.video=g}),PlusObject.register("bluetooth",function(e,t,n,i){var o=t.bridge,r={closeBluetoothAdapter:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","closeBluetoothAdapter",[callbackID])},openBluetoothAdapter:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","openBluetoothAdapter",[callbackID])},getBluetoothAdapterState:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","getBluetoothAdapterState",[callbackID])},getBluetoothDevices:function(e){var t=this,n="function"!=typeof e.complete?function(){}:e.complete,i="function"!=typeof e.success?null:function(i){for(var o=i.devices,r=0;r<o.length;r++){var s=o[r];if(s.advertisData&&"string"==typeof s.advertisData&&"{}"!=s.advertisData&&(s.advertisData=t.string2ArrayBuffer(s.advertisData)),s.serviceData&&"{}"!=s.serviceData)for(var a in s.serviceData)"string"==typeof s.serviceData[a]&&""!=s.serviceData[a]&&(s.serviceData[a]=t.string2ArrayBuffer(s.serviceData[a]))}e.success(i),n(i)},r="function"!=typeof e.fail?null:function(t){e.fail(t),n(t)},s=o.callbackId(i,r);return o.exec("Bluetooth","getBluetoothDevices",[s])},getConnectedBluetoothDevices:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","getConnectedBluetoothDevices",[callbackID,e])},startBluetoothDevicesDiscovery:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","startBluetoothDevicesDiscovery",[callbackID,e])},stopBluetoothDevicesDiscovery:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","stopBluetoothDevicesDiscovery",[callbackID])},onBluetoothAdapterStateChange:function(e){return callbackID=this.getCallbackIDByFunction(e),o.exec("Bluetooth","onBluetoothAdapterStateChange",[callbackID])},onBluetoothDeviceFound:function(e){var t=this,n="function"!=typeof e?null:function(n){for(var i=n.devices,o=0;o<i.length;o++){var r=i[o];if(r.advertisData&&"string"==typeof r.advertisData&&"{}"!=r.advertisData&&(r.advertisData=t.string2ArrayBuffer(r.advertisData)),r.serviceData&&"{}"!=r.serviceData)for(var s in r.serviceData)"string"==typeof r.serviceData[s]&&""!=r.serviceData[s]&&(r.serviceData[s]=t.string2ArrayBuffer(r.serviceData[s]))}e(n)};return callbackID=o.callbackId(n),o.exec("Bluetooth","onBluetoothDeviceFound",[callbackID])},createBLEConnection:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","createBLEConnection",[callbackID,e])},closeBLEConnection:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","closeBLEConnection",[callbackID,e])},setBLEMTU:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","setBLEMTU",[callbackID,e])},getBLEDeviceRSSI:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","getBLEDeviceRSSI",[callbackID,e])},getBLEDeviceCharacteristics:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","getBLEDeviceCharacteristics",[callbackID,e])},getBLEDeviceServices:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","getBLEDeviceServices",[callbackID,e])},notifyBLECharacteristicValueChange:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","notifyBLECharacteristicValueChange",[callbackID,e])},onBLECharacteristicValueChange:function(e){var t=this,n="function"!=typeof e?null:function(n){n.value&&"string"==typeof n.value&&(n.value=t.string2ArrayBuffer(n.value)),e(n)};return callbackID=o.callbackId(n),o.exec("Bluetooth","onBLECharacteristicValueChange",[callbackID])},onBLEConnectionStateChange:function(e){return callbackID=this.getCallbackIDByFunction(e),o.exec("Bluetooth","onBLEConnectionStateChange",[callbackID])},readBLECharacteristicValue:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),o.exec("Bluetooth","readBLECharacteristicValue",[callbackID,e])},writeBLECharacteristicValue:function(e){if("string"!=typeof e.value)return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),e.value=this.ab2hex(e.value),o.exec("Bluetooth","writeBLECharacteristicValue",[callbackID,e]);e.fail({code:10019,message:"Value cannot be a string"})},ab2hex:function(e){return Array.prototype.map.call(new Uint8Array(e),function(e){return("00"+e.toString(16)).slice(-2)}).join("")},string2ArrayBuffer:function(e){return new Uint8Array(e.match(/[\da-f]{2}/gi).map(function(e){return parseInt(e,16)})).buffer},getCallbackIDByFunction:function(e,t,n){n="function"!=typeof n?function(){}:n;var i="function"!=typeof e?null:function(t){e(t),n(t)},r="function"!=typeof t?null:function(e){t(e),n(e)};return o.callbackId(i,r)}};t.bluetooth=r}),PlusObject.register("beacon",function(e,t,n,i){var o=t.bridge,r={startBeaconDiscovery:function(e){if("object"==typeof e){var t="function"!=typeof e.complete?function(){}:e.complete,n="function"!=typeof e.success?t:function(n){e.success(n),t(n)},i="function"!=typeof e.fail?t:function(n){e.fail(n),t(n)};return callbackID=o.callbackId(n,i),o.exec("iBeacon","startBeaconDiscovery",[callbackID,e.uuids])}},stopBeaconDiscovery:function(e){var t="function"!=typeof e.complete?function(){}:e.complete,n="function"!=typeof e.success?t:function(n){e.success(n),t(n)},i="function"!=typeof e.fail?t:function(n){e.fail(n),t(n)};return callbackID=o.callbackId(n,i),o.exec("iBeacon","stopBeaconDiscovery",[callbackID])},getBeacons:function(e){var t="function"!=typeof e.complete?function(){}:e.complete,n="function"!=typeof e.success?t:function(n){e.success(n),t(n)},i="function"!=typeof e.fail?t:function(n){e.fail(n),t(n)};return callbackID=o.callbackId(n,i),o.exec("iBeacon","getBeacons",[callbackID])},onBeaconUpdate:function(e){var t="function"!=typeof e?null:e;return callbackID=o.callbackId(t),o.exec("iBeacon","onBeaconUpdate",[callbackID])},onBeaconServiceChange:function(e){var t="function"!=typeof e?null:e;return callbackID=o.callbackId(t),o.execSync("iBeacon","onBeaconServiceChange",[callbackID])}};t.ibeacon=r}),PlusObject.register("sqlite",function(e,t,n,i){var o=t.bridge,r={isOpenDatabase:function(e){return o.execSync("Sqlite","isOpenDatabase",[e])},openDatabase:function(e){if(e&&e.name&&e.path)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),o.exec("Sqlite","openDatabase",[callbackID,e.name,e.path])},closeDatabase:function(e){if(e&&e.name)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),o.exec("Sqlite","closeDatabase",[callbackID,e.name])},transaction:function(e){if(e&&e.name&&e.operation)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),o.exec("Sqlite","transaction",[callbackID,e.name,e.operation])},executeSql:function(e){if(e&&e.name&&e.sql)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),o.exec("Sqlite","executeSql",[callbackID,e.name,e.sql])},selectSql:function(e){if(e&&e.name&&e.sql)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),o.exec("Sqlite","selectSql",[callbackID,e.name,e.sql])},getCallbackIDByFunction:function(e,t,n){var i="function"!=typeof e?null:function(t){e(t)},r="function"!=typeof t?null:function(e){t(e)};return o.callbackId(i,r)}};t.sqlite=r}),PlusObject.register("ad",function(e,t,n,i){var o="Ad",r=t.bridge,s=t.tools,a={},c={},u={};function l(e,t,n){this.__id__=s.UUID("Bitmap"),this.id=e,this.type="bitmap",n||(c[this.__id__]=this,r.exec(o,"Bitmap",[this.__id__,e,t]))}function _(e,t,n,i,c,u){this.__id__=e||s.UUID("adView"),this.id=this.__id__,this.__uuid__=s.UUID(this.id),this.type="UniAdView",this.IDENTITY=o;var l={};l.top=t,l.left=n,l.width=i,l.height=c,l.position=u,r.exec(o,"createAdView",[this.__id__,this.__uuid__,l,this.type]),a[this.__uuid__]=this}function f(e,t,n){var i="function"!=typeof t?null:function(e){t(e)},s="function"!=typeof n?null:function(e){n(e)},a=r.callbackId(i,s);r.exec(o,"getAds",[a],e)}function d(e,t){var n=null;for(var i in u){var o=u[i];if(t){if(o.uid&&o.uid==e||o.uuid&&o.uuid==e){n=o;break}}else if(o.id==e){n=o;break}}return n}function p(e){var n=a[e.uuid];return n||((n=new t.ad.View(e.id,e.styles,"",!0)).__uuid__=e.uuid,a[n.__uuid__]=n),n}function h(){var e=this;this.__cbs__={},this.callbackId=r.callbackId(function(t){var n=t.evt,i=t.args,o=e.__cbs__[n];if(o){var r=o.length,s=0;for(s=0;s<r;s++)o[s](i)}})}function y(e){this.__uuid__=s.UUID("RewardedVideoAd"),this.__cbs__=new h,r.exec(o,"RewardedVideoAd",[this.__uuid__,e,this.__cbs__.callbackId])}return l.prototype.clear=function(){r.exec(o,"clear",[this.__id__]),this.__id__=void 0,c[this.__id__]&&delete c[this.__id__]},l.prototype.recycle=function(){r.exec(o,"bitmapRecycle",[this.__id__])},l.prototype.load=function(e,t,n){var i=function(e){"function"==typeof n&&n(e)};if(this.__id__){var s=r.callbackId(function(){"function"==typeof t&&t()},i);r.exec(o,"load",[this.__id__,e,s])}else i({code:-1,message:"已经销毁的对象"})},l.prototype.loadBase64Data=function(e,t,n){var i=function(e){"function"==typeof n&&n(e)};if(this.__id__){var s=r.callbackId(function(){"function"==typeof t&&t()},i);r.exec(o,"loadBase64Data",[this.__id__,e,s])}else i({code:-1,message:"已经销毁的对象"})},l.prototype.save=function(e,t,n,i){var s=function(e){"function"==typeof i&&i(e)};if(this.__id__){var a=r.callbackId(function(e){if("function"==typeof n){var t={target:e.path,width:e.w,height:e.h,size:e.size};n(t)}},s);r.exec(o,"save",[this.__id__,e,t,a])}else s({code:-1,message:"已经销毁的对象"})},l.prototype.__captureWebview=function(e,t,n,i){var s=function(e){"function"==typeof i&&i(e)};if(this.__id__){var a=r.callbackId(function(){"function"==typeof n&&n()},s);r.exec(o,"captureWebview",[this.__id__,e,a,t])}else s({code:-1,message:"已经销毁的对象"})},l.prototype.toBase64Data=function(){return this.__id__?r.execSync(o,"toBase64Data",[this.__id__]):null},l.getItems=function(){for(var e=[],t=r.execSync(o,"getItems",[]),n=0;n<t.length;n++){var i=t[n].__id__,s=c[i];if(!s){var a=new l(t[n].id,null,!0);a.__id__=i,c[i]=a,s=a}e.push(s)}return e},l.getBitmapById=function(e){var t=r.execSync(o,"getBitmapById",[e]);if(t){var n=c[t.__id__];if(!n){var i=new l(t.id,null,!0);i.__id__=t.__id__,c[t.__id__]=i,n=i}return n}return null},_.prototype.bind=function(e){r.exec(o,"bind",[this.__id__,this.__uuid__,e])},_.prototype.renderingBind=function(e){r.exec(o,"renderingBind",[this.__id__,this.__uuid__,e])},_.prototype.setDownloadListener=function(e){var t="function"!=typeof e?null:function(t){e(t)},n=r.callbackId(t);r.exec(o,"setDownloadListener",[this.__id__,this.__uuid__,n])},_.prototype.changeDownloadStatus=function(){r.exec(o,"changeDownloadStatus",[this.__id__,this.__uuid__])},_.prototype.setRenderingListener=function(e){var t="function"!=typeof e?null:function(t){e(t)},n=r.callbackId(t);r.exec(o,"setRenderingListener",[this.__id__,this.__uuid__,n])},_.prototype.setAdClickedListener=function(e){var t="function"!=typeof e?null:function(t){e(t)},n=r.callbackId(t);r.exec(o,"setAdClickedListener",[this.__id__,this.__uuid__,n])},_.prototype.setDislikeListener=function(e){var t="function"!=typeof e?null:function(t){e(t)},n=r.callbackId(t);r.exec(o,"setDislikeListener",[this.__id__,this.__uuid__,n])},_.prototype.setImages=function(e){r.exec(o,"setImages",[this.__id__,this.__uuid__,e])},_.prototype.currentImageIndex=function(){return r.execSync(o,"currentImageIndex",[this.__id__,this.__uuid__])},_.prototype.addImages=function(e){r.exec(o,"addImages",[this.__id__,this.__uuid__,e])},_.prototype.drawBitmap=function(e,t,n,i){r.exec(o,"drawBitmap",[this.__id__,this.__uuid__,e,t,n,i])},_.prototype.drawText=function(e,t,n,i){r.exec(o,"drawText",[this.__id__,this.__uuid__,e,t,n,i])},_.prototype.drawRichText=function(e,t,n,i){n&&n.onClick&&"function"==typeof n.onClick&&(n.__onClickCallBackId__=r.callbackId(n.onClick)),r.exec(o,"drawRichText",[this.__id__,this.__uuid__,e,t,n,i])},_.prototype.drawInput=function(e,t,n){t.onComplete&&"function"==typeof t.onComplete&&(t.__onCompleteCallBackId__=r.callbackId(t.onComplete)),t.onFocus&&"function"==typeof t.onFocus&&(t.__onFocusCallBackId__=r.callbackId(t.onFocus)),t.onBlur&&"function"==typeof t.onBlur&&(t.__onBlurCallBackId__=r.callbackId(t.onBlur)),r.exec(o,"drawInput",[this.__id__,this.__uuid__,e,t,n])},_.prototype.getInputValueById=function(e){return r.execSync(o,"getInputValueById",[this.__id__,this.__uuid__,e])},_.prototype.getInputFocusById=function(e){return r.execSync(o,"getInputFocusById",[this.__id__,this.__uuid__,e])},_.prototype.setInputFocusById=function(e,t){r.exec(o,"setInputFocusById",[this.__id__,this.__uuid__,e,t])},_.prototype.show=function(){r.exec(o,"show",[this.__id__,this.__uuid__])},_.prototype.setStyle=function(e){r.exec(o,"setStyle",[this.__id__,this.__uuid__,e])},_.prototype.hide=function(){r.exec(o,"hide",[this.__id__,this.__uuid__])},_.prototype.clear=function(){this.close()},_.prototype.close=function(){r.exec(o,"view_close",[this.__id__,this.__uuid__]),delete a[this.__uuid__]},_.prototype.animate=function(e,t){var n;t&&(n=r.callbackId(function(){"function"==typeof t&&t()})),r.exec(o,"view_animate",[this.__id__,this.__uuid__,e,n])},_.prototype.reset=function(){r.exec(o,"view_reset",[this.__id__,this.__uuid__])},_.prototype.restore=function(){r.exec(o,"view_restore",[this.__id__,this.__uuid__])},_.prototype.isVisible=function(){return r.execSync(o,"isVisible",[this.__id__,this.__uuid__])},_.prototype.drawRect=function(e,t,n){r.exec(o,"view_drawRect",[this.__id__,this.__uuid__,e,t,n])},_.prototype.interceptTouchEvent=function(e){r.exec(o,"interceptTouchEvent",[this.__id__,this.__uuid__,e])},_.prototype.addEventListener=function(e,t){var n,i=this;if(t){var s=function(e){"function"==typeof t&&(e.target=i,t(e))};t.callback=s,n=r.callbackId(s)}r.exec(o,"addEventListener",[this.__id__,this.__uuid__,e,n])},_.prototype.setTouchEventRect=function(e){r.exec(o,"setTouchEventRect",[this.__id__,this.__uuid__,e])},_.getViewById=function(e){var t=r.execSync(o,"getViewById",[e]);return t||(t=d(e,!1)),t?p(t):null},_.__getViewByUidAndCreate=function(e){return p(e)},_.__getViewByUidFromCache=function(e){var t=d(e,!0);return t?p(t):null},_.startAnimation=function(e,t,n,i){var s,a,c;e&&t&&(a=t instanceof _?{viewId:t.__uuid__}:{uuid:t.bitmap?t.bitmap.__id__:null,texts:{value:t.text,textStyles:t.textStyles,textRect:t.textRect}},n&&(c=n instanceof _?{viewId:n.__uuid__}:{uuid:n.bitmap?n.bitmap.__id__:null,texts:{value:n.text,textStyles:n.textStyles,textRect:n.textRect}}),i&&(s=r.callbackId(function(){"function"==typeof i&&i()})),r.exec(o,"startAnimation",[e,a,c,s]))},_.clearAnimation=function(e){e||(e="none"),r.exec(o,"clearAnimation",[e])},_.prototype.clearRect=function(e,t){r.exec(o,"view_clearRect",[this.__id__,this.__uuid__,e,t])},_.prototype.draw=function(e){!function(e){if(Array.isArray(e))for(var t,n=0;n<e.length;n++)(t=e[n])&&"input"==t.tag?t.inputStyles&&(t.inputStyles.onComplete&&"function"==typeof t.inputStyles.onComplete&&(t.inputStyles.__onCompleteCallBackId__=r.callbackId(t.inputStyles.onComplete)),t.inputStyles.onFocus&&"function"==typeof t.inputStyles.onFocus&&(t.inputStyles.__onFocusCallBackId__=r.callbackId(t.inputStyles.onFocus)),t.inputStyles.onBlur&&"function"==typeof t.inputStyles.onBlur&&(t.inputStyles.__onBlurCallBackId__=r.callbackId(t.inputStyles.onBlur))):t&&"richtext"==t.tag&&t.richTextStyles&&t.richTextStyles.onClick&&"function"==typeof t.richTextStyles.onClick&&(t.richTextStyles.__onClickCallBackId__=r.callbackId(t.richTextStyles.onClick))}(e),r.exec(o,"view_draw",[this.__id__,this.__uuid__,e])},h.prototype.push=function(e,t){this.__cbs__[e]||(this.__cbs__[e]=[]),this.__cbs__[e].push(t)},h.prototype.pop=function(e,t){this.__cbs__[e]&&this.__cbs__[e].pop(t)},y.prototype.load=function(){r.exec(o,"load",[this.__uuid__])},y.prototype.show=function(){r.exec(o,"show",[this.__uuid__])},y.prototype.getProvider=function(){return r.execSync(o,"getProvider",[this.__uuid__])},y.prototype.destroy=function(){r.exec(o,"destroy",[this.__uuid__])},y.prototype.onLoad=function(e){this.__cbs__.push("load",e)},y.prototype.offLoad=function(e){this.__cbs__.pop("load",e)},y.prototype.onError=function(e){this.__cbs__.push("error",e)},y.prototype.offError=function(e){this.__cbs__.pop("error",e)},y.prototype.onClose=function(e){this.__cbs__.push("close",e)},y.prototype.offClose=function(e){this.__cbs__.pop("close",e)},y.prototype.onVerify=function(e){this.__cbs__.push("verify",e)},y.prototype.offVerify=function(e){this.__cbs__.pop("verify",e)},y.prototype.onAdClicked=function(e){this.__cbs__.push("adClicked",e)},y.prototype.offAdClicked=function(e){this.__cbs__.pop("adClicked",e)},{Bitmap:l,View:_,createAdView:function(e){return new t.ad.View(e.id,e.top,e.left,e.width,e.height,e.position)},releaseAdData:function(e){r.exec(o,"releaseAdData",[e])},measureAdHeight:function(e,t){return r.execSync(o,"measureAdHeight",[e,t])},getAds:f,getDrawAds:function(e,t,n){e._t_="draw_flow",f(e,t,n)},getProviders:function(e,t){var n="function"!=typeof e?null:function(t){e(t)},i="function"!=typeof t?null:function(e){t(e)},s=r.callbackId(n,i);r.exec(o,"getProviders",[s],null)},__createNView:function(e,n,i,o){var r=new t.ad.View(n,i,o);return r.__uuid__=e,a[r.__uuid__]=r,r},createRewardedVideoAd:function(e){return new y(e=e||{})},createFullScreenVideoAd:function(e){return(e=e||{}).__type="fullScreenVideo",new y(e)},createInterstitialAd:function(e){return(e=e||{}).__type="InterstitialAd",new y(e)},setSplashAd:function(e,t,n){var i=r.callbackId(t,n);r.exec(o,"setSplashAd",[e,i])},getSplashAd:function(){return r.execSync(o,"getSplashAd",null)},setPersonalizedAd:function(e){r.exec(o,"setPersonalizedAd",[e])},getPersonalizedAd:function(){return r.execSync(o,"getPersonalizedAd",null)},setPrivacyConfig:function(e){r.execSync(o,"setPrivacyConfig",[e])},showContentPage:function(e,t,n){var i=r.callbackId(t,n);r.exec(o,"showContentPage",[e,i])},__appendSubViewInfo:function(e){u[e.uuid]=e},__removeSubViewInfos:function(e){for(var t=0;t<e.length;t++)delete u[e[t]]}}});