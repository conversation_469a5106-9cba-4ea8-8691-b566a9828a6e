// 检查悬浮窗权限
if (!floaty.checkPermission()) {
  toast("本脚本需要悬浮窗权限来显示悬浮窗，请在随后的界面中允许并重新运行本脚本。");
  floaty.requestPermission();
  exit();
}

// 创建主界面
ui.layout(
  <frame>
    <webview id="webview" />
  </frame>
);

const webView = ui.webview;
const jsBridge = webView.jsBridge;

declare var __DevIp__: string;
if (typeof __DevIp__ === "string") {
  webView.loadUrl(`http://${__DevIp__}:5173/`);
} else {
  webView.loadLocalFile(files.path("./website"));
}

// 创建悬浮窗
let floatyWindow: any = null;
let isExpanded = false;
let screenWidth = 1080; // 默认屏幕宽度
let screenHeight = 1920; // 默认屏幕高度
let autoCollapseTimer: any = null;
let isScriptPaused = false;

// 获取屏幕尺寸
try {
  const size = shell("wm size", true).result;
  if (size && size.includes("x")) {
    const parts = size.split(": ")[1].split("x");
    screenWidth = parseInt(parts[0]);
    screenHeight = parseInt(parts[1]);
  }
} catch (e) {
  console.log("无法获取屏幕尺寸，使用默认值");
}

// 创建悬浮窗布局
function createFloatyWindow() {
  floatyWindow = floaty.rawWindow(
    <frame id="main" w="wrap_content" h="wrap_content">
      {/* 主图标 - 圆形悬浮球 */}
      <frame id="iconFrame" w="60dp" h="60dp" bg="#ff4081" gravity="center"
             margin="0dp" alpha="0.9">
        <text text="�" textSize="24sp" textColor="#ffffff" gravity="center"/>
      </frame>

      {/* 展开的功能面板 - 水平布局 */}
      <horizontal id="expandedPanel" visibility="gone"
                  bg="#ee000000" padding="5dp" gravity="center" alpha="0.95">

        {/* 启动按钮 */}
        <vertical id="startBtn" w="50dp" h="70dp" bg="#4caf50" margin="3dp"
                  gravity="center" clickable="true">
          <text text="▶" textSize="18sp" textColor="#ffffff" gravity="center"/>
          <text text="启动" textSize="8sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 暂停按钮 */}
        <vertical id="pauseBtn" w="50dp" h="70dp" bg="#ff9800" margin="3dp"
                  gravity="center" clickable="true">
          <text text="⏸" textSize="18sp" textColor="#ffffff" gravity="center"/>
          <text text="暂停" textSize="8sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 设置按钮 */}
        <vertical id="settingsBtn" w="50dp" h="70dp" bg="#2196f3" margin="3dp"
                  gravity="center" clickable="true">
          <text text="⚙" textSize="18sp" textColor="#ffffff" gravity="center"/>
          <text text="设置" textSize="8sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 日志按钮 */}
        <vertical id="logBtn" w="50dp" h="70dp" bg="#9c27b0" margin="3dp"
                  gravity="center" clickable="true">
          <text text="📋" textSize="18sp" textColor="#ffffff" gravity="center"/>
          <text text="日志" textSize="8sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 退出按钮 */}
        <vertical id="exitBtn" w="50dp" h="70dp" bg="#f44336" margin="3dp"
                  gravity="center" clickable="true">
          <text text="⏻" textSize="18sp" textColor="#ffffff" gravity="center"/>
          <text text="退出" textSize="8sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

      </horizontal>
    </frame>
  );

  // 设置悬浮窗属性 - 保持在最顶层
  floatyWindow.setSize(-2, -2);
  floatyWindow.setPosition(screenWidth - 80, screenHeight / 2); // 初始位置在右侧
  floatyWindow.setTouchable(true);

  return floatyWindow;
}

// 智能展开菜单 - 根据位置决定展开方向
function expandMenu() {
  if (!floatyWindow || isExpanded) return;

  const currentX = floatyWindow.getX();
  const currentY = floatyWindow.getY();
  const isOnLeftSide = currentX < screenWidth / 2;

  isExpanded = true;

  ui.run(() => {
    floatyWindow.iconFrame.setVisibility(8); // 隐藏图标
    floatyWindow.expandedPanel.setVisibility(0); // 显示面板

    // 根据位置调整面板位置
    if (isOnLeftSide) {
      // 在左侧，向右展开
      floatyWindow.setPosition(currentX, currentY - 10);
    } else {
      // 在右侧，向左展开
      floatyWindow.setPosition(currentX - 220, currentY - 10); // 220是面板宽度估算
    }
  });

  // 启动自动收起定时器
  startAutoCollapseTimer();
}

// 收起菜单并贴边
function collapseMenu() {
  if (!floatyWindow || !isExpanded) return;

  isExpanded = false;
  clearAutoCollapseTimer();

  const currentX = floatyWindow.getX();
  const currentY = floatyWindow.getY();
  const isOnLeftSide = currentX < screenWidth / 2;

  ui.run(() => {
    floatyWindow.iconFrame.setVisibility(0); // 显示图标
    floatyWindow.expandedPanel.setVisibility(8); // 隐藏面板

    // 贴边动画
    const targetX = isOnLeftSide ? 0 : screenWidth - 60;
    animateToPosition(targetX, currentY);
  });
}

// 位置动画
function animateToPosition(targetX: number, targetY: number) {
  if (!floatyWindow) return;

  const startX = floatyWindow.getX();
  const startY = floatyWindow.getY();
  const duration = 300; // 动画时长300ms
  const steps = 20;
  const stepTime = duration / steps;

  let currentStep = 0;

  const animationTimer = setInterval(() => {
    currentStep++;
    const progress = currentStep / steps;

    // 使用缓动函数
    const easeProgress = 1 - Math.pow(1 - progress, 3);

    const newX = startX + (targetX - startX) * easeProgress;
    const newY = startY + (targetY - startY) * easeProgress;

    ui.run(() => {
      floatyWindow.setPosition(newX, newY);
    });

    if (currentStep >= steps) {
      clearInterval(animationTimer);
    }
  }, stepTime);
}

// 自动收起定时器
function startAutoCollapseTimer() {
  clearAutoCollapseTimer();
  autoCollapseTimer = setTimeout(() => {
    collapseMenu();
  }, 3000); // 3秒后自动收起
}

function clearAutoCollapseTimer() {
  if (autoCollapseTimer) {
    clearTimeout(autoCollapseTimer);
    autoCollapseTimer = null;
  }
}

// 重置自动收起定时器（用户有操作时调用）
function resetAutoCollapseTimer() {
  if (isExpanded) {
    startAutoCollapseTimer();
  }
}

// 创建悬浮窗
createFloatyWindow();

// 主图标点击事件
floatyWindow.iconFrame.on("click", () => {
  expandMenu();
});

// 拖拽相关变量
let isDragging = false;
let startX = 0, startY = 0;
let startTouchX = 0, startTouchY = 0;

// 主图标触摸事件处理
floatyWindow.iconFrame.setOnTouchListener(new android.view.View.OnTouchListener({
  onTouch: function(_view: any, event: any) {
    switch (event.getAction()) {
      case event.ACTION_DOWN:
        isDragging = false;
        startX = floatyWindow.getX();
        startY = floatyWindow.getY();
        startTouchX = event.getRawX();
        startTouchY = event.getRawY();
        return true;

      case event.ACTION_MOVE:
        const deltaX = Math.abs(event.getRawX() - startTouchX);
        const deltaY = Math.abs(event.getRawY() - startTouchY);

        if (!isDragging && (deltaX > 20 || deltaY > 20)) {
          isDragging = true;
        }

        if (isDragging) {
          const newX = startX + (event.getRawX() - startTouchX);
          const newY = startY + (event.getRawY() - startTouchY);

          // 限制在屏幕范围内
          const boundedX = Math.max(0, Math.min(screenWidth - 60, newX));
          const boundedY = Math.max(0, Math.min(screenHeight - 60, newY));

          floatyWindow.setPosition(boundedX, boundedY);
        }
        return true;

      case event.ACTION_UP:
        if (!isDragging) {
          // 如果没有拖拽，则是点击事件
          expandMenu();
        }
        isDragging = false;
        return true;
    }
    return false;
  }
}));

// 功能按钮点击事件
floatyWindow.startBtn.on("click", () => {
  toast("启动脚本");
  resetAutoCollapseTimer();
  if (isScriptPaused) {
    isScriptPaused = false;
    // 这里添加启动脚本的逻辑
  }
});

floatyWindow.pauseBtn.on("click", () => {
  toast(isScriptPaused ? "恢复脚本" : "暂停脚本");
  resetAutoCollapseTimer();
  isScriptPaused = !isScriptPaused;
  // 这里添加暂停/恢复脚本的逻辑
});

floatyWindow.settingsBtn.on("click", () => {
  toast("打开设置");
  collapseMenu();
  // 这里添加打开设置的逻辑
});

floatyWindow.logBtn.on("click", () => {
  toast("查看日志");
  collapseMenu();
  // 这里添加查看日志的逻辑
});

floatyWindow.exitBtn.on("click", () => {
  toast("退出脚本");
  floatyWindow.close();
  exit();
});

// 点击面板外区域收起菜单
floatyWindow.main.on("touch", (event: any) => {
  if (isExpanded && event.getAction() === event.ACTION_DOWN) {
    // 延迟收起菜单，给按钮点击事件时间执行
    setTimeout(() => {
      if (isExpanded) {
        collapseMenu();
      }
    }, 100);
  }
  return false;
});

jsBridge.registerHandler("test", (data: string) => {
  toastLog("你点击了:" + data);
});

jsBridge.registerHandler("exit", () => {
  if (floatyWindow) {
    floatyWindow.close();
  }
  exit();
});

// 保持脚本运行
setInterval(() => {}, 1000);
