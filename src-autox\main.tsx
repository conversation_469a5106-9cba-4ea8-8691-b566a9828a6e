// 检查悬浮窗权限
if (!floaty.checkPermission()) {
  toast("本脚本需要悬浮窗权限来显示悬浮窗，请在随后的界面中允许并重新运行本脚本。");
  floaty.requestPermission();
  exit();
}

// 创建主界面
ui.layout(
  <frame>
    <webview id="webview" />
  </frame>
);

const webView = ui.webview;
const jsBridge = webView.jsBridge;

declare var __DevIp__: string;
if (typeof __DevIp__ === "string") {
  webView.loadUrl(`http://${__DevIp__}:5173/`);
} else {
  webView.loadLocalFile(files.path("./website"));
}

// 创建悬浮窗
let floatyWindow: any = null;
let isExpanded = false;

// 创建悬浮窗布局
function createFloatyWindow() {
  floatyWindow = floaty.rawWindow(
    <vertical id="main" bg="#88000000" gravity="center">
      {/* 主图标 */}
      <frame id="iconFrame" w="60dp" h="60dp" bg="#ff4081" gravity="center"
             margin="5dp" visibility={isExpanded ? "gone" : "visible"}>
        <text text="🐱" textSize="24sp" textColor="#ffffff" gravity="center"/>
      </frame>

      {/* 展开的功能面板 */}
      <horizontal id="expandedPanel" visibility={isExpanded ? "visible" : "gone"}
                  bg="#ee000000" padding="10dp" gravity="center">

        {/* 停止按钮 */}
        <vertical id="stopBtn" w="60dp" h="80dp" bg="#666666" margin="5dp"
                  gravity="center" clickable="true">
          <text text="⏹" textSize="20sp" textColor="#ffffff" gravity="center"/>
          <text text="停止" textSize="10sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 暂停按钮 */}
        <vertical id="pauseBtn" w="60dp" h="80dp" bg="#666666" margin="5dp"
                  gravity="center" clickable="true">
          <text text="⏸" textSize="20sp" textColor="#ffffff" gravity="center"/>
          <text text="暂停" textSize="10sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 设置按钮 */}
        <vertical id="settingsBtn" w="60dp" h="80dp" bg="#666666" margin="5dp"
                  gravity="center" clickable="true">
          <text text="⚙" textSize="20sp" textColor="#ffffff" gravity="center"/>
          <text text="设置" textSize="10sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 日志按钮 */}
        <vertical id="logBtn" w="60dp" h="80dp" bg="#666666" margin="5dp"
                  gravity="center" clickable="true">
          <text text="📋" textSize="20sp" textColor="#ffffff" gravity="center"/>
          <text text="日志" textSize="10sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

        {/* 退出按钮 */}
        <vertical id="exitBtn" w="60dp" h="80dp" bg="#ff5722" margin="5dp"
                  gravity="center" clickable="true">
          <text text="⏻" textSize="20sp" textColor="#ffffff" gravity="center"/>
          <text text="退出" textSize="10sp" textColor="#ffffff" gravity="center" marginTop="2dp"/>
        </vertical>

      </horizontal>
    </vertical>
  );

  // 设置悬浮窗属性
  floatyWindow.setSize(-2, -2); // 根据内容自适应大小
  floatyWindow.setPosition(800, 500); // 初始位置
  floatyWindow.setTouchable(true);

  return floatyWindow;
}

// 更新悬浮窗显示状态
function updateFloatyDisplay() {
  if (!floatyWindow) return;

  ui.run(() => {
    if (isExpanded) {
      floatyWindow.iconFrame.setVisibility(8); // GONE
      floatyWindow.expandedPanel.setVisibility(0); // VISIBLE
    } else {
      floatyWindow.iconFrame.setVisibility(0); // VISIBLE
      floatyWindow.expandedPanel.setVisibility(8); // GONE
    }
  });
}

// 创建悬浮窗
createFloatyWindow();

// 主图标点击事件
floatyWindow.iconFrame.on("click", () => {
  isExpanded = true;
  updateFloatyDisplay();
});

// 功能按钮点击事件
floatyWindow.stopBtn.on("click", () => {
  toast("停止脚本");
  isExpanded = false;
  updateFloatyDisplay();
  // 这里可以添加停止脚本的逻辑
});

floatyWindow.pauseBtn.on("click", () => {
  toast("暂停脚本");
  isExpanded = false;
  updateFloatyDisplay();
  // 这里可以添加暂停脚本的逻辑
});

floatyWindow.settingsBtn.on("click", () => {
  toast("打开设置");
  isExpanded = false;
  updateFloatyDisplay();
  // 这里可以添加打开设置的逻辑
});

floatyWindow.logBtn.on("click", () => {
  toast("查看日志");
  isExpanded = false;
  updateFloatyDisplay();
  // 这里可以添加查看日志的逻辑
});

floatyWindow.exitBtn.on("click", () => {
  toast("退出脚本");
  floatyWindow.close();
  exit();
});

// 点击空白区域收起面板
floatyWindow.main.on("click", (event: any) => {
  if (isExpanded && event.target === floatyWindow.main) {
    isExpanded = false;
    updateFloatyDisplay();
  }
});

jsBridge.registerHandler("test", (data: string) => {
  toastLog("你点击了:" + data);
});

jsBridge.registerHandler("exit", () => {
  if (floatyWindow) {
    floatyWindow.close();
  }
  exit();
});

// 保持脚本运行
setInterval(() => {}, 1000);
