.class final Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;
.super Ljava/lang/Object;
.source "LinkedHashTreeMap.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/gson/internal/LinkedHashTreeMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = "AvlBuilder"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "V:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private leavesSkipped:I

.field private leavesToSkip:I

.field private size:I

.field private stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>()V
    .locals 0

    .line 673
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method add(Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 690
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    const/4 v0, 0x1

    .line 691
    iput v0, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 694
    iget v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesToSkip:I

    if-lez v1, :cond_0

    iget v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->size:I

    and-int/lit8 v3, v2, 0x1

    if-nez v3, :cond_0

    add-int/2addr v2, v0

    .line 695
    iput v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->size:I

    sub-int/2addr v1, v0

    .line 696
    iput v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesToSkip:I

    .line 697
    iget v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    add-int/2addr v1, v0

    iput v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    .line 700
    :cond_0
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 701
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 702
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->size:I

    add-int/lit8 v1, p1, 0x1

    iput v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->size:I

    .line 705
    iget v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesToSkip:I

    const/4 v3, 0x2

    if-lez v2, :cond_1

    and-int/2addr v1, v0

    if-nez v1, :cond_1

    add-int/2addr p1, v3

    .line 706
    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->size:I

    sub-int/2addr v2, v0

    .line 707
    iput v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesToSkip:I

    .line 708
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    add-int/2addr p1, v0

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    :cond_1
    const/4 p1, 0x4

    .line 724
    :goto_0
    iget v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->size:I

    add-int/lit8 v2, p1, -0x1

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_5

    .line 725
    iget v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    if-nez v1, :cond_2

    .line 727
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 728
    iget-object v2, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 729
    iget-object v4, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 730
    iget-object v5, v4, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    iput-object v5, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 731
    iput-object v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 733
    iput-object v4, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 734
    iput-object v1, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 735
    iget v5, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    add-int/2addr v5, v0

    iput v5, v2, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 736
    iput-object v2, v4, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 737
    iput-object v2, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    goto :goto_1

    :cond_2
    const/4 v2, 0x0

    if-ne v1, v0, :cond_3

    .line 740
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 741
    iget-object v4, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 742
    iput-object v4, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 744
    iput-object v1, v4, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 745
    iget v5, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    add-int/2addr v5, v0

    iput v5, v4, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->height:I

    .line 746
    iput-object v4, v1, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 747
    iput v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    goto :goto_1

    :cond_3
    if-ne v1, v3, :cond_4

    .line 749
    iput v2, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    :cond_4
    :goto_1
    mul-int/lit8 p1, p1, 0x2

    goto :goto_0

    :cond_5
    return-void
.end method

.method reset(I)V
    .locals 1

    .line 682
    invoke-static {p1}, Ljava/lang/Integer;->highestOneBit(I)I

    move-result v0

    mul-int/lit8 v0, v0, 0x2

    add-int/lit8 v0, v0, -0x1

    sub-int/2addr v0, p1

    .line 683
    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesToSkip:I

    const/4 p1, 0x0

    .line 684
    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->size:I

    .line 685
    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->leavesSkipped:I

    const/4 p1, 0x0

    .line 686
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    return-void
.end method

.method root()Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 755
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$AvlBuilder;->stack:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    .line 756
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedHashTreeMap$Node;

    if-nez v1, :cond_0

    return-object v0

    .line 757
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method
