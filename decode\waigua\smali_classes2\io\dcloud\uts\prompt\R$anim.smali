.class public final Lio/dcloud/uts/prompt/R$anim;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/prompt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "anim"
.end annotation


# static fields
.field public static uni_app_uni_prompt_anim_slide_in_bottom:I = 0x7f01002b

.field public static uni_app_uni_prompt_anim_slide_out_top:I = 0x7f01002c


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
