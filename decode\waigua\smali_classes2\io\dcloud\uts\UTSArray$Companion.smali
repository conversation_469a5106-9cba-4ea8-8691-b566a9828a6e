.class public final Lio/dcloud/uts/UTSArray$Companion;
.super Ljava/lang/Object;
.source "UTSArray.kt"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/UTSArray;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Companion"
.end annotation

.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nUTSArray.kt\nKotlin\n*S Kotlin\n*F\n+ 1 UTSArray.kt\nio/dcloud/uts/UTSArray$Companion\n+ 2 _Arrays.kt\nkotlin/collections/ArraysKt___ArraysKt\n+ 3 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n+ 4 _Strings.kt\nkotlin/text/StringsKt___StringsKt\n*L\n1#1,1401:1\n13644#2,3:1402\n13644#2,3:1405\n13600#2,2:1417\n13586#2,2:1419\n13607#2,2:1421\n13614#2,2:1423\n13621#2,2:1425\n13593#2,2:1427\n13635#2,2:1429\n13628#2,2:1431\n1864#3,3:1408\n1864#3,3:1411\n1183#4,3:1414\n*S KotlinDebug\n*F\n+ 1 UTSArray.kt\nio/dcloud/uts/UTSArray$Companion\n*L\n188#1:1402,3\n201#1:1405,3\n255#1:1417,2\n263#1:1419,2\n271#1:1421,2\n279#1:1423,2\n287#1:1425,2\n295#1:1427,2\n303#1:1429,2\n311#1:1431,2\n213#1:1408,3\n225#1:1411,3\n239#1:1414,3\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0004\n\u0000\n\u0002\u0010\u0011\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0010\r\n\u0002\u0010\u001c\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0010\u0018\n\u0002\u0010\u0012\n\u0002\u0010\u000c\n\u0002\u0010\u0019\n\u0002\u0010\u0013\n\u0002\u0010\u0014\n\u0002\u0010\u0015\n\u0002\u0010\u0016\n\u0002\u0010\u0017\n\u0002\u0010 \n\u0002\u0008\u0005\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002J\\\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u0002H\u00050\u0004\"\u0004\u0008\u0001\u0010\u00052\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u00072<\u0008\u0002\u0010\u0008\u001a6\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u0002H\u0005\u0018\u00010\tJa\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u0002H\u00050\u0004\"\u0004\u0008\u0001\u0010\u00052\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u000f2<\u0008\u0002\u0010\u0008\u001a6\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u0002H\u0005\u0018\u00010\t\u00a2\u0006\u0002\u0010\u0010Jc\u0010\u0003\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00010\u00042\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u000f2>\u0008\u0002\u0010\u0008\u001a8\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0018\u00010\tH\u0007\u00a2\u0006\u0004\u0008\u0011\u0010\u0010JP\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u00042\u0006\u0010\u0006\u001a\u00020\u00132:\u0008\u0002\u0010\u0008\u001a4\u0012\u0013\u0012\u00110\u0012\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u00020\u0012\u0018\u00010\tJ\\\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u0002H\u00050\u0004\"\u0004\u0008\u0001\u0010\u00052\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u00142<\u0008\u0002\u0010\u0008\u001a6\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u0002H\u0005\u0018\u00010\tJa\u0010\u0003\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00010\u00042\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u00142>\u0008\u0002\u0010\u0008\u001a8\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0018\u00010\tH\u0007\u00a2\u0006\u0002\u0008\u0015Jb\u0010\u0016\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\u00050\u00040\u0017\"\u0004\u0008\u0001\u0010\u00052\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u00072<\u0008\u0002\u0010\u0008\u001a6\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u0002H\u0005\u0018\u00010\tJg\u0010\u0016\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\u00050\u00040\u0017\"\u0004\u0008\u0001\u0010\u00052\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u000f2<\u0008\u0002\u0010\u0008\u001a6\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u0002H\u0005\u0018\u00010\t\u00a2\u0006\u0002\u0010\u0018JV\u0010\u0016\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00120\u00040\u00172\u0006\u0010\u0006\u001a\u00020\u00132:\u0008\u0002\u0010\u0008\u001a4\u0012\u0013\u0012\u00110\u0012\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u00020\u0012\u0018\u00010\tJb\u0010\u0016\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\u00050\u00040\u0017\"\u0004\u0008\u0001\u0010\u00052\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u00142<\u0008\u0002\u0010\u0008\u001a6\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u0002H\u0005\u0018\u00010\tJd\u0010\u0019\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002H\u00050\u00040\u0017\"\u0004\u0008\u0001\u0010\u00052\n\u0010\u0006\u001a\u0006\u0012\u0002\u0008\u00030\u00142<\u0008\u0002\u0010\u0008\u001a6\u0012\u0015\u0012\u0013\u0018\u00010\u0001\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000c\u0012\u0013\u0012\u00110\r\u00a2\u0006\u000c\u0008\n\u0012\u0008\u0008\u000b\u0012\u0004\u0008\u0008(\u000e\u0012\u0004\u0012\u0002H\u0005\u0018\u00010\tH\u0002J%\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u0002H\u001b0\u0004\"\u0004\u0008\u0001\u0010\u001b2\u000c\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u0002H\u001b0\u000f\u00a2\u0006\u0002\u0010\u001dJ\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u00042\u0006\u0010\u0006\u001a\u00020\u001fJ\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00042\u0006\u0010\u0006\u001a\u00020 J\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020!0\u00042\u0006\u0010\u0006\u001a\u00020\"J\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00042\u0006\u0010\u0006\u001a\u00020#J\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00042\u0006\u0010\u0006\u001a\u00020$J\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00042\u0006\u0010\u0006\u001a\u00020%J\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00042\u0006\u0010\u0006\u001a\u00020&J\u0014\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00042\u0006\u0010\u0006\u001a\u00020\'J \u0010\u001a\u001a\u0008\u0012\u0004\u0012\u0002H\u001b0\u0004\"\u0004\u0008\u0001\u0010\u001b2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u0002H\u001b0(J\u0014\u0010)\u001a\u00020\u001e2\n\u0008\u0002\u0010*\u001a\u0004\u0018\u00010\u0001H\u0016J\u000e\u0010+\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00010\u0004J+\u0010+\u001a\u0008\u0012\u0004\u0012\u0002H\u001b0\u0004\"\u0004\u0008\u0001\u0010\u001b2\u0012\u0010,\u001a\n\u0012\u0006\u0008\u0001\u0012\u0002H\u001b0\u000f\"\u0002H\u001b\u00a2\u0006\u0002\u0010\u001d\u00a8\u0006-"
    }
    d2 = {
        "Lio/dcloud/uts/UTSArray$Companion;",
        "",
        "()V",
        "from",
        "Lio/dcloud/uts/UTSArray;",
        "T",
        "list",
        "Lio/dcloud/uts/UTSValueIterable;",
        "mapFn",
        "Lkotlin/Function2;",
        "Lkotlin/ParameterName;",
        "name",
        "element",
        "",
        "index",
        "",
        "([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;",
        "UTSArray_from_Array_no_type",
        "",
        "",
        "",
        "UTSArray_from_Iterable_no_type",
        "fromAsync",
        "Lio/dcloud/uts/UTSPromise;",
        "([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;",
        "fromAsyncImpl",
        "fromNative",
        "E",
        "array",
        "([Ljava/lang/Object;)Lio/dcloud/uts/UTSArray;",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "isArray",
        "input",
        "of",
        "items",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 48
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Lio/dcloud/uts/UTSArray$Companion;-><init>()V

    return-void
.end method

.method public static synthetic UTSArray_from_Array_no_type$default(Lio/dcloud/uts/UTSArray$Companion;[Ljava/lang/Object;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 199
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->UTSArray_from_Array_no_type([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic UTSArray_from_Iterable_no_type$default(Lio/dcloud/uts/UTSArray$Companion;Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 223
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->UTSArray_from_Iterable_no_type(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic from$default(Lio/dcloud/uts/UTSArray$Companion;Lio/dcloud/uts/UTSValueIterable;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 170
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->from(Lio/dcloud/uts/UTSValueIterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic from$default(Lio/dcloud/uts/UTSArray$Companion;Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 236
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->from(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic from$default(Lio/dcloud/uts/UTSArray$Companion;Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 211
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->from(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic from$default(Lio/dcloud/uts/UTSArray$Companion;[Ljava/lang/Object;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 186
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->from([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic fromAsync$default(Lio/dcloud/uts/UTSArray$Companion;Lio/dcloud/uts/UTSValueIterable;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 53
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->fromAsync(Lio/dcloud/uts/UTSValueIterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic fromAsync$default(Lio/dcloud/uts/UTSArray$Companion;Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 141
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->fromAsync(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic fromAsync$default(Lio/dcloud/uts/UTSArray$Companion;Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 136
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->fromAsync(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic fromAsync$default(Lio/dcloud/uts/UTSArray$Companion;[Ljava/lang/Object;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 92
    :cond_0
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->fromAsync([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method

.method private final fromAsyncImpl(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Iterable<",
            "*>;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+TT;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;>;"
        }
    .end annotation

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 101
    invoke-static {v0, v1, v0}, Lkotlinx/coroutines/SupervisorKt;->SupervisorJob$default(Lkotlinx/coroutines/Job;ILjava/lang/Object;)Lkotlinx/coroutines/CompletableJob;

    move-result-object v0

    invoke-static {}, Lkotlinx/coroutines/Dispatchers;->getDefault()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v1

    check-cast v1, Lkotlin/coroutines/CoroutineContext;

    invoke-interface {v0, v1}, Lkotlinx/coroutines/CompletableJob;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    move-result-object v0

    invoke-static {v0}, Lkotlinx/coroutines/CoroutineScopeKt;->CoroutineScope(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/CoroutineScope;

    move-result-object v0

    .line 103
    new-instance v1, Lio/dcloud/uts/UTSPromise;

    new-instance v2, Lio/dcloud/uts/UTSArray$Companion$fromAsyncImpl$p$1;

    invoke-direct {v2, v0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion$fromAsyncImpl$p$1;-><init>(Lkotlinx/coroutines/CoroutineScope;Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)V

    check-cast v2, Lkotlin/jvm/functions/Function2;

    invoke-direct {v1, v2}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v1
.end method

.method static synthetic fromAsyncImpl$default(Lio/dcloud/uts/UTSArray$Companion;Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lio/dcloud/uts/UTSPromise;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 99
    :cond_0
    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->fromAsyncImpl(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic isArray$default(Lio/dcloud/uts/UTSArray$Companion;Ljava/lang/Object;ILjava/lang/Object;)Z
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    .line 330
    :cond_0
    invoke-virtual {p0, p1}, Lio/dcloud/uts/UTSArray$Companion;->isArray(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final UTSArray_from_Array_no_type([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Object;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+",
            "Ljava/lang/Object;",
            ">;)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 200
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1406
    array-length v1, p1

    const/4 v2, 0x0

    const/4 v3, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v4, p1, v2

    add-int/lit8 v5, v3, 0x1

    if-eqz p2, :cond_0

    .line 204
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {p2, v4, v3}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    .line 206
    :cond_0
    invoke-virtual {v0, v4}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    move v3, v5

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public final UTSArray_from_Iterable_no_type(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "*>;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+",
            "Ljava/lang/Object;",
            ">;)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 224
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1412
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p1

    const/4 v1, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    add-int/lit8 v3, v1, 0x1

    if-gez v1, :cond_0

    invoke-static {}, Lkotlin/collections/CollectionsKt;->throwIndexOverflow()V

    :cond_0
    if-eqz p2, :cond_1

    .line 228
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p2, v2, v1}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 230
    :cond_1
    invoke-virtual {v0, v2}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    move v1, v3

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public final from(Lio/dcloud/uts/UTSValueIterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSValueIterable<",
            "*>;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+TT;>;)",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 171
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 172
    invoke-interface {p1}, Lio/dcloud/uts/UTSValueIterable;->valueIterator()Lio/dcloud/uts/UTSIterator;

    move-result-object p1

    const/4 v1, 0x0

    .line 175
    :goto_0
    invoke-virtual {p1}, Lio/dcloud/uts/UTSIterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {p1}, Lio/dcloud/uts/UTSIterator;->next()Ljava/lang/Object;

    move-result-object v2

    if-eqz p2, :cond_0

    .line 178
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {p2, v2, v3}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 180
    :cond_0
    invoke-virtual {v0, v2}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public final from(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/CharSequence;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/String;",
            "-",
            "Ljava/lang/Number;",
            "Ljava/lang/String;",
            ">;)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 237
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    const/4 v1, 0x0

    const/4 v2, 0x0

    .line 1415
    :goto_0
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v3

    if-ge v1, v3, :cond_1

    invoke-interface {p1, v1}, Ljava/lang/CharSequence;->charAt(I)C

    move-result v3

    add-int/lit8 v4, v2, 0x1

    .line 240
    invoke-static {v3}, Ljava/lang/String;->valueOf(C)Ljava/lang/String;

    move-result-object v3

    if-eqz p2, :cond_0

    .line 243
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {p2, v3, v2}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    move-object v3, v2

    check-cast v3, Ljava/lang/String;

    .line 245
    :cond_0
    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    move v2, v4

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public final from(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Iterable<",
            "*>;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+TT;>;)",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 212
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1409
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p1

    const/4 v1, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    add-int/lit8 v3, v1, 0x1

    if-gez v1, :cond_0

    invoke-static {}, Lkotlin/collections/CollectionsKt;->throwIndexOverflow()V

    :cond_0
    if-eqz p2, :cond_1

    .line 216
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p2, v2, v1}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 218
    :cond_1
    invoke-virtual {v0, v2}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    move v1, v3

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public final from([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSArray;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">([",
            "Ljava/lang/Object;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+TT;>;)",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 187
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1403
    array-length v1, p1

    const/4 v2, 0x0

    const/4 v3, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v4, p1, v2

    add-int/lit8 v5, v3, 0x1

    if-eqz p2, :cond_0

    .line 191
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {p2, v4, v3}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    .line 193
    :cond_0
    invoke-virtual {v0, v4}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    move v3, v5

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public final fromAsync(Lio/dcloud/uts/UTSValueIterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lio/dcloud/uts/UTSValueIterable<",
            "*>;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+TT;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 55
    invoke-static {v0, v1, v0}, Lkotlinx/coroutines/SupervisorKt;->SupervisorJob$default(Lkotlinx/coroutines/Job;ILjava/lang/Object;)Lkotlinx/coroutines/CompletableJob;

    move-result-object v0

    invoke-static {}, Lkotlinx/coroutines/Dispatchers;->getDefault()Lkotlinx/coroutines/CoroutineDispatcher;

    move-result-object v1

    check-cast v1, Lkotlin/coroutines/CoroutineContext;

    invoke-interface {v0, v1}, Lkotlinx/coroutines/CompletableJob;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    move-result-object v0

    invoke-static {v0}, Lkotlinx/coroutines/CoroutineScopeKt;->CoroutineScope(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/CoroutineScope;

    move-result-object v0

    .line 57
    new-instance v1, Lio/dcloud/uts/UTSPromise;

    new-instance v2, Lio/dcloud/uts/UTSArray$Companion$fromAsync$p$1;

    invoke-direct {v2, v0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion$fromAsync$p$1;-><init>(Lkotlinx/coroutines/CoroutineScope;Lio/dcloud/uts/UTSValueIterable;Lkotlin/jvm/functions/Function2;)V

    check-cast v2, Lkotlin/jvm/functions/Function2;

    invoke-direct {v1, v2}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v1
.end method

.method public final fromAsync(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/CharSequence;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/String;",
            "-",
            "Ljava/lang/Number;",
            "Ljava/lang/String;",
            ">;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 144
    new-instance v0, Lio/dcloud/uts/UTSPromise;

    new-instance v1, Lio/dcloud/uts/UTSArray$Companion$fromAsync$promiseRet$1;

    invoke-direct {v1, p1, p2}, Lio/dcloud/uts/UTSArray$Companion$fromAsync$promiseRet$1;-><init>(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;)V

    check-cast v1, Lkotlin/jvm/functions/Function2;

    invoke-direct {v0, v1}, Lio/dcloud/uts/UTSPromise;-><init>(Lkotlin/jvm/functions/Function2;)V

    return-object v0
.end method

.method public final fromAsync(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Iterable<",
            "*>;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+TT;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 137
    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->fromAsyncImpl(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final fromAsync([Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">([",
            "Ljava/lang/Object;",
            "Lkotlin/jvm/functions/Function2<",
            "Ljava/lang/Object;",
            "-",
            "Ljava/lang/Number;",
            "+TT;>;)",
            "Lio/dcloud/uts/UTSPromise<",
            "Lio/dcloud/uts/UTSArray<",
            "TT;>;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 93
    invoke-static {p1}, Lkotlin/collections/ArraysKt;->toList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    check-cast p1, Ljava/lang/Iterable;

    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/UTSArray$Companion;->fromAsyncImpl(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Lio/dcloud/uts/UTSPromise;

    move-result-object p1

    return-object p1
.end method

.method public final fromNative(Ljava/util/List;)Lio/dcloud/uts/UTSArray;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/util/List<",
            "+TE;>;)",
            "Lio/dcloud/uts/UTSArray<",
            "TE;>;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 319
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0, p1}, Lio/dcloud/uts/UTSArray;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public final fromNative([B)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([B)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 262
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1419
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-byte v3, p1, v2

    .line 264
    invoke-static {v3}, Ljava/lang/Byte;->valueOf(B)Ljava/lang/Byte;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final fromNative([C)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([C)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Character;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 302
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1429
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-char v3, p1, v2

    .line 304
    invoke-static {v3}, Ljava/lang/Character;->valueOf(C)Ljava/lang/Character;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final fromNative([D)Lio/dcloud/uts/UTSArray;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([D)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 286
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1425
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-wide v3, p1, v2

    .line 288
    invoke-static {v3, v4}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final fromNative([F)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([F)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 278
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1423
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget v3, p1, v2

    .line 280
    invoke-static {v3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final fromNative([I)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([I)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 254
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1417
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget v3, p1, v2

    .line 256
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final fromNative([J)Lio/dcloud/uts/UTSArray;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([J)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 270
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1421
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-wide v3, p1, v2

    .line 272
    invoke-static {v3, v4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final fromNative([Ljava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E:",
            "Ljava/lang/Object;",
            ">([TE;)",
            "Lio/dcloud/uts/UTSArray<",
            "TE;>;"
        }
    .end annotation

    const-string v0, "array"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 323
    new-instance v0, Lio/dcloud/uts/UTSArray;

    array-length v1, p1

    invoke-static {p1, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object p1

    invoke-direct {v0, p1}, Lio/dcloud/uts/UTSArray;-><init>([Ljava/lang/Object;)V

    return-object v0
.end method

.method public final fromNative([S)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([S)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 294
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1427
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-short v3, p1, v2

    .line 296
    invoke-static {v3}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public final fromNative([Z)Lio/dcloud/uts/UTSArray;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([Z)",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    const-string v0, "list"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 310
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 1431
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-boolean v3, p1, v2

    .line 312
    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public isArray(Ljava/lang/Object;)Z
    .locals 0

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return p1

    .line 334
    :cond_0
    instance-of p1, p1, Lio/dcloud/uts/UTSArray;

    return p1
.end method

.method public final of()Lio/dcloud/uts/UTSArray;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .line 346
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    return-object v0
.end method

.method public final varargs of([Ljava/lang/Object;)Lio/dcloud/uts/UTSArray;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E:",
            "Ljava/lang/Object;",
            ">([TE;)",
            "Lio/dcloud/uts/UTSArray<",
            "TE;>;"
        }
    .end annotation

    const-string v0, "items"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 342
    new-instance v0, Lio/dcloud/uts/UTSArray;

    array-length v1, p1

    invoke-static {p1, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object p1

    invoke-direct {v0, p1}, Lio/dcloud/uts/UTSArray;-><init>([Ljava/lang/Object;)V

    return-object v0
.end method
