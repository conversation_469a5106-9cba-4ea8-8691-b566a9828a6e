.class public Lcom/meizu/flyme/openidsdk/OpenId;
.super Ljava/lang/Object;


# instance fields
.field public a:J

.field public b:Ljava/lang/String;

.field public c:Ljava/lang/String;

.field public d:I


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/meizu/flyme/openidsdk/OpenId;->c:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public native a(I)V
.end method

.method public native a(J)V
.end method

.method public native a(Ljava/lang/String;)V
.end method

.method public native a()Z
.end method

.method public native b()V
.end method
