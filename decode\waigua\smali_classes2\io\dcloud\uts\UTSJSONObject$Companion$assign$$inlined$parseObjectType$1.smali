.class public final Lio/dcloud/uts/UTSJSONObject$Companion$assign$$inlined$parseObjectType$1;
.super Lio/dcloud/uts/gson/reflect/TypeToken;
.source "JSON.kt"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lio/dcloud/uts/UTSJSONObject$Companion;->assign([Lio/dcloud/uts/IUTSObject;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lio/dcloud/uts/gson/reflect/TypeToken<",
        "TT;>;"
    }
.end annotation

.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nJSON.kt\nKotlin\n*S Kotlin\n*F\n+ 1 JSON.kt\nio/dcloud/uts/JSON$parse$1\n*L\n1#1,371:1\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\r\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u0008\u0012\u0004\u0012\u00028\u00000\u0001\u00a8\u0006\u0002\u00b8\u0006\u0004"
    }
    d2 = {
        "io/dcloud/uts/JSON$parse$1",
        "Lio/dcloud/uts/gson/reflect/TypeToken;",
        "utsplugin_release",
        "io/dcloud/uts/JSON$parse$$inlined$parse$1",
        "io/dcloud/uts/JSON$parseObject$$inlined$parseType$default$1"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0xb0
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 62
    invoke-direct {p0}, Lio/dcloud/uts/gson/reflect/TypeToken;-><init>()V

    return-void
.end method
