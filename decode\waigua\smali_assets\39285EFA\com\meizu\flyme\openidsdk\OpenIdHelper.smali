.class public Lcom/meizu/flyme/openidsdk/OpenIdHelper;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static native a(Landroid/content/Context;)Ljava/lang/String;
.end method

.method public static final native a()Z
.end method

.method public static native b(Landroid/content/Context;)Ljava/lang/String;
.end method

.method public static native c(Landroid/content/Context;)Ljava/lang/String;
.end method

.method public static native d(Landroid/content/Context;)Ljava/lang/String;
.end method
