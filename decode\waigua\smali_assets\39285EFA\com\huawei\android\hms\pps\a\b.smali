.class public final Lcom/huawei/android/hms/pps/a/b;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/huawei/android/hms/pps/a/c;


# instance fields
.field private a:Landroid/os/IBinder;


# direct methods
.method public constructor <init>(Landroid/os/IBinder;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/huawei/android/hms/pps/a/b;->a:Landroid/os/IBinder;

    return-void
.end method


# virtual methods
.method public final native asBinder()Landroid/os/IBinder;
.end method

.method public final native d()Z
.end method

.method public final native e()Ljava/lang/String;
.end method
