{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__E2661D2", "name": "獬豸侠法律", "version": {"name": "2.6.7", "code": 2670}, "description": "獬豸侠法律", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Share": {}, "Payment": {}, "VideoPlayer": {}, "Maps": {"coordType": "gcj02"}, "Camera": {}, "Bluetooth": {}, "Barcode": {}, "OAuth": {}, "Contacts": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": true, "delay": 0, "target": "id:1", "waiting": true}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#3388FF"}, "compatible": {"ignoreVersion": true}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "distribute": {"icons": {"android": {"hdpi": "icon-android-hdpi.png", "xhdpi": "icon-android-xhdpi.png", "xxhdpi": "icon-android-xxhdpi.png", "xxxhdpi": "icon-android-xxxhdpi.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notific__UNI__9080613ation": "unpackage/res/icons/20x20.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}, "prerendered": "false"}}, "splashscreen": {"useOriginalMsgbox": true, "androidStyle": "default", "android": {"hdpi": "splash-android-hdpi.png", "xhdpi": "splash-android-xhdpi.png", "xxhdpi": "splash-android-xxhdpi.png"}}, "google": {"abiFilters": ["armeabi-v7a", "arm64-v8a"], "permissionExternalStorage": {"prompt": "应用需要保存运行状态，用户信息等数据到手机。", "request": "none"}, "permissionPhoneState": {"prompt": "需要获取设备信息,统计app使用率", "request": "none"}, "permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "packagename": "uni.UNIE2661D2", "custompermissions": true}, "apple": {"capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:v2ulink.diandashop.com", "applinks:static-7c82a81f-93f5-4c76-aa1d-9f56479b257e.bspapp.com", "applinks:ulink.diandashop.com"]}}, "dSYMs": false, "idfa": false, "privacyDescription": {"NSCameraUsageDescription": "拍摄图片作为用户头像及商品评价等", "NSLocationAlwaysAndWhenInUseUsageDescription": "获取当前位置用于展示距离", "NSLocationAlwaysUsageDescription": "实时获取位置进行定位", "NSLocationWhenInUseUsageDescription": "用于展示店铺距离", "NSMicrophoneUsageDescription": "录音上传到用户点评", "NSPhotoLibraryAddUsageDescription": "保存商品海报等图片到手机", "NSPhotoLibraryUsageDescription": "从相册中选择图片作为用户头像及商品评价等"}, "devices": "universal"}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}, "maps": {"amap": {"appkey_android": "256448acc3bc6e090c9005f05b57d605", "appkey_ios": "61e75cacb365d682d08f36c2862c5a50", "name": "amapphPhPclT"}, "description": "地图插件"}, "oauth": {"weixin": {"UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613", "appid": "wxa4447aba91caa5ce"}}, "payment": {"alipay": {"__platform__": ["ios", "android"]}, "weixin": {"UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613", "__platform__": ["ios", "android"], "appid": "wxa4447aba91caa5ce"}}, "share": {"weixin": {"UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613", "appid": "wxa4447aba91caa5ce"}}}, "orientation": "portrait-primary"}, "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "launch_path": "__uniappview.html", "adid": "123212240006"}}