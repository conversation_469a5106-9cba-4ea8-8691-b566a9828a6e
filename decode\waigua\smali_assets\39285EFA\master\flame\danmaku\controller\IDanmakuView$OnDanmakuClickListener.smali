.class public interface abstract Lmaster/flame/danmaku/controller/IDanmakuView$OnDanmakuClickListener;
.super Ljava/lang/Object;
.source "IDanmakuView.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lmaster/flame/danmaku/controller/IDanmakuView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnDanmakuClickListener"
.end annotation


# virtual methods
.method public abstract onDanmakuClick(Lmaster/flame/danmaku/danmaku/model/IDanmakus;)Z
.end method

.method public abstract onViewClick(Lmaster/flame/danmaku/controller/IDanmakuView;)Z
.end method
