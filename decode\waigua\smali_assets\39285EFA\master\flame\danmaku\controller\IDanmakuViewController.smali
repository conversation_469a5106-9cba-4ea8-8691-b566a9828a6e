.class public interface abstract Lmaster/flame/danmaku/controller/IDanmakuViewController;
.super Ljava/lang/Object;
.source "IDanmakuViewController.java"


# virtual methods
.method public abstract clear()V
.end method

.method public abstract drawDanmakus()J
.end method

.method public abstract getContext()Landroid/content/Context;
.end method

.method public abstract getHeight()I
.end method

.method public abstract getWidth()I
.end method

.method public abstract isDanmakuDrawingCacheEnabled()Z
.end method

.method public abstract isHardwareAccelerated()Z
.end method

.method public abstract isViewReady()Z
.end method
