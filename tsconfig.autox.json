{"compilerOptions": {"forceConsistentCasingInFileNames": true, "target": "ES2016", "useDefineForClassFields": true, "module": "ES2020", "outDir": "dist-autox", "moduleResolution": "node", "allowUnreachableCode": false, "noImplicitReturns": true, "strict": true, "jsx": "react", "jsxFactory": "ui.h", "declaration": false, "resolveJsonModule": true, "isolatedModules": true, "allowJs": true, "esModuleInterop": true, "lib": ["ESNext"], "skipLibCheck": true, "paths": {"@/*": ["./src-autox/*"]}}, "include": ["src-autox/**/*.ts", "src-autox/**/*.d.ts", "src-autox/**/*.tsx", "node_modules/autox-v6-api/**/*.d.ts"]}