.class public final Lio/dcloud/uts/prompt/R$layout;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/prompt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "layout"
.end annotation


# static fields
.field public static uni_app_uni_prompt_ac_recyclerview_layout:I = 0x7f0b005c

.field public static uni_app_uni_prompt_ac_recyclerview_layout_night:I = 0x7f0b005d

.field public static uni_app_uni_prompt_ac_recyclerview_layout_top:I = 0x7f0b005e

.field public static uni_app_uni_prompt_ac_recyclerview_layout_top_night:I = 0x7f0b005f

.field public static uni_app_uni_prompt_loadingview:I = 0x7f0b0060

.field public static uni_app_uni_prompt_modal_dialog:I = 0x7f0b0061

.field public static uni_app_uni_prompt_modal_dialog_night:I = 0x7f0b0062

.field public static uni_app_uni_prompt_uts_action_sheet:I = 0x7f0b0063

.field public static uni_app_uni_prompt_uts_action_sheet_night:I = 0x7f0b0064


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
