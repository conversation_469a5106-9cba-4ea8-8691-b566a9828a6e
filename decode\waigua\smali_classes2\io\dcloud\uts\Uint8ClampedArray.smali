.class public final Lio/dcloud/uts/Uint8ClampedArray;
.super Lio/dcloud/uts/Uint8Array;
.source "Uint8ClampedArray.kt"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lio/dcloud/uts/Uint8ClampedArray$Companion;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0004\n\u0002\u0008\u0002\n\u0002\u0010\u001e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0008\u0018\u0000 \"2\u00020\u0001:\u0001\"B\u000f\u0008\u0016\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004B\u0015\u0008\u0016\u0012\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\u0002\u0010\u0007B\u000f\u0008\u0016\u0012\u0006\u0010\u0008\u001a\u00020\t\u00a2\u0006\u0002\u0010\nB\'\u0008\u0016\u0012\u0006\u0010\u0008\u001a\u00020\t\u0012\n\u0008\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\n\u0008\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u000cJ_\u0010\r\u001a\u00020\u0000\"\u0008\u0008\u0000\u0010\u000e*\u00020\u000f2K\u0010\u0010\u001aG\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0012\u0012\u0008\u0008\u0013\u0012\u0004\u0008\u0008(\u0014\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0012\u0012\u0008\u0008\u0013\u0012\u0004\u0008\u0008(\u0015\u0012\u0013\u0012\u0011H\u000e\u00a2\u0006\u000c\u0008\u0012\u0012\u0008\u0008\u0013\u0012\u0004\u0008\u0008(\u0005\u0012\u0004\u0012\u00020\u00160\u0011H\u0016J_\u0010\u0017\u001a\u00020\u0000\"\u0008\u0008\u0000\u0010\u000e*\u00020\u000f2K\u0010\u0018\u001aG\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0012\u0012\u0008\u0008\u0013\u0012\u0004\u0008\u0008(\u0014\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\u000c\u0008\u0012\u0012\u0008\u0008\u0013\u0012\u0004\u0008\u0008(\u0015\u0012\u0013\u0012\u0011H\u000e\u00a2\u0006\u000c\u0008\u0012\u0012\u0008\u0008\u0013\u0012\u0004\u0008\u0008(\u0005\u0012\u0004\u0012\u00020\u00030\u0011H\u0016J\u001f\u0010\u0019\u001a\u00020\u001a2\u0008\u0010\u0015\u001a\u0004\u0018\u00010\u001b2\u0006\u0010\u0014\u001a\u00020\u0003H\u0016\u00a2\u0006\u0002\u0010\u001cJ\u001c\u0010\u001d\u001a\u00020\u00002\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u00032\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u0016J\u001c\u0010 \u001a\u00020\u00002\u0008\u0010!\u001a\u0004\u0018\u00010\u00032\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u0016\u00a8\u0006#"
    }
    d2 = {
        "Lio/dcloud/uts/Uint8ClampedArray;",
        "Lio/dcloud/uts/Uint8Array;",
        "length",
        "",
        "(Ljava/lang/Number;)V",
        "array",
        "",
        "(Ljava/util/Collection;)V",
        "buffer",
        "Lio/dcloud/uts/ArrayBuffer;",
        "(Lio/dcloud/uts/ArrayBuffer;)V",
        "byteOffset",
        "(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V",
        "filter",
        "T",
        "Lio/dcloud/uts/TypedArray;",
        "predicate",
        "Lkotlin/Function3;",
        "Lkotlin/ParameterName;",
        "name",
        "value",
        "index",
        "",
        "map",
        "callbackfn",
        "putAuto",
        "",
        "",
        "(Ljava/lang/Integer;Ljava/lang/Number;)V",
        "slice",
        "start",
        "end",
        "subarray",
        "begin",
        "Companion",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final BYTES_PER_ELEMENT:I = 0x1

.field public static final Companion:Lio/dcloud/uts/Uint8ClampedArray$Companion;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lio/dcloud/uts/Uint8ClampedArray$Companion;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lio/dcloud/uts/Uint8ClampedArray$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lio/dcloud/uts/Uint8ClampedArray;->Companion:Lio/dcloud/uts/Uint8ClampedArray$Companion;

    return-void
.end method

.method public constructor <init>(Lio/dcloud/uts/ArrayBuffer;)V
    .locals 1

    const-string v0, "buffer"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 46
    invoke-direct {p0, p1}, Lio/dcloud/uts/Uint8Array;-><init>(Lio/dcloud/uts/ArrayBuffer;)V

    return-void
.end method

.method public constructor <init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V
    .locals 1

    const-string v0, "buffer"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 52
    invoke-direct {p0, p1, p2, p3}, Lio/dcloud/uts/Uint8Array;-><init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V

    return-void
.end method

.method public synthetic constructor <init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p5, p4, 0x2

    const/4 v0, 0x0

    if-eqz p5, :cond_0

    move-object p2, v0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    move-object p3, v0

    .line 48
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lio/dcloud/uts/Uint8ClampedArray;-><init>(Lio/dcloud/uts/ArrayBuffer;Ljava/lang/Number;Ljava/lang/Number;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Number;)V
    .locals 1

    const-string v0, "length"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 38
    invoke-direct {p0, p1}, Lio/dcloud/uts/Uint8Array;-><init>(Ljava/lang/Number;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Collection;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+",
            "Ljava/lang/Number;",
            ">;)V"
        }
    .end annotation

    const-string v0, "array"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 42
    invoke-direct {p0, p1}, Lio/dcloud/uts/Uint8Array;-><init>(Ljava/util/Collection;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint8ClampedArray;->filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public bridge synthetic filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8Array;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint8ClampedArray;->filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/Uint8Array;

    return-object p1
.end method

.method public filter(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8ClampedArray;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lio/dcloud/uts/TypedArray;",
            ">(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Number;",
            "-",
            "Ljava/lang/Number;",
            "-TT;",
            "Ljava/lang/Boolean;",
            ">;)",
            "Lio/dcloud/uts/Uint8ClampedArray;"
        }
    .end annotation

    const-string v0, "predicate"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 65
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 67
    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->size()I

    move-result v1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    .line 69
    invoke-virtual {p0, v2}, Lio/dcloud/uts/Uint8ClampedArray;->getAuto(I)Ljava/lang/Number;

    move-result-object v3

    .line 70
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const-string v6, "null cannot be cast to non-null type T of io.dcloud.uts.Uint8ClampedArray.filter"

    invoke-static {p0, v6}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v6, p0

    check-cast v6, Lio/dcloud/uts/TypedArray;

    invoke-interface {p1, v4, v5, v6}, Lkotlin/jvm/functions/Function3;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Boolean;

    invoke-virtual {v4}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v4

    if-eqz v4, :cond_0

    .line 71
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    move-result v3

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-virtual {v0, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 75
    :cond_1
    new-instance p1, Lio/dcloud/uts/Uint8ClampedArray;

    check-cast v0, Ljava/util/Collection;

    invoke-direct {p1, v0}, Lio/dcloud/uts/Uint8ClampedArray;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method

.method public bridge synthetic map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint8ClampedArray;->map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public bridge synthetic map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8Array;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint8ClampedArray;->map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/Uint8Array;

    return-object p1
.end method

.method public map(Lkotlin/jvm/functions/Function3;)Lio/dcloud/uts/Uint8ClampedArray;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lio/dcloud/uts/TypedArray;",
            ">(",
            "Lkotlin/jvm/functions/Function3<",
            "-",
            "Ljava/lang/Number;",
            "-",
            "Ljava/lang/Number;",
            "-TT;+",
            "Ljava/lang/Number;",
            ">;)",
            "Lio/dcloud/uts/Uint8ClampedArray;"
        }
    .end annotation

    const-string v0, "callbackfn"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 81
    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->size()I

    move-result v0

    .line 82
    new-instance v1, Lio/dcloud/uts/UTSArray;

    invoke-direct {v1}, Lio/dcloud/uts/UTSArray;-><init>()V

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 86
    invoke-virtual {p0, v2}, Lio/dcloud/uts/Uint8ClampedArray;->getAuto(I)Ljava/lang/Number;

    move-result-object v3

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v5, "null cannot be cast to non-null type T of io.dcloud.uts.Uint8ClampedArray.map"

    invoke-static {p0, v5}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    move-object v5, p0

    check-cast v5, Lio/dcloud/uts/TypedArray;

    .line 85
    invoke-interface {p1, v3, v4, v5}, Lkotlin/jvm/functions/Function3;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Number;

    .line 87
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    move-result v3

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    .line 84
    invoke-virtual {v1, v3}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 92
    :cond_0
    new-instance p1, Lio/dcloud/uts/Uint8ClampedArray;

    check-cast v1, Ljava/util/Collection;

    invoke-direct {p1, v1}, Lio/dcloud/uts/Uint8ClampedArray;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method

.method public putAuto(Ljava/lang/Integer;Ljava/lang/Number;)V
    .locals 2

    const-string v0, "value"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    .line 56
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Ljava/lang/Number;

    invoke-static {p2, v1}, Lio/dcloud/uts/NumberKt;->compareTo(Ljava/lang/Number;Ljava/lang/Number;)I

    move-result v1

    if-gez v1, :cond_0

    .line 57
    move-object p2, v0

    check-cast p2, Ljava/lang/Number;

    goto :goto_0

    :cond_0
    const/16 v0, 0xff

    .line 58
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    check-cast v1, Ljava/lang/Number;

    invoke-static {p2, v1}, Lio/dcloud/uts/NumberKt;->compareTo(Ljava/lang/Number;Ljava/lang/Number;)I

    move-result v1

    if-lez v1, :cond_1

    .line 59
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    check-cast p2, Ljava/lang/Number;

    .line 60
    :cond_1
    :goto_0
    invoke-super {p0, p1, p2}, Lio/dcloud/uts/Uint8Array;->putAuto(Ljava/lang/Integer;Ljava/lang/Number;)V

    return-void
.end method

.method public bridge synthetic slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/Uint8ClampedArray;->slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public bridge synthetic slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8Array;
    .locals 0

    .line 5
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/Uint8ClampedArray;->slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/Uint8Array;

    return-object p1
.end method

.method public slice(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8ClampedArray;
    .locals 2

    if-nez p1, :cond_0

    const/4 p1, 0x0

    .line 96
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    :cond_0
    if-nez p2, :cond_1

    .line 97
    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    check-cast p2, Ljava/lang/Number;

    .line 98
    :cond_1
    new-instance v0, Lio/dcloud/uts/UTSArray;

    invoke-direct {v0}, Lio/dcloud/uts/UTSArray;-><init>()V

    .line 99
    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    invoke-static {p1, v1}, Lio/dcloud/uts/utils/IndexKt;->toSliceIndex(Ljava/lang/Number;I)I

    move-result p1

    .line 101
    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    .line 99
    invoke-static {p2, v1}, Lio/dcloud/uts/utils/IndexKt;->toSliceIndex(Ljava/lang/Number;I)I

    move-result p2

    :goto_0
    if-ge p1, p2, :cond_2

    .line 103
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint8ClampedArray;->getAuto(I)Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v0, v1}, Lio/dcloud/uts/UTSArray;->add(Ljava/lang/Object;)Z

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 105
    :cond_2
    new-instance p1, Lio/dcloud/uts/Uint8ClampedArray;

    check-cast v0, Ljava/util/Collection;

    invoke-direct {p1, v0}, Lio/dcloud/uts/Uint8ClampedArray;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method

.method public bridge synthetic subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/TypedArray;
    .locals 0

    .line 5
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/Uint8ClampedArray;->subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/TypedArray;

    return-object p1
.end method

.method public bridge synthetic subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8Array;
    .locals 0

    .line 5
    invoke-virtual {p0, p1, p2}, Lio/dcloud/uts/Uint8ClampedArray;->subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8ClampedArray;

    move-result-object p1

    check-cast p1, Lio/dcloud/uts/Uint8Array;

    return-object p1
.end method

.method public subarray(Ljava/lang/Number;Ljava/lang/Number;)Lio/dcloud/uts/Uint8ClampedArray;
    .locals 4

    const/4 v0, 0x0

    .line 112
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    if-eqz p1, :cond_1

    .line 111
    move-object v2, v1

    check-cast v2, Ljava/lang/Number;

    invoke-static {p1, v2}, Lio/dcloud/uts/NumberKt;->compareTo(Ljava/lang/Number;Ljava/lang/Number;)I

    move-result v2

    if-gez v2, :cond_0

    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object v2

    invoke-static {v2, p1}, Lio/dcloud/uts/NumberKt;->plus(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p1

    :cond_0
    if-nez p1, :cond_2

    .line 112
    :cond_1
    move-object p1, v1

    check-cast p1, Ljava/lang/Number;

    :cond_2
    if-eqz p2, :cond_4

    .line 116
    check-cast v1, Ljava/lang/Number;

    invoke-static {p2, v1}, Lio/dcloud/uts/NumberKt;->compareTo(Ljava/lang/Number;Ljava/lang/Number;)I

    move-result v1

    if-gez v1, :cond_3

    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-static {v1, p2}, Lio/dcloud/uts/NumberKt;->plus(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object p2

    :cond_3
    if-nez p2, :cond_5

    .line 117
    :cond_4
    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object p2

    .line 120
    :cond_5
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    invoke-static {p1, v0, v1}, Lkotlin/ranges/RangesKt;->coerceIn(III)I

    move-result p1

    .line 122
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    invoke-virtual {p0}, Lio/dcloud/uts/Uint8ClampedArray;->getLength()Ljava/lang/Number;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    move-result v1

    invoke-static {p2, p1, v1}, Lkotlin/ranges/RangesKt;->coerceIn(III)I

    move-result p2

    .line 123
    new-instance v1, Lio/dcloud/uts/UTSArray;

    invoke-direct {v1}, Lio/dcloud/uts/UTSArray;-><init>()V

    :goto_0
    if-ge p1, p2, :cond_6

    .line 125
    invoke-virtual {p0, p1}, Lio/dcloud/uts/Uint8ClampedArray;->getAuto(I)Ljava/lang/Number;

    move-result-object v2

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Number;

    aput-object v2, v3, v0

    invoke-virtual {v1, v3}, Lio/dcloud/uts/UTSArray;->push([Ljava/lang/Object;)I

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 127
    :cond_6
    new-instance p1, Lio/dcloud/uts/Uint8ClampedArray;

    check-cast v1, Ljava/util/Collection;

    invoke-direct {p1, v1}, Lio/dcloud/uts/Uint8ClampedArray;-><init>(Ljava/util/Collection;)V

    return-object p1
.end method
