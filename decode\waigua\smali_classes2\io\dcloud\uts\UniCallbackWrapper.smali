.class public final Lio/dcloud/uts/UniCallbackWrapper;
.super Ljava/lang/Object;
.source "UniCallbackWrapper.kt"


# annotations
.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nUniCallbackWrapper.kt\nKotlin\n*S Kotlin\n*F\n+ 1 UniCallbackWrapper.kt\nio/dcloud/uts/UniCallbackWrapper\n+ 2 _Arrays.kt\nkotlin/collections/ArraysKt___ArraysKt\n*L\n1#1,151:1\n13579#2,2:152\n*S KotlinDebug\n*F\n+ 1 UniCallbackWrapper.kt\nio/dcloud/uts/UniCallbackWrapper\n*L\n65#1:152,2\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0011\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\u0013\u0008\u0016\u0012\n\u0010\u0002\u001a\u0006\u0012\u0002\u0008\u00030\u0003\u00a2\u0006\u0002\u0010\u0004B\u0013\u0008\u0016\u0012\n\u0010\u0002\u001a\u0006\u0012\u0002\u0008\u00030\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u001c\u001a\u0012\u0012\u0004\u0012\u00020\t\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0018\u00010\u0008J$\u0010\u001d\u001a\u0004\u0018\u00010\u00012\u0012\u0010\u001e\u001a\n\u0012\u0006\u0008\u0001\u0012\u00020\u00010\u001f\"\u00020\u0001H\u0086\u0002\u00a2\u0006\u0002\u0010 J\u0006\u0010!\u001a\u00020\"J\u0006\u0010#\u001a\u00020$J\u001e\u0010%\u001a\u00020$2\u0016\u0010&\u001a\u0012\u0012\u0004\u0012\u00020\t\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0018\u00010\u0008R*\u0010\u0007\u001a\u0012\u0012\u0004\u0012\u00020\t\u0012\u0006\u0012\u0004\u0018\u00010\u0001\u0018\u00010\u0008X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\n\u0010\u000b\"\u0004\u0008\u000c\u0010\rR \u0010\u000e\u001a\u0008\u0012\u0002\u0008\u0003\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u000f\u0010\u0010\"\u0004\u0008\u0011\u0010\u0006R \u0010\u0012\u001a\u0008\u0012\u0002\u0008\u0003\u0018\u00010\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0013\u0010\u0014\"\u0004\u0008\u0015\u0010\u0004R \u0010\u0016\u001a\u0008\u0012\u0002\u0008\u0003\u0018\u00010\u0017X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0018\u0010\u0019\"\u0004\u0008\u001a\u0010\u001b\u00a8\u0006\'"
    }
    d2 = {
        "Lio/dcloud/uts/UniCallbackWrapper;",
        "",
        "callFunc",
        "Lkotlin/reflect/KFunction;",
        "(Lkotlin/reflect/KFunction;)V",
        "Lkotlin/Function;",
        "(Lkotlin/Function;)V",
        "holderArgs",
        "",
        "",
        "getHolderArgs",
        "()Ljava/util/Map;",
        "setHolderArgs",
        "(Ljava/util/Map;)V",
        "holderFun",
        "getHolderFun",
        "()Lkotlin/Function;",
        "setHolderFun",
        "holderFunc",
        "getHolderFunc",
        "()Lkotlin/reflect/KFunction;",
        "setHolderFunc",
        "hostClass",
        "Ljava/lang/Class;",
        "getHostClass",
        "()Ljava/lang/Class;",
        "setHostClass",
        "(Ljava/lang/Class;)V",
        "getArgs",
        "invoke",
        "args",
        "",
        "([Ljava/lang/Object;)Ljava/lang/Object;",
        "isValid",
        "",
        "off",
        "",
        "setArgs",
        "param",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private holderArgs:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private holderFun:Lkotlin/Function;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/Function<",
            "*>;"
        }
    .end annotation
.end field

.field private holderFunc:Lkotlin/reflect/KFunction;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/reflect/KFunction<",
            "*>;"
        }
    .end annotation
.end field

.field private hostClass:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lkotlin/Function;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/Function<",
            "*>;)V"
        }
    .end annotation

    const-string v0, "callFunc"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 45
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 46
    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    .line 47
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->hostClass:Ljava/lang/Class;

    return-void
.end method

.method public constructor <init>(Lkotlin/reflect/KFunction;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/reflect/KFunction<",
            "*>;)V"
        }
    .end annotation

    const-string v0, "callFunc"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 37
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 38
    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFunc:Lkotlin/reflect/KFunction;

    return-void
.end method


# virtual methods
.method public final getArgs()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .line 133
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderArgs:Ljava/util/Map;

    return-object v0
.end method

.method public final getHolderArgs()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .line 31
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderArgs:Ljava/util/Map;

    return-object v0
.end method

.method public final getHolderFun()Lkotlin/Function;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/Function<",
            "*>;"
        }
    .end annotation

    .line 25
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    return-object v0
.end method

.method public final getHolderFunc()Lkotlin/reflect/KFunction;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/reflect/KFunction<",
            "*>;"
        }
    .end annotation

    .line 20
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFunc:Lkotlin/reflect/KFunction;

    return-object v0
.end method

.method public final getHostClass()Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation

    .line 26
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->hostClass:Ljava/lang/Class;

    return-object v0
.end method

.method public final varargs invoke([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    const-string v0, "args"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 56
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    const/4 v1, 0x0

    if-eqz v0, :cond_8

    .line 65
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->hostClass:Ljava/lang/Class;

    const/4 v2, 0x0

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 152
    array-length v3, v0

    move-object v5, v1

    move-object v6, v5

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v3, :cond_3

    aget-object v7, v0, v4

    .line 66
    const-string v8, "invoke"

    invoke-virtual {v7}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v9

    invoke-static {v8, v9}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_1

    .line 67
    invoke-virtual {v7}, Ljava/lang/reflect/Method;->isBridge()Z

    move-result v8

    if-nez v8, :cond_0

    move-object v5, v7

    goto :goto_1

    :cond_0
    move-object v6, v7

    :cond_1
    :goto_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_2
    move-object v5, v1

    move-object v6, v5

    :cond_3
    if-nez v5, :cond_4

    if-eqz v6, :cond_4

    move-object v5, v6

    :cond_4
    if-eqz v5, :cond_8

    .line 89
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v0

    array-length v0, v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_5

    .line 93
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v3

    aget-object v3, v3, v2

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    .line 94
    const-string v4, "[Ljava.lang.Object;"

    invoke-static {v4, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_5

    .line 96
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v2

    invoke-virtual {v5, v0, v1}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 101
    :cond_5
    array-length v1, p1

    const-string v2, ""

    if-ge v1, v0, :cond_6

    return-object v2

    .line 106
    :cond_6
    array-length v1, p1

    if-le v1, v0, :cond_7

    return-object v2

    .line 111
    :cond_7
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    array-length v1, p1

    invoke-static {p1, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {v5, v0, p1}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 120
    :cond_8
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFunc:Lkotlin/reflect/KFunction;

    if-eqz v0, :cond_9

    array-length v1, p1

    invoke-static {p1, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object p1

    invoke-interface {v0, p1}, Lkotlin/reflect/KFunction;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    :cond_9
    return-object v1
.end method

.method public final isValid()Z
    .locals 1

    .line 145
    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFunc:Lkotlin/reflect/KFunction;

    if-nez v0, :cond_0

    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    if-nez v0, :cond_0

    iget-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderArgs:Ljava/util/Map;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    const/4 v0, 0x1

    return v0
.end method

.method public final off()V
    .locals 1

    const/4 v0, 0x0

    .line 138
    iput-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFunc:Lkotlin/reflect/KFunction;

    .line 139
    iput-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    .line 140
    iput-object v0, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderArgs:Ljava/util/Map;

    return-void
.end method

.method public final setArgs(Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 129
    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderArgs:Ljava/util/Map;

    return-void
.end method

.method public final setHolderArgs(Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 31
    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderArgs:Ljava/util/Map;

    return-void
.end method

.method public final setHolderFun(Lkotlin/Function;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/Function<",
            "*>;)V"
        }
    .end annotation

    .line 25
    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFun:Lkotlin/Function;

    return-void
.end method

.method public final setHolderFunc(Lkotlin/reflect/KFunction;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/reflect/KFunction<",
            "*>;)V"
        }
    .end annotation

    .line 20
    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->holderFunc:Lkotlin/reflect/KFunction;

    return-void
.end method

.method public final setHostClass(Ljava/lang/Class;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)V"
        }
    .end annotation

    .line 26
    iput-object p1, p0, Lio/dcloud/uts/UniCallbackWrapper;->hostClass:Ljava/lang/Class;

    return-void
.end method
