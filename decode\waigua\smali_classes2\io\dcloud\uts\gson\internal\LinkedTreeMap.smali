.class public final Lio/dcloud/uts/gson/internal/LinkedTreeMap;
.super Ljava/util/AbstractMap;
.source "LinkedTreeMap.java"

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;,
        Lio/dcloud/uts/gson/internal/LinkedTreeMap$EntrySet;,
        Lio/dcloud/uts/gson/internal/LinkedTreeMap$KeySet;,
        Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<K:",
        "Ljava/lang/Object;",
        "V:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/util/AbstractMap<",
        "TK;TV;>;",
        "Ljava/io/Serializable;"
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z

.field private static final NATURAL_ORDER:Ljava/util/Comparator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Comparator<",
            "Ljava/lang/Comparable;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field comparator:Ljava/util/Comparator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Comparator<",
            "-TK;>;"
        }
    .end annotation
.end field

.field private entrySet:Lio/dcloud/uts/gson/internal/LinkedTreeMap$EntrySet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap<",
            "TK;TV;>.EntrySet;"
        }
    .end annotation
.end field

.field final header:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field private keySet:Lio/dcloud/uts/gson/internal/LinkedTreeMap$KeySet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap<",
            "TK;TV;>.KeySet;"
        }
    .end annotation
.end field

.field modCount:I

.field root:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field size:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 43
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$1;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$1;-><init>()V

    sput-object v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 63
    sget-object v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    invoke-direct {p0, v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;-><init>(Ljava/util/Comparator;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Comparator;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Comparator<",
            "-TK;>;)V"
        }
    .end annotation

    .line 74
    invoke-direct {p0}, Ljava/util/AbstractMap;-><init>()V

    const/4 v0, 0x0

    .line 51
    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->size:I

    .line 52
    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    .line 55
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;-><init>()V

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz p1, :cond_0

    goto :goto_0

    .line 77
    :cond_0
    sget-object p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    :goto_0
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->comparator:Ljava/util/Comparator;

    return-void
.end method

.method private equal(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    if-eq p1, p2, :cond_1

    if-eqz p1, :cond_0

    .line 211
    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method private readObject(Ljava/io/ObjectInputStream;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 636
    new-instance p1, Ljava/io/InvalidObjectException;

    const-string v0, "Deserialization is unsupported"

    invoke-direct {p1, v0}, Ljava/io/InvalidObjectException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method private rebalance(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;Z)V"
        }
    .end annotation

    :goto_0
    if-eqz p1, :cond_e

    .line 315
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 316
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    .line 317
    iget v3, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_1

    :cond_0
    const/4 v3, 0x0

    :goto_1
    if-eqz v1, :cond_1

    .line 318
    iget v4, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_2

    :cond_1
    const/4 v4, 0x0

    :goto_2
    sub-int v5, v3, v4

    const/4 v6, -0x2

    if-ne v5, v6, :cond_6

    .line 322
    iget-object v0, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 323
    iget-object v3, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz v3, :cond_2

    .line 324
    iget v3, v3, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_3

    :cond_2
    const/4 v3, 0x0

    :goto_3
    if-eqz v0, :cond_3

    .line 325
    iget v2, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    :cond_3
    sub-int/2addr v2, v3

    const/4 v0, -0x1

    if-eq v2, v0, :cond_5

    if-nez v2, :cond_4

    if-nez p2, :cond_4

    goto :goto_4

    .line 332
    :cond_4
    invoke-direct {p0, v1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rotateRight(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 333
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rotateLeft(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    goto :goto_5

    .line 329
    :cond_5
    :goto_4
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rotateLeft(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    :goto_5
    if-eqz p2, :cond_d

    goto :goto_9

    :cond_6
    const/4 v1, 0x2

    const/4 v6, 0x1

    if-ne v5, v1, :cond_b

    .line 340
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 341
    iget-object v3, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz v3, :cond_7

    .line 342
    iget v3, v3, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_6

    :cond_7
    const/4 v3, 0x0

    :goto_6
    if-eqz v1, :cond_8

    .line 343
    iget v2, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    :cond_8
    sub-int/2addr v2, v3

    if-eq v2, v6, :cond_a

    if-nez v2, :cond_9

    if-nez p2, :cond_9

    goto :goto_7

    .line 350
    :cond_9
    invoke-direct {p0, v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rotateLeft(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 351
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rotateRight(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    goto :goto_8

    .line 347
    :cond_a
    :goto_7
    invoke-direct {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rotateRight(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    :goto_8
    if-eqz p2, :cond_d

    goto :goto_9

    :cond_b
    if-nez v5, :cond_c

    add-int/lit8 v3, v3, 0x1

    .line 358
    iput v3, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    if-eqz p2, :cond_d

    goto :goto_9

    .line 365
    :cond_c
    invoke-static {v3, v4}, Ljava/lang/Math;->max(II)I

    move-result v0

    add-int/2addr v0, v6

    iput v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    if-nez p2, :cond_d

    goto :goto_9

    .line 314
    :cond_d
    iget-object p1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_0

    :cond_e
    :goto_9
    return-void
.end method

.method private replaceInParent(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;)V"
        }
    .end annotation

    .line 288
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v1, 0x0

    .line 289
    iput-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz p2, :cond_0

    .line 291
    iput-object v0, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    :cond_0
    if-eqz v0, :cond_2

    .line 295
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-ne v1, p1, :cond_1

    .line 296
    iput-object p2, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_0

    .line 299
    :cond_1
    iput-object p2, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_0

    .line 302
    :cond_2
    iput-object p2, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->root:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    :goto_0
    return-void
.end method

.method private rotateLeft(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;)V"
        }
    .end annotation

    .line 377
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 378
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 379
    iget-object v2, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 380
    iget-object v3, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 383
    iput-object v2, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz v2, :cond_0

    .line 385
    iput-object p1, v2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 388
    :cond_0
    invoke-direct {p0, p1, v1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 391
    iput-object p1, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 392
    iput-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v4, 0x0

    if-eqz v0, :cond_1

    .line 395
    iget v0, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    if-eqz v2, :cond_2

    .line 396
    iget v2, v2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_1

    :cond_2
    const/4 v2, 0x0

    .line 395
    :goto_1
    invoke-static {v0, v2}, Ljava/lang/Math;->max(II)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    iput v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    .line 397
    iget p1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    if-eqz v3, :cond_3

    .line 398
    iget v4, v3, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    .line 397
    :cond_3
    invoke-static {p1, v4}, Ljava/lang/Math;->max(II)I

    move-result p1

    add-int/lit8 p1, p1, 0x1

    iput p1, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    return-void
.end method

.method private rotateRight(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;)V"
        }
    .end annotation

    .line 405
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 406
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 407
    iget-object v2, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 408
    iget-object v3, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 411
    iput-object v3, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz v3, :cond_0

    .line 413
    iput-object p1, v3, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 416
    :cond_0
    invoke-direct {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 419
    iput-object p1, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 420
    iput-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v4, 0x0

    if-eqz v1, :cond_1

    .line 423
    iget v1, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    if-eqz v3, :cond_2

    .line 424
    iget v3, v3, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    goto :goto_1

    :cond_2
    const/4 v3, 0x0

    .line 423
    :goto_1
    invoke-static {v1, v3}, Ljava/lang/Math;->max(II)I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    iput v1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    .line 425
    iget p1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    if-eqz v2, :cond_3

    .line 426
    iget v4, v2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    .line 425
    :cond_3
    invoke-static {p1, v4}, Ljava/lang/Math;->max(II)I

    move-result p1

    add-int/lit8 p1, p1, 0x1

    iput p1, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    return-void
.end method

.method private writeReplace()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/ObjectStreamException;
        }
    .end annotation

    .line 631
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0, p0}, Ljava/util/LinkedHashMap;-><init>(Ljava/util/Map;)V

    return-object v0
.end method


# virtual methods
.method public clear()V
    .locals 1

    const/4 v0, 0x0

    .line 104
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->root:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v0, 0x0

    .line 105
    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->size:I

    .line 106
    iget v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    .line 109
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 110
    iput-object v0, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iput-object v0, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    return-void
.end method

.method public containsKey(Ljava/lang/Object;)Z
    .locals 0

    .line 90
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public entrySet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/util/Map$Entry<",
            "TK;TV;>;>;"
        }
    .end annotation

    .line 433
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->entrySet:Lio/dcloud/uts/gson/internal/LinkedTreeMap$EntrySet;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 434
    :cond_0
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$EntrySet;

    invoke-direct {v0, p0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$EntrySet;-><init>(Lio/dcloud/uts/gson/internal/LinkedTreeMap;)V

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->entrySet:Lio/dcloud/uts/gson/internal/LinkedTreeMap$EntrySet;

    :goto_0
    return-object v0
.end method

.method find(Ljava/lang/Object;Z)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;Z)",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 125
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->comparator:Ljava/util/Comparator;

    .line 126
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->root:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v2, 0x0

    if-eqz v1, :cond_5

    .line 132
    sget-object v3, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    if-ne v0, v3, :cond_0

    .line 133
    move-object v3, p1

    check-cast v3, Ljava/lang/Comparable;

    goto :goto_0

    :cond_0
    move-object v3, v2

    :goto_0
    if-eqz v3, :cond_1

    .line 138
    iget-object v4, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->key:Ljava/lang/Object;

    invoke-interface {v3, v4}, Ljava/lang/Comparable;->compareTo(Ljava/lang/Object;)I

    move-result v4

    goto :goto_1

    .line 139
    :cond_1
    iget-object v4, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->key:Ljava/lang/Object;

    invoke-interface {v0, p1, v4}, Ljava/util/Comparator;->compare(Ljava/lang/Object;Ljava/lang/Object;)I

    move-result v4

    :goto_1
    if-nez v4, :cond_2

    return-object v1

    :cond_2
    if-gez v4, :cond_3

    .line 147
    iget-object v5, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_2

    :cond_3
    iget-object v5, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    :goto_2
    if-nez v5, :cond_4

    goto :goto_3

    :cond_4
    move-object v1, v5

    goto :goto_0

    :cond_5
    const/4 v4, 0x0

    :goto_3
    if-nez p2, :cond_6

    return-object v2

    .line 162
    :cond_6
    iget-object p2, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v2, 0x1

    if-nez v1, :cond_9

    .line 166
    sget-object v3, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->NATURAL_ORDER:Ljava/util/Comparator;

    if-ne v0, v3, :cond_8

    instance-of v0, p1, Ljava/lang/Comparable;

    if-eqz v0, :cond_7

    goto :goto_4

    .line 167
    :cond_7
    new-instance p2, Ljava/lang/ClassCastException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " is not Comparable"

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/ClassCastException;-><init>(Ljava/lang/String;)V

    throw p2

    .line 169
    :cond_8
    :goto_4
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iget-object v3, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    invoke-direct {v0, v1, p1, p2, v3}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;-><init>(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Ljava/lang/Object;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 170
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->root:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_6

    .line 172
    :cond_9
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iget-object v3, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    invoke-direct {v0, v1, p1, p2, v3}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;-><init>(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Ljava/lang/Object;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    if-gez v4, :cond_a

    .line 174
    iput-object v0, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_5

    .line 176
    :cond_a
    iput-object v0, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 178
    :goto_5
    invoke-direct {p0, v1, v2}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rebalance(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Z)V

    .line 180
    :goto_6
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->size:I

    add-int/2addr p1, v2

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->size:I

    .line 181
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    add-int/2addr p1, v2

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    return-object v0
.end method

.method findByEntry(Ljava/util/Map$Entry;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map$Entry<",
            "**>;)",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 205
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p0, v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 206
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->value:Ljava/lang/Object;

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    invoke-direct {p0, v1, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->equal(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    const/4 v1, 0x0

    .line 189
    :try_start_0
    invoke-virtual {p0, p1, v1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->find(Ljava/lang/Object;Z)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/ClassCastException; {:try_start_0 .. :try_end_0} :catch_0

    nop

    :catch_0
    :cond_0
    return-object v0
.end method

.method public get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 85
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 86
    iget-object p1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->value:Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public keySet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "TK;>;"
        }
    .end annotation

    .line 438
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->keySet:Lio/dcloud/uts/gson/internal/LinkedTreeMap$KeySet;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 439
    :cond_0
    new-instance v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$KeySet;

    invoke-direct {v0, p0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$KeySet;-><init>(Lio/dcloud/uts/gson/internal/LinkedTreeMap;)V

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->keySet:Lio/dcloud/uts/gson/internal/LinkedTreeMap$KeySet;

    :goto_0
    return-object v0
.end method

.method public put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TK;TV;)TV;"
        }
    .end annotation

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    .line 97
    invoke-virtual {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->find(Ljava/lang/Object;Z)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object p1

    .line 98
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->value:Ljava/lang/Object;

    .line 99
    iput-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->value:Ljava/lang/Object;

    return-object v0

    .line 95
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "key == null"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public remove(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 114
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->removeInternalByKey(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 115
    iget-object p1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->value:Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method removeInternal(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Z)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;Z)V"
        }
    .end annotation

    if-eqz p2, :cond_0

    .line 222
    iget-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iput-object v0, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 223
    iget-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iput-object v0, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->prev:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 226
    :cond_0
    iget-object p2, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 227
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 228
    iget-object v1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v2, 0x0

    const/4 v3, 0x0

    if-eqz p2, :cond_4

    if-eqz v0, :cond_4

    .line 240
    iget v1, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    iget v4, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    if-le v1, v4, :cond_1

    invoke-virtual {p2}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->last()Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object p2

    goto :goto_0

    :cond_1
    invoke-virtual {v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->first()Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object p2

    .line 241
    :goto_0
    invoke-virtual {p0, p2, v2}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->removeInternal(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Z)V

    .line 244
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz v0, :cond_2

    .line 246
    iget v1, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    .line 247
    iput-object v0, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 248
    iput-object p2, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 249
    iput-object v3, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_1

    :cond_2
    const/4 v1, 0x0

    .line 253
    :goto_1
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz v0, :cond_3

    .line 255
    iget v2, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    .line 256
    iput-object v0, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 257
    iput-object p2, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->parent:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 258
    iput-object v3, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 261
    :cond_3
    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    iput v0, p2, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->height:I

    .line 262
    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    return-void

    :cond_4
    if-eqz p2, :cond_5

    .line 265
    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 266
    iput-object v3, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->left:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_2

    :cond_5
    if-eqz v0, :cond_6

    .line 268
    invoke-direct {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 269
    iput-object v3, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->right:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    goto :goto_2

    .line 271
    :cond_6
    invoke-direct {p0, p1, v3}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->replaceInParent(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;)V

    .line 274
    :goto_2
    invoke-direct {p0, v1, v2}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->rebalance(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Z)V

    .line 275
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->size:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->size:I

    .line 276
    iget p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    return-void
.end method

.method removeInternalByKey(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 280
    invoke-virtual {p0, p1}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->findByObject(Ljava/lang/Object;)Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    .line 282
    invoke-virtual {p0, p1, v0}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->removeInternal(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Z)V

    :cond_0
    return-object p1
.end method

.method public size()I
    .locals 1

    .line 81
    iget v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->size:I

    return v0
.end method
