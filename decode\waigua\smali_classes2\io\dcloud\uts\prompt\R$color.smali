.class public final Lio/dcloud/uts/prompt/R$color;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/prompt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "color"
.end annotation


# static fields
.field public static uni_app_uni_prompt_button_cancel_text_color:I = 0x7f050072

.field public static uni_app_uni_prompt_button_cancel_text_color_night:I = 0x7f050073

.field public static uni_app_uni_prompt_button_sure_text_color:I = 0x7f050074

.field public static uni_app_uni_prompt_button_sure_text_color_night:I = 0x7f050075

.field public static uni_app_uni_prompt_dialog_edit_text_bg:I = 0x7f050076

.field public static uni_app_uni_prompt_dialog_edit_text_bg_night:I = 0x7f050077

.field public static uni_app_uni_prompt_dialog_edit_text_color:I = 0x7f050078

.field public static uni_app_uni_prompt_dialog_edit_text_color_night:I = 0x7f050079

.field public static uni_app_uni_prompt_dialog_edit_text_hint:I = 0x7f05007a

.field public static uni_app_uni_prompt_dialog_edit_text_hint_night:I = 0x7f05007b

.field public static uni_app_uni_prompt_dialog_sub_text:I = 0x7f05007c

.field public static uni_app_uni_prompt_dialog_sub_text_night:I = 0x7f05007d

.field public static uni_app_uni_prompt_dialog_title_text:I = 0x7f05007e

.field public static uni_app_uni_prompt_dialog_title_text_night:I = 0x7f05007f

.field public static uni_app_uni_prompt_light_bg:I = 0x7f050080

.field public static uni_app_uni_prompt_light_bg_main:I = 0x7f050081

.field public static uni_app_uni_prompt_light_gray:I = 0x7f050082

.field public static uni_app_uni_prompt_night_bg:I = 0x7f050083

.field public static uni_app_uni_prompt_night_bg_hair_line:I = 0x7f050084

.field public static uni_app_uni_prompt_night_bg_hair_line_night:I = 0x7f050085

.field public static uni_app_uni_prompt_night_bg_main:I = 0x7f050086

.field public static uni_app_uni_prompt_night_bg_split:I = 0x7f050087

.field public static uni_app_uni_prompt_night_bg_split_night:I = 0x7f050088

.field public static uni_app_uni_prompt_white:I = 0x7f050089


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
