.class public final Lio/dcloud/uts/android/AndroidUTSContext;
.super Ljava/lang/Object;
.source "AndroidUTSContext.kt"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0004\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0015\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010I\u001a\u00020\u0006J\u000e\u0010J\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0015J\u001c\u0010K\u001a\u00020\u00062\u0006\u0010L\u001a\u0002082\u000c\u0010?\u001a\u0008\u0012\u0004\u0012\u00020807R&\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0007\u0010\u0008\"\u0004\u0008\t\u0010\nR&\u0010\u000b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u000c\u0010\u0008\"\u0004\u0008\r\u0010\nR\u001c\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0010\u0010\u0011\"\u0004\u0008\u0012\u0010\u0013R\u001c\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0016\u0010\u0017\"\u0004\u0008\u0018\u0010\u0019Rg\u0010\u001a\u001aO\u0012K\u0012I\u0012\u0013\u0012\u00110\u001c\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008(\u001f\u0012\u0013\u0012\u00110\u001c\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008( \u0012\u0015\u0012\u0013\u0018\u00010!\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008(\"\u0012\u0004\u0012\u00020\u00060\u001b0\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008#\u0010\u0008\"\u0004\u0008$\u0010\nR;\u0010%\u001a#\u0012\u001f\u0012\u001d\u0012\u0013\u0012\u00110\'\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008((\u0012\u0004\u0012\u00020\u00060&0\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008)\u0010\u0008\"\u0004\u0008*\u0010\nR&\u0010+\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008,\u0010\u0008\"\u0004\u0008-\u0010\nR;\u0010.\u001a#\u0012\u001f\u0012\u001d\u0012\u0013\u0012\u00110/\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008(0\u0012\u0004\u0012\u00020\u00060&0\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00081\u0010\u0008\"\u0004\u00082\u0010\nR&\u00103\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00084\u0010\u0008\"\u0004\u00085\u0010\nR)\u00106\u001a\u001a\u0012\u0016\u0012\u0014\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020807\u0012\u0004\u0012\u00020\u00060&0\u0004\u00a2\u0006\u0008\n\u0000\u001a\u0004\u00089\u0010\u0008R)\u0010:\u001a\u001a\u0012\u0016\u0012\u0014\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020807\u0012\u0004\u0012\u00020\u00060&0\u0004\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008;\u0010\u0008R)\u0010<\u001a\u001a\u0012\u0016\u0012\u0014\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020807\u0012\u0004\u0012\u00020\u00060&0\u0004\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008=\u0010\u0008Rq\u0010>\u001aY\u0012U\u0012S\u0012\u0013\u0012\u00110\u001c\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008(\u001f\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020807\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008(?\u0012\u0019\u0012\u0017\u0012\u0004\u0012\u00020/07\u00a2\u0006\u000c\u0008\u001d\u0012\u0008\u0008\u001e\u0012\u0004\u0008\u0008(@\u0012\u0004\u0012\u00020\u00060\u001b0\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008A\u0010\u0008\"\u0004\u0008B\u0010\nR&\u0010C\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008D\u0010\u0008\"\u0004\u0008E\u0010\nR&\u0010F\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008G\u0010\u0008\"\u0004\u0008H\u0010\n\u00a8\u0006M"
    }
    d2 = {
        "Lio/dcloud/uts/android/AndroidUTSContext;",
        "",
        "()V",
        "backListenFunc",
        "Ljava/util/concurrent/CopyOnWriteArrayList;",
        "Lkotlin/Function0;",
        "",
        "getBackListenFunc",
        "()Ljava/util/concurrent/CopyOnWriteArrayList;",
        "setBackListenFunc",
        "(Ljava/util/concurrent/CopyOnWriteArrayList;)V",
        "destroyListenFunc",
        "getDestroyListenFunc",
        "setDestroyListenFunc",
        "hostAppContext",
        "Landroid/content/Context;",
        "getHostAppContext",
        "()Landroid/content/Context;",
        "setHostAppContext",
        "(Landroid/content/Context;)V",
        "instance",
        "Lio/dcloud/feature/uniapp/AbsSDKInstance;",
        "getInstance",
        "()Lio/dcloud/feature/uniapp/AbsSDKInstance;",
        "setInstance",
        "(Lio/dcloud/feature/uniapp/AbsSDKInstance;)V",
        "onActivityResultListenFunc",
        "Lkotlin/Function3;",
        "",
        "Lkotlin/ParameterName;",
        "name",
        "requestCode",
        "resultCode",
        "Landroid/content/Intent;",
        "data",
        "getOnActivityResultListenFunc",
        "setOnActivityResultListenFunc",
        "onConfigChangedListenFunc",
        "Lkotlin/Function1;",
        "Lio/dcloud/uts/UTSJSONObject;",
        "config",
        "getOnConfigChangedListenFunc",
        "setOnConfigChangedListenFunc",
        "onCreateListenFunc",
        "getOnCreateListenFunc",
        "setOnCreateListenFunc",
        "onTrimMemoryListenFunc",
        "",
        "level",
        "getOnTrimMemoryListenFunc",
        "setOnTrimMemoryListenFunc",
        "pauseListenFunc",
        "getPauseListenFunc",
        "setPauseListenFunc",
        "permissionConfirmFunc",
        "Lio/dcloud/uts/UTSArray;",
        "",
        "getPermissionConfirmFunc",
        "permissionRequestFinishedFunc",
        "getPermissionRequestFinishedFunc",
        "permissionRequestFunc",
        "getPermissionRequestFunc",
        "permissionsResultListenFunc",
        "permissions",
        "grantResults",
        "getPermissionsResultListenFunc",
        "setPermissionsResultListenFunc",
        "resumeListenFunc",
        "getResumeListenFunc",
        "setResumeListenFunc",
        "stopListenFunc",
        "getStopListenFunc",
        "setStopListenFunc",
        "initApp",
        "initContext",
        "permission",
        "type",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

.field private static backListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static destroyListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static hostAppContext:Landroid/content/Context;

.field private static instance:Lio/dcloud/feature/uniapp/AbsSDKInstance;

.field private static onActivityResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function3<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            "Landroid/content/Intent;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static onConfigChangedListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSJSONObject;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static onCreateListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static onTrimMemoryListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Number;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static pauseListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static final permissionConfirmFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static final permissionRequestFinishedFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static final permissionRequestFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static permissionsResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function3<",
            "Ljava/lang/Integer;",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static resumeListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field

.field private static stopListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lio/dcloud/uts/android/AndroidUTSContext;

    invoke-direct {v0}, Lio/dcloud/uts/android/AndroidUTSContext;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->INSTANCE:Lio/dcloud/uts/android/AndroidUTSContext;

    .line 227
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onActivityResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 228
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->destroyListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 229
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->pauseListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 230
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->resumeListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 231
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->stopListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 232
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->backListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 233
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionsResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 238
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onTrimMemoryListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 239
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onConfigChangedListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 240
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onCreateListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 242
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionRequestFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 243
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionConfirmFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 244
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionRequestFinishedFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 222
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final getBackListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 232
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->backListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getDestroyListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 228
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->destroyListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getHostAppContext()Landroid/content/Context;
    .locals 1

    .line 224
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->hostAppContext:Landroid/content/Context;

    return-object v0
.end method

.method public final getInstance()Lio/dcloud/feature/uniapp/AbsSDKInstance;
    .locals 1

    .line 225
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->instance:Lio/dcloud/feature/uniapp/AbsSDKInstance;

    return-object v0
.end method

.method public final getOnActivityResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function3<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            "Landroid/content/Intent;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 227
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onActivityResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getOnConfigChangedListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSJSONObject;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 239
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onConfigChangedListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getOnCreateListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 240
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onCreateListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getOnTrimMemoryListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Number;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 238
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->onTrimMemoryListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getPauseListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 229
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->pauseListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getPermissionConfirmFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 243
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionConfirmFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getPermissionRequestFinishedFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 244
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionRequestFinishedFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getPermissionRequestFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 242
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionRequestFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getPermissionsResultListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function3<",
            "Ljava/lang/Integer;",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 233
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->permissionsResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getResumeListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 230
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->resumeListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final getStopListenFunc()Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;"
        }
    .end annotation

    .line 231
    sget-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->stopListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object v0
.end method

.method public final initApp()V
    .locals 3

    .line 257
    invoke-static {}, Lio/dcloud/common/DHInterface/message/ActionBus;->getInstance()Lio/dcloud/common/DHInterface/message/ActionBus;

    move-result-object v0

    new-instance v1, Lio/dcloud/uts/android/AndroidUTSContext$initApp$2;

    invoke-direct {v1}, Lio/dcloud/uts/android/AndroidUTSContext$initApp$2;-><init>()V

    new-instance v2, Lio/dcloud/uts/android/AndroidUTSContext$initApp$1;

    invoke-direct {v2, v1}, Lio/dcloud/uts/android/AndroidUTSContext$initApp$1;-><init>(Lio/dcloud/uts/android/AndroidUTSContext$initApp$2;)V

    check-cast v2, Lio/dcloud/common/DHInterface/message/AbsActionObserver;

    invoke-virtual {v0, v2}, Lio/dcloud/common/DHInterface/message/ActionBus;->observeAction(Lio/dcloud/common/DHInterface/message/AbsActionObserver;)Z

    return-void
.end method

.method public final initContext(Lio/dcloud/feature/uniapp/AbsSDKInstance;)V
    .locals 1

    const-string v0, "instance"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 249
    invoke-interface {p1}, Lio/dcloud/feature/uniapp/AbsSDKInstance;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    sput-object v0, Lio/dcloud/uts/android/AndroidUTSContext;->hostAppContext:Landroid/content/Context;

    .line 250
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->instance:Lio/dcloud/feature/uniapp/AbsSDKInstance;

    return-void
.end method

.method public final permission(Ljava/lang/String;Lio/dcloud/uts/UTSArray;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const-string v0, "type"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "permissions"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 327
    sget-object v0, Lio/dcloud/common/DHInterface/message/action/PermissionRequestAction;->TYPE_REQUEST:Ljava/lang/String;

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 328
    sget-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->permissionRequestFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lkotlin/jvm/functions/Function1;

    .line 329
    invoke-interface {v0, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 333
    :cond_0
    sget-object v0, Lio/dcloud/common/DHInterface/message/action/PermissionRequestAction;->TYPE_CONFIRM:Ljava/lang/String;

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 334
    sget-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->permissionConfirmFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lkotlin/jvm/functions/Function1;

    .line 335
    invoke-interface {v0, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 339
    :cond_1
    sget-object v0, Lio/dcloud/common/DHInterface/message/action/PermissionRequestAction;->TYPE_COMPLETE:Ljava/lang/String;

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 340
    sget-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->permissionRequestFinishedFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lkotlin/jvm/functions/Function1;

    .line 341
    invoke-interface {v0, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_2
    return-void
.end method

.method public final setBackListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 232
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->backListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setDestroyListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 228
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->destroyListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setHostAppContext(Landroid/content/Context;)V
    .locals 0

    .line 224
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->hostAppContext:Landroid/content/Context;

    return-void
.end method

.method public final setInstance(Lio/dcloud/feature/uniapp/AbsSDKInstance;)V
    .locals 0

    .line 225
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->instance:Lio/dcloud/feature/uniapp/AbsSDKInstance;

    return-void
.end method

.method public final setOnActivityResultListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function3<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            "Landroid/content/Intent;",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 227
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->onActivityResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setOnConfigChangedListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Lio/dcloud/uts/UTSJSONObject;",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 239
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->onConfigChangedListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setOnCreateListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 240
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->onCreateListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setOnTrimMemoryListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Number;",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 238
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->onTrimMemoryListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setPauseListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 229
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->pauseListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setPermissionsResultListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function3<",
            "Ljava/lang/Integer;",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/String;",
            ">;",
            "Lio/dcloud/uts/UTSArray<",
            "Ljava/lang/Number;",
            ">;",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 233
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->permissionsResultListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setResumeListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 230
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->resumeListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method

.method public final setStopListenFunc(Ljava/util/concurrent/CopyOnWriteArrayList;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;>;)V"
        }
    .end annotation

    const-string v0, "<set-?>"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 231
    sput-object p1, Lio/dcloud/uts/android/AndroidUTSContext;->stopListenFunc:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-void
.end method
