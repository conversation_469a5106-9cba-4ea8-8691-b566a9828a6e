.class public final Lio/dcloud/uts/prompt/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/prompt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static dcloud_iv_loading:I = 0x7f080086

.field public static dcloud_pb_loading:I = 0x7f080087

.field public static dcloud_pd_root:I = 0x7f080088

.field public static dcloud_tv_loading:I = 0x7f080096

.field public static dcloud_view_seaparator:I = 0x7f080097

.field public static et_content:I = 0x7f0800a7

.field public static line_bottom:I = 0x7f0800e1

.field public static line_cancel:I = 0x7f0800e2

.field public static line_content:I = 0x7f0800e3

.field public static line_title:I = 0x7f0800e4

.field public static myRecyclerview:I = 0x7f0800f6

.field public static root_layout:I = 0x7f08011a

.field public static tvCancelAction:I = 0x7f08016b

.field public static tvName:I = 0x7f08016c

.field public static tvSureAction:I = 0x7f08016d

.field public static tvTitle:I = 0x7f08016e

.field public static tv_content:I = 0x7f080173

.field public static tv_title:I = 0x7f080181

.field public static view_split_line_h:I = 0x7f080189

.field public static view_split_line_v:I = 0x7f08018a


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
