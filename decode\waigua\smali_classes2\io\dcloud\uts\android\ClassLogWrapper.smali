.class public final Lio/dcloud/uts/android/ClassLogWrapper;
.super Ljava/lang/Object;
.source "ClassLogWrapper.kt"


# annotations
.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nClassLogWrapper.kt\nKotlin\n*S Kotlin\n*F\n+ 1 ClassLogWrapper.kt\nio/dcloud/uts/android/ClassLogWrapper\n+ 2 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n+ 3 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n+ 4 _Arrays.kt\nkotlin/collections/ArraysKt___ArraysKt\n*L\n1#1,1146:1\n215#2:1147\n216#2:1152\n215#2,2:1159\n1855#3,2:1148\n1855#3,2:1150\n1855#3,2:1154\n1855#3,2:1156\n1855#3,2:1161\n1855#3,2:1163\n1855#3:1165\n1855#3,2:1166\n1855#3,2:1168\n1856#3:1170\n1855#3,2:1171\n1855#3,2:1191\n1855#3:1193\n1855#3,2:1194\n1855#3,2:1196\n1856#3:1198\n13579#4:1153\n13580#4:1158\n13579#4,2:1173\n13586#4,2:1175\n13635#4,2:1177\n13593#4,2:1179\n13600#4,2:1181\n13607#4,2:1183\n13614#4,2:1185\n13621#4,2:1187\n13628#4,2:1189\n*S KotlinDebug\n*F\n+ 1 ClassLogWrapper.kt\nio/dcloud/uts/android/ClassLogWrapper\n*L\n183#1:1147\n183#1:1152\n477#1:1159,2\n220#1:1148,2\n232#1:1150,2\n311#1:1154,2\n325#1:1156,2\n497#1:1161,2\n524#1:1163,2\n600#1:1165\n661#1:1166,2\n675#1:1168,2\n600#1:1170\n711#1:1171,2\n883#1:1191,2\n901#1:1193\n949#1:1194,2\n963#1:1196,2\n901#1:1198\n277#1:1153\n277#1:1158\n765#1:1173,2\n778#1:1175,2\n791#1:1177,2\n804#1:1179,2\n817#1:1181,2\n830#1:1183,2\n843#1:1185,2\n857#1:1187,2\n871#1:1189,2\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002J:\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00012\u0008\u0010\u0006\u001a\u0004\u0018\u00010\u00012\u0016\u0010\u0007\u001a\u0012\u0012\u0004\u0012\u00020\u00010\u0008j\u0008\u0012\u0004\u0012\u00020\u0001`\t2\u0006\u0010\n\u001a\u00020\u000bH\u0002JV\u0010\u000c\u001a\u00020\r2\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u00020\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u00010\u000f2\u0016\u0010\u0007\u001a\u0012\u0012\u0004\u0012\u00020\u00010\u0008j\u0008\u0012\u0004\u0012\u00020\u0001`\t2\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u0001H\u0002J\u0012\u0010\u0015\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0016\u001a\u00020\u0001H\u0002J\u0014\u0010\u0017\u001a\u0004\u0018\u00010\u00102\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u0002J\u001c\u0010\u0018\u001a\u00020\u00102\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u00012\u0008\u0008\u0002\u0010\u0019\u001a\u00020\u001aH\u0002J\u001a\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u00102\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u0002J<\u0010\u001e\u001a\u00020\u00042\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u00012\u0008\u0010\u0006\u001a\u0004\u0018\u00010\u00012\u0016\u0010\u0007\u001a\u0012\u0012\u0004\u0012\u00020\u00010\u0008j\u0008\u0012\u0004\u0012\u00020\u0001`\t2\u0008\u0008\u0002\u0010\u001f\u001a\u00020\u000bJ<\u0010 \u001a\u00020\u00122\n\u0010!\u001a\u0006\u0012\u0002\u0008\u00030\"2\u0016\u0010\u0007\u001a\u0012\u0012\u0004\u0012\u00020\u00010\u0008j\u0008\u0012\u0004\u0012\u00020\u0001`\t2\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\u000bH\u0002J\u0010\u0010#\u001a\u00020\u00102\u0008\u0010$\u001a\u0004\u0018\u00010\u0001\u00a8\u0006%"
    }
    d2 = {
        "Lio/dcloud/uts/android/ClassLogWrapper;",
        "",
        "()V",
        "doJsonConvert",
        "Lio/dcloud/uts/gson/JsonElement;",
        "objInstance",
        "parentField",
        "inputStock",
        "Ljava/util/HashSet;",
        "Lkotlin/collections/HashSet;",
        "nextLimitCounter",
        "Ljava/util/concurrent/atomic/AtomicInteger;",
        "fillRootJsonWithMap",
        "",
        "customMap",
        "",
        "",
        "rootJsonObject",
        "Lio/dcloud/uts/gson/JsonObject;",
        "valueProperObject",
        "Lio/dcloud/uts/gson/JsonArray;",
        "logClassOf",
        "input",
        "logSubTypeOf",
        "logTypeOf",
        "isReturnTag",
        "",
        "needSubType",
        "type",
        "value",
        "wrapClass",
        "limitCounter",
        "wrapClassInfo",
        "classObj",
        "Ljava/lang/Class;",
        "wrapNumberText",
        "logValue",
        "utsplugin_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-direct {v0}, Lio/dcloud/uts/android/ClassLogWrapper;-><init>()V

    sput-object v0, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 34
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private final doJsonConvert(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;
    .locals 33
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Ljava/util/HashSet<",
            "Ljava/lang/Object;",
            ">;",
            "Ljava/util/concurrent/atomic/AtomicInteger;",
            ")",
            "Lio/dcloud/uts/gson/JsonElement;"
        }
    .end annotation

    move-object/from16 v7, p0

    move-object/from16 v8, p1

    move-object/from16 v9, p3

    move-object/from16 v10, p4

    .line 366
    invoke-virtual/range {p4 .. p4}, Ljava/util/concurrent/atomic/AtomicInteger;->decrementAndGet()I

    .line 375
    instance-of v0, v8, Lio/dcloud/uts/log/LogSelfV2Simple;

    const-string v11, "subType"

    const-string v12, "type"

    const-string v13, "value"

    if-eqz v0, :cond_0

    .line 376
    move-object v0, v8

    check-cast v0, Lio/dcloud/uts/log/LogSelfV2Simple;

    invoke-interface {v0}, Lio/dcloud/uts/log/LogSelfV2Simple;->toLogV2Simple()Ljava/lang/Object;

    move-result-object v0

    .line 377
    instance-of v1, v0, Lio/dcloud/uts/gson/JsonElement;

    if-eqz v1, :cond_b

    .line 378
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 385
    :cond_0
    instance-of v0, v8, Lio/dcloud/uts/android/UTSLogInfo;

    if-eqz v0, :cond_2

    .line 389
    move-object v0, v8

    check-cast v0, Lio/dcloud/uts/android/UTSLogInfo;

    invoke-virtual {v0}, Lio/dcloud/uts/android/UTSLogInfo;->getType()Ljava/lang/String;

    move-result-object v1

    .line 390
    invoke-virtual {v0}, Lio/dcloud/uts/android/UTSLogInfo;->getValue()Ljava/lang/Object;

    move-result-object v2

    .line 391
    invoke-virtual {v0}, Lio/dcloud/uts/android/UTSLogInfo;->getSubtype()Ljava/lang/String;

    move-result-object v0

    .line 393
    new-instance v3, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v3}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 394
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v4, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v3, v12, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 395
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual {v7, v2}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapNumberText(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v3, v13, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 397
    move-object v1, v0

    check-cast v1, Ljava/lang/CharSequence;

    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    move-result v1

    if-lez v1, :cond_1

    .line 399
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v1, v0}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v3, v11, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 401
    :cond_1
    check-cast v3, Lio/dcloud/uts/gson/JsonElement;

    return-object v3

    .line 405
    :cond_2
    instance-of v0, v8, Ljava/lang/Number;

    if-eqz v0, :cond_5

    if-nez p2, :cond_4

    .line 410
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 411
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v2, Lio/dcloud/uts/UTSAndroid;->INSTANCE:Lio/dcloud/uts/UTSAndroid;

    invoke-virtual {v2, v8}, Lio/dcloud/uts/UTSAndroid;->typeof(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 412
    const-string v1, "number"

    invoke-direct {v7, v1, v8}, Lio/dcloud/uts/android/ClassLogWrapper;->needSubType(Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    .line 413
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v2, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v11, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 415
    :cond_3
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p0 .. p1}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapNumberText(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v13, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 416
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 419
    :cond_4
    new-instance v0, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p0 .. p1}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapNumberText(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 422
    :cond_5
    instance-of v0, v8, Ljava/lang/String;

    const-string v1, "string"

    if-eqz v0, :cond_7

    if-nez p2, :cond_6

    .line 425
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 426
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v2, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 427
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v13, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 428
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 431
    :cond_6
    new-instance v0, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 434
    :cond_7
    instance-of v0, v8, Ljava/lang/Character;

    if-eqz v0, :cond_9

    if-nez p2, :cond_8

    .line 437
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 438
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string v3, "Char"

    invoke-direct {v2, v3}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 439
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v2, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v11, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 440
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v13, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 441
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 444
    :cond_8
    new-instance v0, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 447
    :cond_9
    instance-of v0, v8, Ljava/lang/Boolean;

    if-eqz v0, :cond_b

    if-nez p2, :cond_a

    .line 450
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 451
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string v2, "boolean"

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 452
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v13, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 453
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 456
    :cond_a
    new-instance v0, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 463
    :cond_b
    invoke-virtual/range {p4 .. p4}, Ljava/util/concurrent/atomic/AtomicInteger;->get()I

    move-result v0

    if-gez v0, :cond_c

    .line 464
    new-instance v0, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string v1, "[depth-max-limit]"

    invoke-direct {v0, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 467
    :cond_c
    instance-of v0, v8, Ljava/util/Map;

    const-string v1, "entries"

    const/16 v2, 0x29

    const-string v3, "description"

    const-string v14, "object"

    const/4 v15, 0x0

    if-eqz v0, :cond_e

    .line 468
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 469
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v4, v14}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 470
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string v5, "map"

    invoke-direct {v4, v5}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v11, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 471
    new-instance v4, Ljava/lang/StringBuilder;

    const-string v5, "Map("

    invoke-direct {v4, v5}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    move-object v5, v8

    check-cast v5, Ljava/util/Map;

    invoke-interface {v5}, Ljava/util/Map;->size()I

    move-result v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 472
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v4, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v3, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 476
    new-instance v4, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v4}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 1159
    invoke-interface {v5}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :goto_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_d

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/util/Map$Entry;

    .line 478
    new-instance v8, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v8}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 479
    sget-object v11, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v6}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v12

    invoke-virtual {v11, v12, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v12

    const-string v14, "key"

    invoke-virtual {v8, v14, v12}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 480
    invoke-interface {v6}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v6

    invoke-virtual {v11, v6, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v6

    invoke-virtual {v8, v13, v6}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 481
    check-cast v8, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v4, v8}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_0

    .line 483
    :cond_d
    new-instance v5, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v5}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 484
    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v5, v1, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 485
    check-cast v5, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v13, v5}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 487
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v3, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 488
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 490
    :cond_e
    instance-of v0, v8, Ljava/util/Set;

    if-eqz v0, :cond_10

    .line 491
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 492
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v4, v14}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 493
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string v5, "set"

    invoke-direct {v4, v5}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v11, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 496
    new-instance v4, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v4}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 497
    move-object v5, v8

    check-cast v5, Ljava/lang/Iterable;

    .line 1161
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :goto_1
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_f

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    .line 498
    new-instance v11, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v11}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 499
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-virtual {v12, v6, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v6

    .line 500
    invoke-virtual {v11, v13, v6}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 501
    check-cast v11, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v4, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_1

    .line 503
    :cond_f
    new-instance v5, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v5}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 504
    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v5, v1, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 505
    check-cast v5, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v13, v5}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 507
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v4, "Set("

    invoke-direct {v1, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    move-object v4, v8

    check-cast v4, Ljava/util/Set;

    invoke-interface {v4}, Ljava/util/Set;->size()I

    move-result v4

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 508
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v2, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v3, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 509
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 514
    :cond_10
    instance-of v0, v8, Lkotlin/Function;

    const-string v6, "returned"

    const-string v5, "kotlin.Unit"

    const-string v4, "parameter"

    const/4 v3, 0x2

    const/4 v2, 0x0

    const/4 v1, 0x1

    if-eqz v0, :cond_14

    .line 516
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    if-nez p2, :cond_11

    .line 518
    new-instance v9, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string v10, "function"

    invoke-direct {v9, v10}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v9, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v9}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 521
    :cond_11
    check-cast v8, Lkotlin/Function;

    invoke-static {v8}, Lkotlin/reflect/jvm/ReflectLambdaKt;->reflect(Lkotlin/Function;)Lkotlin/reflect/KFunction;

    move-result-object v8

    if-eqz v8, :cond_13

    .line 523
    new-instance v9, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v9}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 524
    invoke-interface {v8}, Lkotlin/reflect/KFunction;->getParameters()Ljava/util/List;

    move-result-object v10

    check-cast v10, Ljava/lang/Iterable;

    .line 1163
    invoke-interface {v10}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v10

    :goto_2
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    move-result v11

    if-eqz v11, :cond_12

    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lkotlin/reflect/KParameter;

    .line 525
    new-instance v12, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v13, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v11}, Lkotlin/reflect/KParameter;->getType()Lkotlin/reflect/KType;

    move-result-object v11

    invoke-virtual {v11}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-direct {v13, v11, v1}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v11

    invoke-direct {v12, v11}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v12, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v9, v12}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_2

    .line 527
    :cond_12
    check-cast v9, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v4, v9}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 529
    invoke-interface {v8}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v5, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_13

    .line 530
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-interface {v8}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v7, v4, v2, v3, v15}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v6, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 534
    :cond_13
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 540
    :cond_14
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 542
    invoke-static {v7, v8, v2, v3, v15}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    .line 543
    invoke-direct/range {p0 .. p1}, Lio/dcloud/uts/android/ClassLogWrapper;->logSubTypeOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    .line 544
    const-string v3, "class"

    invoke-static {v3, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    .line 545
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v18

    invoke-static/range {v18 .. v18}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v18

    .line 547
    invoke-static/range {v18 .. v18}, Lkotlin/jvm/JvmClassMappingKt;->getJavaClass(Lkotlin/reflect/KClass;)Ljava/lang/Class;

    move-result-object v19

    invoke-virtual/range {v19 .. v19}, Ljava/lang/Class;->isAnonymousClass()Z

    move-result v19

    .line 548
    new-instance v15, Lio/dcloud/uts/gson/JsonPrimitive;

    move-object/from16 v20, v4

    invoke-direct/range {p0 .. p1}, Lio/dcloud/uts/android/ClassLogWrapper;->logClassOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v15, v4}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    const-string v4, "className"

    invoke-virtual {v0, v4, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    if-nez p2, :cond_15

    .line 555
    new-instance v15, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v15, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 556
    invoke-direct {v7, v1, v8}, Lio/dcloud/uts/android/ClassLogWrapper;->needSubType(Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_15

    .line 557
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v1, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v11, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 561
    :cond_15
    new-instance v15, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v15}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 562
    new-instance v1, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v1}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 563
    new-instance v21, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct/range {v21 .. v21}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    move-object/from16 p2, v1

    .line 570
    instance-of v1, v8, Ljava/lang/Class;

    if-eqz v1, :cond_16

    .line 571
    move-object v1, v8

    check-cast v1, Ljava/lang/Class;

    invoke-direct {v7, v1, v9, v0, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClassInfo(Ljava/lang/Class;Ljava/util/HashSet;Lio/dcloud/uts/gson/JsonObject;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonObject;

    move-result-object v0

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0

    .line 576
    :cond_16
    invoke-static {v14, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    const-string v7, "Companion"

    move-object/from16 v22, v15

    const-string v15, "kotlin.Number"

    move-object/from16 v23, v13

    const-string v13, "properties"

    move-object/from16 v24, v13

    const-string v13, "name"

    if-eqz v1, :cond_2b

    .line 578
    const-string v2, "__$originalPosition"

    if-nez v3, :cond_17

    instance-of v1, v8, Lio/dcloud/uts/log/LogSelfV2;

    if-eqz v1, :cond_17

    .line 580
    move-object v1, v8

    check-cast v1, Lio/dcloud/uts/log/LogSelfV2;

    invoke-interface {v1}, Lio/dcloud/uts/log/LogSelfV2;->toLogMap()Ljava/util/Map;

    move-result-object v1

    move-object v7, v0

    move-object/from16 v0, p0

    move-object/from16 v11, p2

    const/4 v12, 0x1

    move-object v15, v2

    const/4 v14, 0x0

    move-object/from16 v2, p3

    move-object v3, v7

    move-object/from16 v4, p4

    move-object v5, v11

    move-object/from16 v6, p1

    .line 584
    invoke-direct/range {v0 .. v6}, Lio/dcloud/uts/android/ClassLogWrapper;->fillRootJsonWithMap(Ljava/util/Map;Ljava/util/HashSet;Lio/dcloud/uts/gson/JsonObject;Ljava/util/concurrent/atomic/AtomicInteger;Lio/dcloud/uts/gson/JsonArray;Ljava/lang/Object;)V

    move-object v6, v7

    move-object v3, v15

    goto/16 :goto_f

    :cond_17
    move-object/from16 v3, p2

    move-object v1, v2

    move-object/from16 v16, v14

    const/4 v14, 0x1

    move-object v2, v0

    .line 586
    invoke-interface/range {v18 .. v18}, Lkotlin/reflect/KClass;->getObjectInstance()Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0, v8}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_27

    .line 590
    instance-of v0, v8, Lio/dcloud/uts/log/ILogParent;

    if-eqz v0, :cond_18

    .line 594
    move-object v0, v8

    check-cast v0, Lio/dcloud/uts/log/ILogParent;

    invoke-interface {v0}, Lio/dcloud/uts/log/ILogParent;->toLogMap()Ljava/util/Map;

    move-result-object v25

    move-object/from16 v0, p0

    move-object/from16 v27, v1

    move-object/from16 v1, v25

    move-object/from16 p2, v2

    move-object/from16 v2, p3

    move-object/from16 v17, v3

    move-object/from16 v3, p2

    move-object/from16 v29, v4

    move-object/from16 v28, v20

    move-object/from16 v4, p4

    move-object/from16 v30, v5

    move-object/from16 v5, v17

    move-object/from16 v31, v6

    move-object/from16 v6, p1

    .line 595
    invoke-direct/range {v0 .. v6}, Lio/dcloud/uts/android/ClassLogWrapper;->fillRootJsonWithMap(Ljava/util/Map;Ljava/util/HashSet;Lio/dcloud/uts/gson/JsonObject;Ljava/util/concurrent/atomic/AtomicInteger;Lio/dcloud/uts/gson/JsonArray;Ljava/lang/Object;)V

    goto :goto_3

    :cond_18
    move-object/from16 v27, v1

    move-object/from16 p2, v2

    move-object/from16 v17, v3

    move-object/from16 v29, v4

    move-object/from16 v30, v5

    move-object/from16 v31, v6

    move-object/from16 v28, v20

    .line 598
    :goto_3
    invoke-static/range {v18 .. v18}, Lkotlin/reflect/full/KClasses;->getDeclaredMemberProperties(Lkotlin/reflect/KClass;)Ljava/util/Collection;

    move-result-object v0

    .line 600
    check-cast v0, Ljava/lang/Iterable;

    .line 1165
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_26

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lkotlin/reflect/KProperty1;

    .line 602
    invoke-interface {v1}, Lkotlin/reflect/KProperty1;->getName()Ljava/lang/String;

    move-result-object v2

    .line 607
    :try_start_0
    move-object v3, v1

    check-cast v3, Lkotlin/reflect/KCallable;

    invoke-static {v3, v14}, Lkotlin/reflect/jvm/KCallablesJvm;->setAccessible(Lkotlin/reflect/KCallable;Z)V

    .line 608
    new-array v3, v14, [Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v4, 0x0

    :try_start_1
    aput-object v8, v3, v4

    invoke-interface {v1, v3}, Lkotlin/reflect/KProperty1;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    move-object v5, v3

    const/4 v3, 0x0

    goto :goto_5

    :catchall_0
    const/4 v4, 0x0

    :catchall_1
    nop

    const/4 v3, 0x1

    const/4 v5, 0x0

    :goto_5
    if-eqz v3, :cond_19

    move-object/from16 v6, p2

    move-object/from16 p2, v0

    move-object/from16 v20, v12

    move-object/from16 v12, v16

    move-object/from16 v5, v17

    move-object/from16 v1, v23

    move-object/from16 v3, v27

    :goto_6
    move-object/from16 v4, v29

    goto/16 :goto_e

    .line 617
    :cond_19
    instance-of v3, v5, Lio/dcloud/uts/UTSSourceMapPosition;

    if-eqz v3, :cond_1b

    move-object/from16 v3, v27

    invoke-static {v3, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_1a

    .line 618
    new-instance v1, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v1}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 619
    check-cast v5, Lio/dcloud/uts/UTSSourceMapPosition;

    invoke-virtual {v5}, Lio/dcloud/uts/UTSSourceMapPosition;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v13, v2}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 620
    const-string v2, "file"

    invoke-virtual {v5}, Lio/dcloud/uts/UTSSourceMapPosition;->getFile()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v1, v2, v6}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 621
    invoke-virtual {v5}, Lio/dcloud/uts/UTSSourceMapPosition;->getColumn()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    check-cast v2, Ljava/lang/Number;

    const-string v6, "column"

    invoke-virtual {v1, v6, v2}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 622
    invoke-virtual {v5}, Lio/dcloud/uts/UTSSourceMapPosition;->getLine()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    check-cast v2, Ljava/lang/Number;

    const-string v5, "line"

    invoke-virtual {v1, v5, v2}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 624
    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v6, p2

    invoke-virtual {v6, v3, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    move-object/from16 p2, v0

    :goto_7
    move-object/from16 v20, v12

    move-object/from16 v12, v16

    move-object/from16 v5, v17

    move-object/from16 v1, v23

    goto :goto_6

    :cond_1a
    move-object/from16 v6, p2

    goto :goto_8

    :cond_1b
    move-object/from16 v6, p2

    move-object/from16 v3, v27

    .line 629
    :goto_8
    invoke-interface {v1}, Lkotlin/reflect/KProperty1;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v15, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1c

    .line 631
    sget-object v1, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-direct {v1, v15, v14}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v1

    move-object/from16 p2, v0

    const/4 v14, 0x2

    goto :goto_9

    .line 633
    :cond_1c
    sget-object v1, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    move-object/from16 p2, v0

    const/4 v0, 0x0

    const/4 v14, 0x2

    invoke-static {v1, v5, v4, v14, v0}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    .line 637
    :goto_9
    invoke-static {v2, v7}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1d

    if-eqz v5, :cond_1d

    .line 639
    invoke-virtual {v5}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v0

    invoke-interface {v0}, Lkotlin/reflect/KClass;->isCompanion()Z

    goto :goto_7

    .line 645
    :cond_1d
    new-instance v0, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v0}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 646
    new-instance v14, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v14, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v14, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v12, v14}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 647
    sget-object v14, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-direct {v14, v1, v5}, Lio/dcloud/uts/android/ClassLogWrapper;->needSubType(Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v20

    if-eqz v20, :cond_1e

    .line 648
    invoke-direct {v14, v5}, Lio/dcloud/uts/android/ClassLogWrapper;->logSubTypeOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_1e

    move-object/from16 v20, v12

    .line 650
    new-instance v12, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v12, v4}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v12, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v11, v12}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_a

    :cond_1e
    move-object/from16 v20, v12

    .line 653
    :goto_a
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v4, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v13, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 654
    instance-of v2, v5, Lkotlin/Function;

    if-eqz v2, :cond_23

    .line 658
    check-cast v5, Lkotlin/Function;

    invoke-static {v5}, Lkotlin/reflect/jvm/ReflectLambdaKt;->reflect(Lkotlin/Function;)Lkotlin/reflect/KFunction;

    move-result-object v1

    if-eqz v1, :cond_21

    .line 660
    new-instance v2, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v2}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 661
    invoke-interface {v1}, Lkotlin/reflect/KFunction;->getParameters()Ljava/util/List;

    move-result-object v4

    check-cast v4, Ljava/lang/Iterable;

    .line 1166
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_b
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1f

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lkotlin/reflect/KParameter;

    .line 662
    new-instance v12, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v14, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v5}, Lkotlin/reflect/KParameter;->getType()Lkotlin/reflect/KType;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    move-object/from16 v25, v4

    const/4 v4, 0x1

    invoke-direct {v14, v5, v4}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v5

    invoke-direct {v12, v5}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v12, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v2, v12}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    move-object/from16 v4, v25

    goto :goto_b

    .line 664
    :cond_1f
    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v4, v28

    invoke-virtual {v0, v4, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 666
    invoke-interface {v1}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    move-object/from16 v12, v30

    invoke-static {v12, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_20

    .line 667
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v5, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v1}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v14, 0x1

    invoke-direct {v5, v1, v14}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v1, v31

    invoke-virtual {v0, v1, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    :cond_20
    move-object/from16 v28, v4

    move-object/from16 v30, v12

    :cond_21
    move-object/from16 v12, v16

    :cond_22
    move-object/from16 v1, v23

    move-object/from16 v4, v29

    goto/16 :goto_d

    :cond_23
    move-object/from16 v4, v28

    move-object/from16 v12, v30

    .line 672
    invoke-virtual {v14, v5, v8, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v2

    move-object/from16 v12, v16

    .line 674
    invoke-static {v12, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v16

    if-eqz v16, :cond_24

    instance-of v4, v2, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v4, :cond_24

    .line 675
    check-cast v2, Lio/dcloud/uts/gson/JsonObject;

    invoke-virtual {v2}, Lio/dcloud/uts/gson/JsonObject;->entrySet()Ljava/util/Set;

    move-result-object v1

    const-string v2, "wrapObjectRet.entrySet()"

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v1, Ljava/lang/Iterable;

    .line 1168
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_c
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_22

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 676
    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v4, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_c

    .line 678
    :cond_24
    invoke-static {v12, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_25

    const-string v1, "\"[Circular]\""

    invoke-virtual {v2}, Lio/dcloud/uts/gson/JsonElement;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v1, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_25

    move-object/from16 v1, v23

    .line 679
    invoke-virtual {v0, v1, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 680
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-static {v5}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {v14, v5}, Lio/dcloud/uts/android/ClassLogWrapper;->logClassOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v2, v4}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v4, v29

    invoke-virtual {v0, v4, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_d

    :cond_25
    move-object/from16 v1, v23

    move-object/from16 v4, v29

    .line 683
    invoke-virtual {v0, v1, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 687
    :goto_d
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v5, v17

    invoke-virtual {v5, v0}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    :goto_e
    move-object/from16 v0, p2

    move-object/from16 v23, v1

    move-object/from16 v27, v3

    move-object/from16 v29, v4

    move-object/from16 v17, v5

    move-object/from16 p2, v6

    move-object/from16 v16, v12

    move-object/from16 v12, v20

    const/4 v14, 0x1

    goto/16 :goto_4

    :cond_26
    move-object/from16 v6, p2

    move-object/from16 v5, v17

    move-object/from16 v1, v23

    move-object/from16 v3, v27

    if-eqz v19, :cond_28

    .line 693
    invoke-interface/range {v18 .. v18}, Lkotlin/reflect/KClass;->getSupertypes()Ljava/util/List;

    move-result-object v0

    check-cast v0, Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_28

    .line 695
    new-instance v0, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-interface/range {v18 .. v18}, Lkotlin/reflect/KClass;->getSupertypes()Ljava/util/List;

    move-result-object v2

    const/4 v4, 0x0

    invoke-interface {v2, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    .line 697
    invoke-virtual {v6, v3}, Lio/dcloud/uts/gson/JsonObject;->has(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_28

    .line 699
    new-instance v2, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v2}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 700
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v2, v13, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 701
    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v6, v3, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_10

    :cond_27
    move-object v6, v2

    move-object v5, v3

    move-object v3, v1

    :goto_f
    move-object/from16 v1, v23

    .line 709
    :cond_28
    :goto_10
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v14, v22

    move-object/from16 v2, v24

    invoke-virtual {v14, v2, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 711
    invoke-static/range {v18 .. v18}, Lkotlin/reflect/full/KClasses;->getDeclaredMemberFunctions(Lkotlin/reflect/KClass;)Ljava/util/Collection;

    move-result-object v0

    check-cast v0, Ljava/lang/Iterable;

    .line 1171
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_29
    :goto_11
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2a

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lkotlin/reflect/KFunction;

    .line 712
    invoke-interface {v2}, Lkotlin/reflect/KFunction;->getName()Ljava/lang/String;

    move-result-object v4

    .line 716
    :try_start_2
    move-object v5, v2

    check-cast v5, Lkotlin/reflect/KCallable;

    const/4 v7, 0x1

    invoke-static {v5, v7}, Lkotlin/reflect/jvm/KCallablesJvm;->setAccessible(Lkotlin/reflect/KCallable;Z)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    .line 725
    instance-of v5, v8, Lio/dcloud/uts/IUTSSourceMap;

    if-eqz v5, :cond_29

    const-string v5, "__$getOriginalPosition"

    invoke-static {v5, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_29

    .line 727
    new-array v4, v7, [Ljava/lang/Object;

    const/4 v5, 0x0

    aput-object v8, v4, v5

    invoke-interface {v2, v4}, Lkotlin/reflect/KFunction;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 728
    instance-of v4, v2, Lio/dcloud/uts/UTSSourceMapPosition;

    if-eqz v4, :cond_29

    .line 729
    new-instance v4, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v4}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 730
    check-cast v2, Lio/dcloud/uts/UTSSourceMapPosition;

    invoke-virtual {v2}, Lio/dcloud/uts/UTSSourceMapPosition;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v13, v5}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 731
    const-string v5, "file"

    invoke-virtual {v2}, Lio/dcloud/uts/UTSSourceMapPosition;->getFile()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v4, v5, v7}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 732
    invoke-virtual {v2}, Lio/dcloud/uts/UTSSourceMapPosition;->getColumn()I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    check-cast v5, Ljava/lang/Number;

    const-string v7, "column"

    invoke-virtual {v4, v7, v5}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 733
    invoke-virtual {v2}, Lio/dcloud/uts/UTSSourceMapPosition;->getLine()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    check-cast v2, Ljava/lang/Number;

    const-string v5, "line"

    invoke-virtual {v4, v5, v2}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 735
    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v6, v3, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_11

    :catchall_2
    nop

    goto :goto_11

    .line 760
    :cond_2a
    const-string v0, "methods"

    move-object/from16 v2, v21

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v0, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 761
    move-object v15, v14

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v6, v1, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    :goto_12
    move-object v0, v6

    goto/16 :goto_29

    :cond_2b
    move-object/from16 v29, v4

    move-object/from16 v30, v5

    move-object/from16 v28, v20

    move-object/from16 v1, v23

    move-object/from16 v3, v24

    move-object/from16 v5, p2

    move-object/from16 v20, v12

    move-object v12, v14

    move-object/from16 v14, v22

    move-object/from16 v32, v6

    move-object v6, v0

    move-object/from16 v0, v32

    .line 762
    const-string v4, "array"

    invoke-static {v4, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4a

    .line 763
    instance-of v0, v8, [Ljava/lang/Object;

    if-eqz v0, :cond_2e

    .line 765
    move-object v0, v8

    check-cast v0, [Ljava/lang/Object;

    .line 1173
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_13
    if-ge v4, v2, :cond_2d

    aget-object v11, v0, v4

    .line 766
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    const/4 v15, 0x0

    invoke-virtual {v12, v11, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 767
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_2c

    .line 768
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 770
    :cond_2c
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_13

    .line 773
    :cond_2d
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 775
    :cond_2e
    instance-of v0, v8, [B

    if-eqz v0, :cond_31

    .line 778
    move-object v0, v8

    check-cast v0, [B

    .line 1175
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_14
    if-ge v4, v2, :cond_30

    aget-byte v11, v0, v4

    .line 779
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v11}, Ljava/lang/Byte;->valueOf(B)Ljava/lang/Byte;

    move-result-object v11

    const/4 v15, 0x0

    invoke-virtual {v12, v11, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 780
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_2f

    .line 781
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 783
    :cond_2f
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_14

    .line 786
    :cond_30
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 788
    :cond_31
    instance-of v0, v8, [C

    if-eqz v0, :cond_34

    .line 791
    move-object v0, v8

    check-cast v0, [C

    .line 1177
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_15
    if-ge v4, v2, :cond_33

    aget-char v11, v0, v4

    .line 792
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v11}, Ljava/lang/Character;->valueOf(C)Ljava/lang/Character;

    move-result-object v11

    const/4 v15, 0x0

    invoke-virtual {v12, v11, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 793
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_32

    .line 794
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 796
    :cond_32
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_15

    .line 799
    :cond_33
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 801
    :cond_34
    instance-of v0, v8, [S

    if-eqz v0, :cond_37

    .line 804
    move-object v0, v8

    check-cast v0, [S

    .line 1179
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_16
    if-ge v4, v2, :cond_36

    aget-short v11, v0, v4

    .line 805
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v11}, Ljava/lang/Short;->valueOf(S)Ljava/lang/Short;

    move-result-object v11

    const/4 v15, 0x0

    invoke-virtual {v12, v11, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 806
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_35

    .line 807
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 809
    :cond_35
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_16

    .line 812
    :cond_36
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 814
    :cond_37
    instance-of v0, v8, [I

    if-eqz v0, :cond_3a

    .line 817
    move-object v0, v8

    check-cast v0, [I

    .line 1181
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_17
    if-ge v4, v2, :cond_39

    aget v11, v0, v4

    .line 818
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    const/4 v15, 0x0

    invoke-virtual {v12, v11, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 819
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_38

    .line 820
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 822
    :cond_38
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_17

    .line 825
    :cond_39
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 827
    :cond_3a
    instance-of v0, v8, [J

    if-eqz v0, :cond_3d

    .line 830
    move-object v0, v8

    check-cast v0, [J

    .line 1183
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_18
    if-ge v4, v2, :cond_3c

    aget-wide v11, v0, v4

    .line 831
    sget-object v15, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v11, v12}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v11

    const/4 v12, 0x0

    invoke-virtual {v15, v11, v12, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 832
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_3b

    .line 833
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 835
    :cond_3b
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_18

    .line 838
    :cond_3c
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 840
    :cond_3d
    instance-of v0, v8, [F

    if-eqz v0, :cond_40

    .line 843
    move-object v0, v8

    check-cast v0, [F

    .line 1185
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_19
    if-ge v4, v2, :cond_3f

    aget v11, v0, v4

    .line 844
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v11}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v11

    const/4 v15, 0x0

    invoke-virtual {v12, v11, v15, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 845
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_3e

    .line 846
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 848
    :cond_3e
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_19

    .line 851
    :cond_3f
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 854
    :cond_40
    instance-of v0, v8, [D

    if-eqz v0, :cond_43

    .line 857
    move-object v0, v8

    check-cast v0, [D

    .line 1187
    array-length v2, v0

    const/4 v4, 0x0

    const/4 v7, 0x0

    :goto_1a
    if-ge v4, v2, :cond_42

    aget-wide v11, v0, v4

    .line 858
    sget-object v15, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v11, v12}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v11

    const/4 v12, 0x0

    invoke-virtual {v15, v11, v12, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v11

    .line 859
    instance-of v12, v11, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v12, :cond_41

    .line 860
    move-object v12, v11

    check-cast v12, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    check-cast v15, Ljava/lang/Number;

    invoke-virtual {v12, v13, v15}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 862
    :cond_41
    invoke-virtual {v5, v11}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v7, v7, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_1a

    .line 865
    :cond_42
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 868
    :cond_43
    instance-of v0, v8, [Z

    if-eqz v0, :cond_46

    .line 871
    move-object v0, v8

    check-cast v0, [Z

    .line 1189
    array-length v2, v0

    const/4 v4, 0x0

    const/16 v26, 0x0

    :goto_1b
    if-ge v4, v2, :cond_45

    aget-boolean v7, v0, v4

    .line 872
    sget-object v8, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v7

    const/4 v11, 0x0

    invoke-virtual {v8, v7, v11, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v7

    .line 873
    instance-of v8, v7, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v8, :cond_44

    .line 874
    move-object v8, v7

    check-cast v8, Lio/dcloud/uts/gson/JsonObject;

    invoke-static/range {v26 .. v26}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    check-cast v11, Ljava/lang/Number;

    invoke-virtual {v8, v13, v11}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 876
    :cond_44
    invoke-virtual {v5, v7}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v26, v26, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_1b

    .line 879
    :cond_45
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_1d

    .line 881
    :cond_46
    instance-of v0, v8, Ljava/util/List;

    if-eqz v0, :cond_49

    .line 883
    move-object v0, v8

    check-cast v0, Ljava/lang/Iterable;

    .line 1191
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_1c
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_48

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    .line 884
    sget-object v7, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    const/4 v8, 0x0

    invoke-virtual {v7, v4, v8, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v4

    .line 885
    instance-of v7, v4, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v7, :cond_47

    .line 886
    move-object v7, v4

    check-cast v7, Lio/dcloud/uts/gson/JsonObject;

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    check-cast v8, Ljava/lang/Number;

    invoke-virtual {v7, v13, v8}, Lio/dcloud/uts/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 888
    :cond_47
    invoke-virtual {v5, v4}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_1c

    .line 891
    :cond_48
    move-object v0, v5

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 893
    :cond_49
    :goto_1d
    move-object v15, v14

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v6, v1, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto/16 :goto_12

    .line 895
    :cond_4a
    const-string v4, "error"

    invoke-static {v4, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_5a

    .line 897
    instance-of v2, v8, Ljava/lang/Throwable;

    if-eqz v2, :cond_57

    .line 901
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-static {v4}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v4

    invoke-static {v4}, Lkotlin/reflect/full/KClasses;->getMemberProperties(Lkotlin/reflect/KClass;)Ljava/util/Collection;

    move-result-object v4

    check-cast v4, Ljava/lang/Iterable;

    .line 1193
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_1e
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v16

    if-eqz v16, :cond_57

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v16

    move-object/from16 p2, v4

    move-object/from16 v4, v16

    check-cast v4, Lkotlin/reflect/KProperty1;

    move-object/from16 v16, v6

    .line 903
    invoke-interface {v4}, Lkotlin/reflect/KProperty1;->getName()Ljava/lang/String;

    move-result-object v6

    move-object/from16 v24, v3

    .line 908
    :try_start_3
    move-object v3, v4

    check-cast v3, Lkotlin/reflect/KCallable;
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_3

    move-object/from16 v22, v14

    const/4 v14, 0x1

    :try_start_4
    invoke-static {v3, v14}, Lkotlin/reflect/jvm/KCallablesJvm;->setAccessible(Lkotlin/reflect/KCallable;Z)V

    .line 909
    new-array v3, v14, [Ljava/lang/Object;

    const/4 v14, 0x0

    aput-object v8, v3, v14

    invoke-interface {v4, v3}, Lkotlin/reflect/KProperty1;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_4

    const/4 v14, 0x0

    goto :goto_1f

    :catchall_3
    move-object/from16 v22, v14

    :catchall_4
    nop

    const/4 v3, 0x0

    const/4 v14, 0x1

    :goto_1f
    if-eqz v14, :cond_4b

    move-object/from16 v31, v0

    move/from16 v18, v2

    move-object/from16 v19, v7

    move-object/from16 v17, v15

    :goto_20
    move-object/from16 v6, v28

    move-object/from16 v3, v29

    move-object/from16 v7, v30

    goto/16 :goto_27

    .line 918
    :cond_4b
    invoke-interface {v4}, Lkotlin/reflect/KProperty1;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v15, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4c

    .line 920
    sget-object v4, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    const/4 v14, 0x1

    invoke-direct {v4, v15, v14}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v4

    move/from16 v18, v2

    move-object/from16 v17, v15

    const/4 v2, 0x0

    const/4 v14, 0x2

    const/4 v15, 0x0

    goto :goto_21

    .line 922
    :cond_4c
    sget-object v4, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    move/from16 v18, v2

    move-object/from16 v17, v15

    const/4 v2, 0x0

    const/4 v14, 0x2

    const/4 v15, 0x0

    invoke-static {v4, v3, v2, v14, v15}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    .line 926
    :goto_21
    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v19

    if-eqz v19, :cond_4d

    if-eqz v3, :cond_4d

    .line 928
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    invoke-static {v3}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v3

    invoke-interface {v3}, Lkotlin/reflect/KClass;->isCompanion()Z

    move-object/from16 v31, v0

    move-object/from16 v19, v7

    goto :goto_20

    .line 933
    :cond_4d
    new-instance v2, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v2}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 934
    new-instance v14, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v14, v4}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v14, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v15, v20

    invoke-virtual {v2, v15, v14}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 935
    sget-object v14, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-direct {v14, v4, v3}, Lio/dcloud/uts/android/ClassLogWrapper;->needSubType(Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v19

    if-eqz v19, :cond_4e

    move-object/from16 v19, v7

    .line 936
    invoke-direct {v14, v3}, Lio/dcloud/uts/android/ClassLogWrapper;->logSubTypeOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v7

    move-object/from16 v20, v15

    if-eqz v7, :cond_4f

    .line 938
    new-instance v15, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v15, v7}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v2, v11, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_22

    :cond_4e
    move-object/from16 v19, v7

    move-object/from16 v20, v15

    .line 941
    :cond_4f
    :goto_22
    new-instance v7, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v7, v6}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v7, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v2, v13, v7}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 942
    instance-of v6, v3, Lkotlin/Function;

    if-eqz v6, :cond_54

    .line 946
    check-cast v3, Lkotlin/Function;

    invoke-static {v3}, Lkotlin/reflect/jvm/ReflectLambdaKt;->reflect(Lkotlin/Function;)Lkotlin/reflect/KFunction;

    move-result-object v3

    if-eqz v3, :cond_51

    .line 948
    new-instance v4, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v4}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 949
    invoke-interface {v3}, Lkotlin/reflect/KFunction;->getParameters()Ljava/util/List;

    move-result-object v6

    check-cast v6, Ljava/lang/Iterable;

    .line 1194
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_23
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_50

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lkotlin/reflect/KParameter;

    .line 950
    new-instance v14, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v15, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v7}, Lkotlin/reflect/KParameter;->getType()Lkotlin/reflect/KType;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v7

    move-object/from16 v21, v6

    const/4 v6, 0x1

    invoke-direct {v15, v7, v6}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v7

    invoke-direct {v14, v7}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v14, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v4, v14}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    move-object/from16 v6, v21

    goto :goto_23

    .line 952
    :cond_50
    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v6, v28

    invoke-virtual {v2, v6, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 954
    invoke-interface {v3}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    move-object/from16 v7, v30

    invoke-static {v7, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_52

    .line 955
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v14, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v3}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    const/4 v15, 0x1

    invoke-direct {v14, v3, v15}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v3

    invoke-direct {v4, v3}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v2, v0, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_24

    :cond_51
    move-object/from16 v6, v28

    move-object/from16 v7, v30

    :cond_52
    :goto_24
    move-object/from16 v31, v0

    :cond_53
    move-object/from16 v3, v29

    goto/16 :goto_26

    :cond_54
    move-object/from16 v6, v28

    move-object/from16 v7, v30

    .line 960
    invoke-virtual {v14, v3, v8, v9, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v15

    .line 962
    invoke-static {v12, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v21

    move-object/from16 v31, v0

    if-eqz v21, :cond_55

    instance-of v0, v15, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v0, :cond_55

    .line 963
    check-cast v15, Lio/dcloud/uts/gson/JsonObject;

    invoke-virtual {v15}, Lio/dcloud/uts/gson/JsonObject;->entrySet()Ljava/util/Set;

    move-result-object v0

    const-string v3, "wrapObjectRet.entrySet()"

    invoke-static {v0, v3}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Ljava/lang/Iterable;

    .line 1196
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_25
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_53

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 964
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v2, v4, v3}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_25

    .line 966
    :cond_55
    invoke-static {v12, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_56

    const-string v0, "\"[Circular]\""

    invoke-virtual {v15}, Lio/dcloud/uts/gson/JsonElement;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v0, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_56

    .line 967
    invoke-virtual {v2, v1, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 968
    new-instance v0, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-static {v3}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {v14, v3}, Lio/dcloud/uts/android/ClassLogWrapper;->logClassOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-direct {v0, v3}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v3, v29

    invoke-virtual {v2, v3, v0}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_26

    :cond_56
    move-object/from16 v3, v29

    .line 971
    invoke-virtual {v2, v1, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 975
    :goto_26
    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v5, v2}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    :goto_27
    move-object/from16 v4, p2

    move-object/from16 v29, v3

    move-object/from16 v28, v6

    move-object/from16 v30, v7

    move-object/from16 v6, v16

    move-object/from16 v15, v17

    move/from16 v2, v18

    move-object/from16 v7, v19

    move-object/from16 v14, v22

    move-object/from16 v3, v24

    move-object/from16 v0, v31

    goto/16 :goto_1e

    :cond_57
    move/from16 v18, v2

    move-object/from16 v24, v3

    move-object/from16 v16, v6

    move-object/from16 v22, v14

    .line 987
    instance-of v0, v8, Ljava/lang/reflect/InvocationTargetException;

    if-eqz v0, :cond_58

    .line 988
    move-object v0, v8

    check-cast v0, Ljava/lang/reflect/InvocationTargetException;

    invoke-virtual {v0}, Ljava/lang/reflect/InvocationTargetException;->getTargetException()Ljava/lang/Throwable;

    move-result-object v0

    .line 989
    const-string v2, "targetEx"

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {v0}, Lkotlin/ExceptionsKt;->stackTraceToString(Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object v0

    goto :goto_28

    :cond_58
    if-eqz v18, :cond_59

    .line 991
    move-object v0, v8

    check-cast v0, Ljava/lang/Throwable;

    invoke-static {v0}, Lkotlin/ExceptionsKt;->stackTraceToString(Ljava/lang/Throwable;)Ljava/lang/String;

    move-result-object v0

    goto :goto_28

    .line 990
    :cond_59
    const-string v0, ""

    .line 994
    :goto_28
    move-object v2, v5

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v3, v22

    move-object/from16 v4, v24

    invoke-virtual {v3, v4, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 995
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v2, v0}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    const-string v0, "stack"

    invoke-virtual {v3, v0, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 996
    move-object v15, v3

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v0, v16

    invoke-virtual {v0, v1, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_29

    :cond_5a
    move-object v0, v6

    .line 1002
    instance-of v2, v8, Lio/dcloud/uts/UTSRegExp;

    if-eqz v2, :cond_5b

    .line 1003
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    .line 1004
    new-instance v3, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v3, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v3, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v1, v3}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_29

    .line 1006
    :cond_5b
    instance-of v2, v8, Lio/dcloud/uts/Date;

    if-eqz v2, :cond_5c

    .line 1007
    new-instance v2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v2, v3}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v1, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_29

    .line 1009
    :cond_5c
    instance-of v2, v8, Ljava/util/Date;

    if-eqz v2, :cond_5d

    .line 1010
    new-instance v2, Lio/dcloud/uts/Date;

    move-object v3, v8

    check-cast v3, Ljava/util/Date;

    invoke-virtual {v3}, Ljava/util/Date;->getTime()J

    move-result-wide v3

    invoke-static {v3, v4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v3

    check-cast v3, Ljava/lang/Number;

    invoke-direct {v2, v3}, Lio/dcloud/uts/Date;-><init>(Ljava/lang/Number;)V

    .line 1011
    new-instance v3, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual {v2}, Lio/dcloud/uts/Date;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v3, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v3, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v1, v3}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 1022
    :cond_5d
    :goto_29
    check-cast v0, Lio/dcloud/uts/gson/JsonElement;

    return-object v0
.end method

.method private final fillRootJsonWithMap(Ljava/util/Map;Ljava/util/HashSet;Lio/dcloud/uts/gson/JsonObject;Ljava/util/concurrent/atomic/AtomicInteger;Lio/dcloud/uts/gson/JsonArray;Ljava/lang/Object;)V
    .locals 15
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;",
            "Ljava/util/HashSet<",
            "Ljava/lang/Object;",
            ">;",
            "Lio/dcloud/uts/gson/JsonObject;",
            "Ljava/util/concurrent/atomic/AtomicInteger;",
            "Lio/dcloud/uts/gson/JsonArray;",
            "Ljava/lang/Object;",
            ")V"
        }
    .end annotation

    move-object/from16 v0, p3

    move-object/from16 v1, p6

    .line 1147
    invoke-interface/range {p1 .. p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    const-string v4, "name"

    const-string v5, "__$originalPosition"

    if-eqz v3, :cond_9

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 184
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    .line 185
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    .line 186
    new-instance v7, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v7}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 188
    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_0

    instance-of v8, v3, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v8, :cond_0

    .line 190
    check-cast v3, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v5, v3}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    move-object/from16 v6, p2

    move-object/from16 v8, p4

    move-object/from16 v3, p5

    goto :goto_0

    .line 196
    :cond_0
    instance-of v5, v3, Lio/dcloud/uts/android/UTSLogInfo;

    const/4 v8, 0x1

    const/4 v9, 0x0

    const/4 v10, 0x2

    const/4 v11, 0x0

    if-eqz v5, :cond_1

    .line 198
    sget-object v5, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    check-cast v3, Lio/dcloud/uts/android/UTSLogInfo;

    invoke-virtual {v3}, Lio/dcloud/uts/android/UTSLogInfo;->getType()Ljava/lang/String;

    move-result-object v12

    invoke-direct {v5, v12, v8}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v5

    .line 199
    invoke-virtual {v3}, Lio/dcloud/uts/android/UTSLogInfo;->getValue()Ljava/lang/Object;

    move-result-object v3

    goto :goto_1

    .line 202
    :cond_1
    sget-object v5, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-static {v5, v3, v11, v10, v9}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v5

    .line 205
    :goto_1
    new-instance v12, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v12, v5}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v12, Lio/dcloud/uts/gson/JsonElement;

    const-string v13, "type"

    invoke-virtual {v7, v13, v12}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 206
    sget-object v12, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-direct {v12, v5, v3}, Lio/dcloud/uts/android/ClassLogWrapper;->needSubType(Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v13

    if-eqz v13, :cond_2

    .line 207
    invoke-direct {v12, v3}, Lio/dcloud/uts/android/ClassLogWrapper;->logSubTypeOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v13

    if-eqz v13, :cond_2

    .line 209
    new-instance v14, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v14, v13}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v14, Lio/dcloud/uts/gson/JsonElement;

    const-string v13, "subType"

    invoke-virtual {v7, v13, v14}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 212
    :cond_2
    new-instance v13, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v13, v6}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v13, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v7, v4, v13}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 213
    instance-of v4, v3, Lkotlin/Function;

    if-eqz v4, :cond_5

    .line 217
    check-cast v3, Lkotlin/Function;

    invoke-static {v3}, Lkotlin/reflect/jvm/ReflectLambdaKt;->reflect(Lkotlin/Function;)Lkotlin/reflect/KFunction;

    move-result-object v3

    if-eqz v3, :cond_4

    .line 219
    new-instance v4, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v4}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 220
    invoke-interface {v3}, Lkotlin/reflect/KFunction;->getParameters()Ljava/util/List;

    move-result-object v5

    check-cast v5, Ljava/lang/Iterable;

    .line 1148
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :goto_2
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_3

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lkotlin/reflect/KParameter;

    .line 221
    new-instance v12, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v13, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v6}, Lkotlin/reflect/KParameter;->getType()Lkotlin/reflect/KType;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v13, v6, v8}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v6

    invoke-direct {v12, v6}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v12, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v4, v12}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_2

    .line 223
    :cond_3
    const-string v5, "parameter"

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v7, v5, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 225
    invoke-interface {v3}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    const-string v5, "kotlin.Unit"

    invoke-static {v5, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_4

    .line 226
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v5, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v3}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v5, v3, v11, v10, v9}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-direct {v4, v3}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    const-string v3, "returned"

    invoke-virtual {v7, v3, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    :cond_4
    move-object/from16 v6, p2

    move-object/from16 v8, p4

    goto :goto_4

    :cond_5
    move-object/from16 v6, p2

    move-object/from16 v8, p4

    .line 230
    invoke-virtual {v12, v3, v1, v6, v8}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v4

    .line 231
    const-string v9, "object"

    invoke-static {v9, v5}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v10

    if-eqz v10, :cond_6

    instance-of v10, v4, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v10, :cond_6

    .line 232
    check-cast v4, Lio/dcloud/uts/gson/JsonObject;

    invoke-virtual {v4}, Lio/dcloud/uts/gson/JsonObject;->entrySet()Ljava/util/Set;

    move-result-object v3

    const-string v4, "wrapObjectRet.entrySet()"

    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v3, Ljava/lang/Iterable;

    .line 1150
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_3
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_8

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map$Entry;

    .line 233
    invoke-interface {v4}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    invoke-interface {v4}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v7, v5, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_3

    .line 235
    :cond_6
    invoke-static {v9, v5}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    const-string v9, "value"

    if-eqz v5, :cond_7

    const-string v5, "\"[Circular]\""

    invoke-virtual {v4}, Lio/dcloud/uts/gson/JsonElement;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-static {v5, v10}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_7

    .line 236
    invoke-virtual {v7, v9, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 237
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-static {v3}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {v12, v3}, Lio/dcloud/uts/android/ClassLogWrapper;->logClassOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-direct {v4, v3}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    const-string v3, "className"

    invoke-virtual {v7, v3, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_4

    .line 240
    :cond_7
    invoke-virtual {v7, v9, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 244
    :cond_8
    :goto_4
    check-cast v7, Lio/dcloud/uts/gson/JsonElement;

    move-object/from16 v3, p5

    invoke-virtual {v3, v7}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    goto/16 :goto_0

    .line 251
    :cond_9
    instance-of v1, v1, Lio/dcloud/uts/UTSJSONObject;

    if-eqz v1, :cond_c

    .line 252
    invoke-virtual {v0, v5}, Lio/dcloud/uts/gson/JsonObject;->has(Ljava/lang/String;)Z

    move-result v1

    const-string v2, "UTSJSONObject"

    if-nez v1, :cond_a

    .line 254
    new-instance v1, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v1}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 255
    new-instance v3, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v3, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v3, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v1, v4, v3}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 256
    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v0, v5, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_5

    .line 258
    :cond_a
    invoke-virtual {v0, v5}, Lio/dcloud/uts/gson/JsonObject;->get(Ljava/lang/String;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v1

    .line 259
    instance-of v3, v1, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v3, :cond_b

    .line 260
    move-object v3, v1

    check-cast v3, Lio/dcloud/uts/gson/JsonObject;

    new-instance v6, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v6, v2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v6, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v3, v4, v6}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 262
    :cond_b
    invoke-virtual {v0, v5, v1}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    :cond_c
    :goto_5
    return-void
.end method

.method private final logClassOf(Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 1029
    instance-of v0, p1, Ljava/lang/Class;

    if-eqz v0, :cond_0

    .line 1030
    check-cast p1, Ljava/lang/Class;

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 1031
    :cond_0
    instance-of v0, p1, Lkotlin/reflect/KClass;

    if-eqz v0, :cond_1

    .line 1032
    check-cast p1, Lkotlin/reflect/KClass;

    invoke-static {p1}, Lkotlin/jvm/JvmClassMappingKt;->getJavaClass(Lkotlin/reflect/KClass;)Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 1038
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v0

    invoke-interface {v0}, Lkotlin/reflect/KClass;->getQualifiedName()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_2

    return-object v0

    .line 1042
    :cond_2
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private final logSubTypeOf(Ljava/lang/Object;)Ljava/lang/String;
    .locals 2

    .line 1068
    instance-of v0, p1, Lio/dcloud/uts/UTSArray;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    instance-of v0, p1, Ljava/util/List;

    :goto_0
    if-eqz v0, :cond_1

    const/4 v0, 0x1

    goto :goto_1

    :cond_1
    instance-of v0, p1, [Ljava/lang/Object;

    :goto_1
    if-eqz v0, :cond_2

    const/4 v0, 0x1

    goto :goto_2

    :cond_2
    instance-of v0, p1, [B

    :goto_2
    if-eqz v0, :cond_3

    const/4 v0, 0x1

    goto :goto_3

    :cond_3
    instance-of v0, p1, [C

    :goto_3
    if-eqz v0, :cond_4

    const/4 v0, 0x1

    goto :goto_4

    :cond_4
    instance-of v0, p1, [S

    :goto_4
    if-eqz v0, :cond_5

    const/4 v0, 0x1

    goto :goto_5

    :cond_5
    instance-of v0, p1, [F

    :goto_5
    if-eqz v0, :cond_6

    const/4 v0, 0x1

    goto :goto_6

    :cond_6
    instance-of v0, p1, [D

    :goto_6
    if-eqz v0, :cond_7

    const/4 v0, 0x1

    goto :goto_7

    :cond_7
    instance-of v0, p1, [I

    :goto_7
    if-eqz v0, :cond_8

    const/4 v0, 0x1

    goto :goto_8

    :cond_8
    instance-of v0, p1, [Z

    :goto_8
    if-eqz v0, :cond_9

    const/4 v0, 0x1

    goto :goto_9

    :cond_9
    instance-of v0, p1, [J

    :goto_9
    if-eqz v0, :cond_a

    .line 1069
    const-string p1, "array"

    return-object p1

    .line 1072
    :cond_a
    instance-of v0, p1, Lio/dcloud/uts/UTSRegExp;

    if-eqz v0, :cond_b

    const/4 v0, 0x1

    goto :goto_a

    :cond_b
    instance-of v0, p1, Lkotlin/text/Regex;

    :goto_a
    if-eqz v0, :cond_c

    .line 1073
    const-string p1, "regexp"

    return-object p1

    .line 1076
    :cond_c
    instance-of v0, p1, Lio/dcloud/uts/Date;

    if-eqz v0, :cond_d

    const/4 v0, 0x1

    goto :goto_b

    :cond_d
    instance-of v0, p1, Ljava/util/Date;

    :goto_b
    if-eqz v0, :cond_e

    .line 1077
    const-string p1, "date"

    return-object p1

    .line 1080
    :cond_e
    instance-of v0, p1, Ljava/lang/Number;

    if-eqz v0, :cond_f

    .line 1081
    const-string p1, "number"

    return-object p1

    .line 1084
    :cond_f
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_10

    .line 1085
    const-string p1, "string"

    return-object p1

    .line 1088
    :cond_10
    instance-of v0, p1, Ljava/lang/Throwable;

    if-eqz v0, :cond_11

    .line 1089
    const-string p1, "error"

    return-object p1

    .line 1092
    :cond_11
    instance-of v0, p1, Ljava/util/Set;

    if-eqz v0, :cond_12

    .line 1093
    const-string p1, "set"

    return-object p1

    .line 1096
    :cond_12
    instance-of v0, p1, Ljava/util/Map;

    if-eqz v0, :cond_13

    .line 1097
    const-string p1, "map"

    return-object p1

    .line 1100
    :cond_13
    instance-of v0, p1, Lkotlin/Function;

    if-eqz v0, :cond_14

    .line 1101
    const-string p1, "function"

    return-object p1

    .line 1104
    :cond_14
    instance-of v0, p1, Ljava/lang/Class;

    if-eqz v0, :cond_15

    goto :goto_c

    :cond_15
    instance-of v1, p1, Lkotlin/reflect/KClass;

    :goto_c
    if-eqz v1, :cond_16

    .line 1105
    const-string p1, "class"

    return-object p1

    .line 1112
    :cond_16
    const-string p1, "object"

    return-object p1
.end method

.method private final logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;
    .locals 0

    if-nez p1, :cond_0

    .line 1126
    const-string p1, "null"

    return-object p1

    :cond_0
    if-eqz p2, :cond_3

    .line 1129
    instance-of p2, p1, Ljava/lang/String;

    if-eqz p2, :cond_3

    .line 1131
    const-string p2, "kotlin.Number"

    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_1

    .line 1132
    const-string p1, "number"

    return-object p1

    .line 1134
    :cond_1
    const-string p2, "kotlin.String"

    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_2

    .line 1135
    const-string p1, "string"

    return-object p1

    .line 1137
    :cond_2
    check-cast p1, Ljava/lang/String;

    return-object p1

    .line 1140
    :cond_3
    sget-object p2, Lio/dcloud/uts/UTSAndroid;->INSTANCE:Lio/dcloud/uts/UTSAndroid;

    invoke-virtual {p2, p1}, Lio/dcloud/uts/UTSAndroid;->typeof(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method static synthetic logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1123
    :cond_0
    invoke-direct {p0, p1, p2}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private final needSubType(Ljava/lang/String;Ljava/lang/Object;)Z
    .locals 1

    .line 1052
    const-string v0, "object"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    const/4 v0, 0x1

    if-eqz p1, :cond_0

    return v0

    .line 1056
    :cond_0
    instance-of p1, p2, Ljava/lang/Number;

    if-eqz p1, :cond_1

    return v0

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public static synthetic wrapClass$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;ILjava/lang/Object;)Lio/dcloud/uts/gson/JsonElement;
    .locals 0

    and-int/lit8 p5, p5, 0x8

    if-eqz p5, :cond_0

    .line 40
    new-instance p4, Ljava/util/concurrent/atomic/AtomicInteger;

    const/16 p5, 0x100

    invoke-direct {p4, p5}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>(I)V

    :cond_0
    invoke-virtual {p0, p1, p2, p3, p4}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object p0

    return-object p0
.end method

.method private final wrapClassInfo(Ljava/lang/Class;Ljava/util/HashSet;Lio/dcloud/uts/gson/JsonObject;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonObject;
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/util/HashSet<",
            "Ljava/lang/Object;",
            ">;",
            "Lio/dcloud/uts/gson/JsonObject;",
            "Ljava/util/concurrent/atomic/AtomicInteger;",
            ")",
            "Lio/dcloud/uts/gson/JsonObject;"
        }
    .end annotation

    move-object/from16 v0, p1

    move-object/from16 v1, p3

    .line 274
    new-instance v2, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v2}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 275
    new-instance v3, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v3}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 277
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Class;->getDeclaredFields()[Ljava/lang/reflect/Field;

    move-result-object v4

    const-string v5, "classObj.declaredFields"

    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v4, [Ljava/lang/Object;

    .line 1153
    array-length v5, v4

    const/4 v6, 0x0

    const/4 v7, 0x0

    :goto_0
    const-string v8, "name"

    const-string v9, "value"

    if-ge v7, v5, :cond_a

    aget-object v10, v4, v7

    check-cast v10, Ljava/lang/reflect/Field;

    .line 278
    invoke-virtual {v10}, Ljava/lang/reflect/Field;->getModifiers()I

    move-result v11

    invoke-static {v11}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v11

    const/4 v12, 0x0

    if-eqz v11, :cond_0

    .line 281
    invoke-virtual {v10, v0}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    goto :goto_1

    :cond_0
    move-object v11, v12

    .line 287
    :goto_1
    sget-object v13, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    const/4 v14, 0x2

    invoke-static {v13, v11, v6, v14, v12}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf$default(Lio/dcloud/uts/android/ClassLogWrapper;Ljava/lang/Object;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v12

    .line 288
    invoke-virtual {v10}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v14

    const-string v15, "Companion"

    invoke-static {v14, v15}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v14

    if-eqz v14, :cond_1

    if-eqz v11, :cond_1

    .line 290
    invoke-virtual {v11}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v8

    invoke-static {v8}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v8

    invoke-interface {v8}, Lkotlin/reflect/KClass;->isCompanion()Z

    move-object/from16 v6, p2

    move-object/from16 v10, p4

    move-object/from16 v16, v4

    goto/16 :goto_5

    .line 295
    :cond_1
    new-instance v14, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v14}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 296
    new-instance v15, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v15, v12}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    const-string v6, "type"

    invoke-virtual {v14, v6, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 297
    invoke-direct {v13, v12, v11}, Lio/dcloud/uts/android/ClassLogWrapper;->needSubType(Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_2

    .line 298
    invoke-direct {v13, v11}, Lio/dcloud/uts/android/ClassLogWrapper;->logSubTypeOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    if-eqz v6, :cond_2

    .line 300
    new-instance v15, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v15, v6}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v15, Lio/dcloud/uts/gson/JsonElement;

    const-string v6, "subType"

    invoke-virtual {v14, v6, v15}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 303
    :cond_2
    new-instance v6, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-virtual {v10}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v10

    invoke-direct {v6, v10}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v6, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v8, v6}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 304
    instance-of v6, v11, Lkotlin/Function;

    if-eqz v6, :cond_5

    .line 308
    check-cast v11, Lkotlin/Function;

    invoke-static {v11}, Lkotlin/reflect/jvm/ReflectLambdaKt;->reflect(Lkotlin/Function;)Lkotlin/reflect/KFunction;

    move-result-object v6

    if-eqz v6, :cond_4

    .line 310
    new-instance v8, Lio/dcloud/uts/gson/JsonArray;

    invoke-direct {v8}, Lio/dcloud/uts/gson/JsonArray;-><init>()V

    .line 311
    invoke-interface {v6}, Lkotlin/reflect/KFunction;->getParameters()Ljava/util/List;

    move-result-object v9

    check-cast v9, Ljava/lang/Iterable;

    .line 1154
    invoke-interface {v9}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v9

    :goto_2
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    move-result v10

    const/4 v11, 0x1

    if-eqz v10, :cond_3

    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lkotlin/reflect/KParameter;

    .line 312
    new-instance v12, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v13, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v10}, Lkotlin/reflect/KParameter;->getType()Lkotlin/reflect/KType;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-direct {v13, v10, v11}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v10

    invoke-direct {v12, v10}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v12, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v8, v12}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_2

    .line 314
    :cond_3
    const-string v9, "parameter"

    check-cast v8, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v9, v8}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 316
    invoke-interface {v6}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v8

    const-string v9, "kotlin.Unit"

    invoke-static {v9, v8}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_4

    .line 317
    new-instance v8, Lio/dcloud/uts/gson/JsonPrimitive;

    sget-object v9, Lio/dcloud/uts/android/ClassLogWrapper;->INSTANCE:Lio/dcloud/uts/android/ClassLogWrapper;

    invoke-interface {v6}, Lkotlin/reflect/KFunction;->getReturnType()Lkotlin/reflect/KType;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v9, v6, v11}, Lio/dcloud/uts/android/ClassLogWrapper;->logTypeOf(Ljava/lang/Object;Z)Ljava/lang/String;

    move-result-object v6

    invoke-direct {v8, v6}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v8, Lio/dcloud/uts/gson/JsonElement;

    const-string v6, "returned"

    invoke-virtual {v14, v6, v8}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    :cond_4
    move-object/from16 v6, p2

    move-object/from16 v10, p4

    move-object/from16 v16, v4

    goto/16 :goto_4

    :cond_5
    move-object/from16 v6, p2

    move-object/from16 v10, p4

    .line 322
    invoke-virtual {v13, v11, v0, v6, v10}, Lio/dcloud/uts/android/ClassLogWrapper;->wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v8

    .line 324
    const-string v15, "object"

    invoke-static {v15, v12}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v16

    if-eqz v16, :cond_6

    move-object/from16 v16, v4

    instance-of v4, v8, Lio/dcloud/uts/gson/JsonObject;

    if-eqz v4, :cond_7

    .line 325
    check-cast v8, Lio/dcloud/uts/gson/JsonObject;

    invoke-virtual {v8}, Lio/dcloud/uts/gson/JsonObject;->entrySet()Ljava/util/Set;

    move-result-object v4

    const-string v8, "wrapObjectRet.entrySet()"

    invoke-static {v4, v8}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v4, Ljava/lang/Iterable;

    .line 1156
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_3
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v8

    if-eqz v8, :cond_9

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Ljava/util/Map$Entry;

    .line 326
    invoke-interface {v8}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/String;

    invoke-interface {v8}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v14, v9, v8}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_3

    :cond_6
    move-object/from16 v16, v4

    .line 328
    :cond_7
    invoke-static {v15, v12}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_8

    const-string v4, "\"[Circular]\""

    invoke-virtual {v8}, Lio/dcloud/uts/gson/JsonElement;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-static {v4, v12}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_8

    .line 329
    invoke-virtual {v14, v9, v8}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 330
    new-instance v4, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-static {v11}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {v13, v11}, Lio/dcloud/uts/android/ClassLogWrapper;->logClassOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v8

    invoke-direct {v4, v8}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    const-string v8, "className"

    invoke-virtual {v14, v8, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    goto :goto_4

    .line 333
    :cond_8
    invoke-virtual {v14, v9, v8}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 337
    :cond_9
    :goto_4
    check-cast v14, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v3, v14}, Lio/dcloud/uts/gson/JsonArray;->add(Lio/dcloud/uts/gson/JsonElement;)V

    :goto_5
    add-int/lit8 v7, v7, 0x1

    move-object/from16 v4, v16

    const/4 v6, 0x0

    goto/16 :goto_0

    .line 344
    :cond_a
    const-class v4, Lio/dcloud/uts/UTSJSONObject;

    invoke-virtual {v4, v0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_b

    .line 345
    const-string v0, "__$originalPosition"

    invoke-virtual {v1, v0}, Lio/dcloud/uts/gson/JsonObject;->has(Ljava/lang/String;)Z

    move-result v4

    if-nez v4, :cond_b

    .line 347
    new-instance v4, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {v4}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 348
    new-instance v5, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string v6, "UTSJSONObject"

    invoke-direct {v5, v6}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v5, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v4, v8, v5}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 349
    check-cast v4, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v1, v0, v4}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 353
    :cond_b
    const-string v0, "properties"

    check-cast v3, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v2, v0, v3}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 354
    check-cast v2, Lio/dcloud/uts/gson/JsonElement;

    invoke-virtual {v1, v9, v2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    return-object v1
.end method


# virtual methods
.method public final wrapClass(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Ljava/util/HashSet<",
            "Ljava/lang/Object;",
            ">;",
            "Ljava/util/concurrent/atomic/AtomicInteger;",
            ")",
            "Lio/dcloud/uts/gson/JsonElement;"
        }
    .end annotation

    const-string v0, "inputStock"

    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "limitCounter"

    invoke-static {p4, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 45
    const-string v0, "null"

    if-nez p1, :cond_1

    if-nez p2, :cond_0

    .line 48
    new-instance p1, Lio/dcloud/uts/gson/JsonObject;

    invoke-direct {p1}, Lio/dcloud/uts/gson/JsonObject;-><init>()V

    .line 49
    new-instance p2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {p2, v0}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast p2, Lio/dcloud/uts/gson/JsonElement;

    const-string p3, "type"

    invoke-virtual {p1, p3, p2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 50
    new-instance p2, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {p2, v0}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast p2, Lio/dcloud/uts/gson/JsonElement;

    const-string p3, "value"

    invoke-virtual {p1, p3, p2}, Lio/dcloud/uts/gson/JsonObject;->add(Ljava/lang/String;Lio/dcloud/uts/gson/JsonElement;)V

    .line 51
    check-cast p1, Lio/dcloud/uts/gson/JsonElement;

    return-object p1

    .line 54
    :cond_0
    new-instance p1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {p1, v0}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast p1, Lio/dcloud/uts/gson/JsonElement;

    return-object p1

    .line 61
    :cond_1
    invoke-virtual {p3, p1}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 65
    invoke-virtual {p3, p1}, Ljava/util/HashSet;->remove(Ljava/lang/Object;)Z

    .line 66
    new-instance p1, Lio/dcloud/uts/gson/JsonPrimitive;

    const-string p2, "[Circular]"

    invoke-direct {p1, p2}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast p1, Lio/dcloud/uts/gson/JsonElement;

    return-object p1

    .line 68
    :cond_2
    invoke-virtual {p3, p1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 71
    new-instance v1, Lio/dcloud/uts/gson/JsonPrimitive;

    invoke-direct {v1, v0}, Lio/dcloud/uts/gson/JsonPrimitive;-><init>(Ljava/lang/String;)V

    check-cast v1, Lio/dcloud/uts/gson/JsonElement;

    .line 73
    :try_start_0
    invoke-direct {p0, p1, p2, p3, p4}, Lio/dcloud/uts/android/ClassLogWrapper;->doJsonConvert(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/HashSet;Ljava/util/concurrent/atomic/AtomicInteger;)Lio/dcloud/uts/gson/JsonElement;

    move-result-object v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 80
    :catchall_0
    invoke-virtual {p3, p1}, Ljava/util/HashSet;->remove(Ljava/lang/Object;)Z

    return-object v1
.end method

.method public final wrapNumberText(Ljava/lang/Object;)Ljava/lang/String;
    .locals 16

    move-object/from16 v0, p1

    if-nez v0, :cond_0

    .line 96
    const-string v0, "null"

    return-object v0

    .line 99
    :cond_0
    instance-of v1, v0, Ljava/lang/Number;

    if-eqz v1, :cond_d

    const/4 v1, 0x0

    .line 101
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    const-string v2, "0"

    if-nez v1, :cond_c

    const-wide/16 v3, 0x0

    invoke-static {v3, v4}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v1

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_c

    const-wide/16 v3, 0x0

    invoke-static {v3, v4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    goto/16 :goto_2

    :cond_1
    const/4 v1, 0x0

    .line 105
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v0, v3}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    instance-of v3, v0, Ljava/lang/Integer;

    if-eqz v3, :cond_2

    goto/16 :goto_2

    .line 109
    :cond_2
    move-object v3, v0

    check-cast v3, Ljava/lang/Number;

    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    move-result v4

    if-nez v4, :cond_3

    instance-of v4, v0, Ljava/lang/Short;

    if-nez v4, :cond_c

    instance-of v4, v0, Ljava/lang/Byte;

    if-eqz v4, :cond_3

    goto/16 :goto_2

    .line 118
    :cond_3
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    check-cast v2, Ljava/lang/Number;

    invoke-static {v3, v2}, Lio/dcloud/uts/NumberKt;->div(Ljava/lang/Number;Ljava/lang/Number;)Ljava/lang/Number;

    move-result-object v2

    const-wide/high16 v4, 0x3ff0000000000000L    # 1.0

    invoke-static {v4, v5}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v4

    invoke-static {v2, v4}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_4

    .line 119
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    move-result v0

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v2

    goto/16 :goto_2

    .line 122
    :cond_4
    instance-of v2, v0, Ljava/lang/Double;

    const/16 v4, 0x11

    if-nez v2, :cond_6

    instance-of v2, v0, Ljava/lang/Float;

    if-eqz v2, :cond_5

    goto :goto_0

    .line 156
    :cond_5
    new-instance v0, Ljava/math/BigDecimal;

    invoke-virtual {v3}, Ljava/lang/Number;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 158
    new-instance v1, Ljava/math/MathContext;

    .line 160
    sget-object v2, Ljava/math/RoundingMode;->HALF_UP:Ljava/math/RoundingMode;

    .line 158
    invoke-direct {v1, v4, v2}, Ljava/math/MathContext;-><init>(ILjava/math/RoundingMode;)V

    .line 157
    invoke-virtual {v0, v1}, Ljava/math/BigDecimal;->round(Ljava/math/MathContext;)Ljava/math/BigDecimal;

    move-result-object v0

    .line 163
    invoke-static {v0}, Lio/dcloud/uts/android/ClassLogWrapper$$ExternalSyntheticBackportWithForwarding0;->m(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object v0

    invoke-virtual {v0}, Ljava/math/BigDecimal;->toPlainString()Ljava/lang/String;

    move-result-object v2

    goto/16 :goto_2

    .line 127
    :cond_6
    :goto_0
    sget-object v2, Lio/dcloud/uts/UTSNumber;->INSTANCE:Lio/dcloud/uts/UTSNumber;

    invoke-virtual {v2}, Lio/dcloud/uts/UTSNumber;->getNaN()Ljava/lang/Number;

    move-result-object v2

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_b

    sget-object v2, Lio/dcloud/uts/UTSNumber;->INSTANCE:Lio/dcloud/uts/UTSNumber;

    invoke-virtual {v2}, Lio/dcloud/uts/UTSNumber;->getPOSITIVE_INFINITY()Ljava/lang/Number;

    move-result-object v2

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_b

    sget-object v2, Lio/dcloud/uts/UTSNumber;->INSTANCE:Lio/dcloud/uts/UTSNumber;

    invoke-virtual {v2}, Lio/dcloud/uts/UTSNumber;->getNEGATIVE_INFINITY()Ljava/lang/Number;

    move-result-object v2

    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_7

    goto/16 :goto_1

    .line 130
    :cond_7
    invoke-virtual {v3}, Ljava/lang/Number;->doubleValue()D

    move-result-wide v5

    invoke-static {v5, v6}, Ljava/lang/Math;->abs(D)D

    move-result-wide v5

    const-wide v7, 0x3eb0c6f7a0b5ed8dL    # 1.0E-6

    cmpl-double v0, v5, v7

    if-ltz v0, :cond_8

    const-wide v7, 0x444b1ae4d6e2ef50L    # 1.0E21

    cmpg-double v0, v5, v7

    if-gez v0, :cond_8

    .line 132
    new-instance v0, Ljava/math/BigDecimal;

    invoke-virtual {v3}, Ljava/lang/Number;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 134
    new-instance v1, Ljava/math/MathContext;

    .line 136
    sget-object v2, Ljava/math/RoundingMode;->HALF_UP:Ljava/math/RoundingMode;

    .line 134
    invoke-direct {v1, v4, v2}, Ljava/math/MathContext;-><init>(ILjava/math/RoundingMode;)V

    .line 133
    invoke-virtual {v0, v1}, Ljava/math/BigDecimal;->round(Ljava/math/MathContext;)Ljava/math/BigDecimal;

    move-result-object v0

    .line 139
    invoke-static {v0}, Lio/dcloud/uts/android/ClassLogWrapper$$ExternalSyntheticBackportWithForwarding0;->m(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object v0

    invoke-virtual {v0}, Ljava/math/BigDecimal;->toPlainString()Ljava/lang/String;

    move-result-object v2

    goto :goto_2

    .line 141
    :cond_8
    invoke-virtual {v3}, Ljava/lang/Number;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lio/dcloud/uts/StringKt;->toLowerCase(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 142
    move-object v0, v2

    check-cast v0, Ljava/lang/CharSequence;

    const-string v3, ".0e"

    check-cast v3, Ljava/lang/CharSequence;

    const/4 v8, 0x2

    const/4 v9, 0x0

    invoke-static {v0, v3, v1, v8, v9}, Lkotlin/text/StringsKt;->contains$default(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_9

    const/4 v6, 0x4

    const/4 v7, 0x0

    .line 143
    const-string v3, ".0e"

    const-string v4, "e"

    const/4 v5, 0x0

    invoke-static/range {v2 .. v7}, Lkotlin/text/StringsKt;->replace$default(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    .line 146
    :cond_9
    move-object v10, v2

    check-cast v10, Ljava/lang/CharSequence;

    const-string v0, "e-"

    check-cast v0, Ljava/lang/CharSequence;

    invoke-static {v10, v0, v1, v8, v9}, Lkotlin/text/StringsKt;->contains$default(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_a

    const-string v0, "e"

    move-object v3, v0

    check-cast v3, Ljava/lang/CharSequence;

    invoke-static {v10, v3, v1, v8, v9}, Lkotlin/text/StringsKt;->contains$default(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_a

    .line 147
    filled-new-array {v0}, [Ljava/lang/String;

    move-result-object v11

    const/4 v14, 0x6

    const/4 v15, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    invoke-static/range {v10 .. v15}, Lkotlin/text/StringsKt;->split$default(Ljava/lang/CharSequence;[Ljava/lang/String;ZIILjava/lang/Object;)Ljava/util/List;

    move-result-object v0

    .line 148
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "e+"

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_a
    return-object v2

    .line 128
    :cond_b
    :goto_1
    invoke-virtual {v3}, Ljava/lang/Number;->toString()Ljava/lang/String;

    move-result-object v2

    .line 170
    :cond_c
    :goto_2
    const-string v0, "numText"

    invoke-static {v2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    return-object v2

    .line 172
    :cond_d
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
