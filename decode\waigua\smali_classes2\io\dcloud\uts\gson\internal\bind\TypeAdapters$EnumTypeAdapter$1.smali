.class Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1;
.super Ljava/lang/Object;
.source "TypeAdapters.java"

# interfaces
.implements Ljava/security/PrivilegedAction;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter;-><init>(Ljava/lang/Class;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/security/PrivilegedAction<",
        "Ljava/lang/Void;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter;

.field final synthetic val$field:Ljava/lang/reflect/Field;


# direct methods
.method constructor <init>(Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter;Ljava/lang/reflect/Field;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 745
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1;->this$0:Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter;

    iput-object p2, p0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1;->val$field:Ljava/lang/reflect/Field;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic run()Ljava/lang/Object;
    .locals 1

    .line 745
    invoke-virtual {p0}, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1;->run()Ljava/lang/Void;

    move-result-object v0

    return-object v0
.end method

.method public run()Ljava/lang/Void;
    .locals 2

    .line 747
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/bind/TypeAdapters$EnumTypeAdapter$1;->val$field:Ljava/lang/reflect/Field;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/lang/reflect/Field;->setAccessible(Z)V

    const/4 v0, 0x0

    return-object v0
.end method
