.class public final Lio/dcloud/uts/prompt/R$drawable;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/prompt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "drawable"
.end annotation


# static fields
.field public static uni_app_uni_prompt_actionsheet_bg_rounded:I = 0x7f070101

.field public static uni_app_uni_prompt_actionsheet_bg_rounded_night:I = 0x7f070102

.field public static uni_app_uni_prompt_actionsheet_button_select_total:I = 0x7f070103

.field public static uni_app_uni_prompt_actionsheet_button_select_total_night:I = 0x7f070104

.field public static uni_app_uni_prompt_actionsheet_button_select_total_night_top:I = 0x7f070105

.field public static uni_app_uni_prompt_actionsheet_button_select_total_top:I = 0x7f070106

.field public static uni_app_uni_prompt_circle_white_progress:I = 0x7f070107

.field public static uni_app_uni_prompt_dialog_bg_rounded:I = 0x7f070108

.field public static uni_app_uni_prompt_dialog_bg_rounded_night:I = 0x7f070109

.field public static uni_app_uni_prompt_dialog_button_select_left:I = 0x7f07010a

.field public static uni_app_uni_prompt_dialog_button_select_left_night:I = 0x7f07010b

.field public static uni_app_uni_prompt_dialog_button_select_right:I = 0x7f07010c

.field public static uni_app_uni_prompt_dialog_button_select_right_night:I = 0x7f07010d

.field public static uni_app_uni_prompt_dialog_button_select_total:I = 0x7f07010e

.field public static uni_app_uni_prompt_dialog_button_select_total_night:I = 0x7f07010f

.field public static uni_app_uni_prompt_loading_bg:I = 0x7f070110

.field public static uni_app_uni_prompt_shape_btn_bg_normal_left:I = 0x7f070111

.field public static uni_app_uni_prompt_shape_btn_bg_normal_left_night:I = 0x7f070112

.field public static uni_app_uni_prompt_shape_btn_bg_normal_right:I = 0x7f070113

.field public static uni_app_uni_prompt_shape_btn_bg_normal_right_night:I = 0x7f070114

.field public static uni_app_uni_prompt_shape_btn_bg_normal_total:I = 0x7f070115

.field public static uni_app_uni_prompt_shape_btn_bg_normal_total_night:I = 0x7f070116

.field public static uni_app_uni_prompt_shape_btn_bg_select_left:I = 0x7f070117

.field public static uni_app_uni_prompt_shape_btn_bg_select_left_night:I = 0x7f070118

.field public static uni_app_uni_prompt_shape_btn_bg_select_right:I = 0x7f070119

.field public static uni_app_uni_prompt_shape_btn_bg_select_right_night:I = 0x7f07011a

.field public static uni_app_uni_prompt_shape_btn_bg_select_total:I = 0x7f07011b

.field public static uni_app_uni_prompt_shape_btn_bg_select_total_night:I = 0x7f07011c


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
