.class abstract Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;
.super Ljava/lang/Object;
.source "LinkedTreeMap.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/gson/internal/LinkedTreeMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x402
    name = "LinkedTreeMapIterator"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "TT;>;"
    }
.end annotation


# instance fields
.field expectedModCount:I

.field lastReturned:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation
.end field

.field final synthetic this$0:Lio/dcloud/uts/gson/internal/LinkedTreeMap;


# direct methods
.method constructor <init>(Lio/dcloud/uts/gson/internal/LinkedTreeMap;)V
    .locals 1

    .line 534
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedTreeMap;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 530
    iget-object v0, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iget-object v0, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    const/4 v0, 0x0

    .line 531
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 532
    iget p1, p1, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    iput p1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->expectedModCount:I

    return-void
.end method


# virtual methods
.method public final hasNext()Z
    .locals 2

    .line 538
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedTreeMap;

    iget-object v1, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method final nextNode()Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node<",
            "TK;TV;>;"
        }
    .end annotation

    .line 542
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 543
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedTreeMap;

    iget-object v1, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->header:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eq v0, v1, :cond_1

    .line 546
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedTreeMap;

    iget v1, v1, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    iget v2, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->expectedModCount:I

    if-ne v1, v2, :cond_0

    .line 549
    iget-object v1, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    iput-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->next:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 550
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    return-object v0

    .line 547
    :cond_0
    new-instance v0, Ljava/util/ConcurrentModificationException;

    invoke-direct {v0}, Ljava/util/ConcurrentModificationException;-><init>()V

    throw v0

    .line 544
    :cond_1
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final remove()V
    .locals 3

    .line 554
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    if-eqz v0, :cond_0

    .line 557
    iget-object v1, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedTreeMap;

    const/4 v2, 0x1

    invoke-virtual {v1, v0, v2}, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->removeInternal(Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;Z)V

    const/4 v0, 0x0

    .line 558
    iput-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->lastReturned:Lio/dcloud/uts/gson/internal/LinkedTreeMap$Node;

    .line 559
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->this$0:Lio/dcloud/uts/gson/internal/LinkedTreeMap;

    iget v0, v0, Lio/dcloud/uts/gson/internal/LinkedTreeMap;->modCount:I

    iput v0, p0, Lio/dcloud/uts/gson/internal/LinkedTreeMap$LinkedTreeMapIterator;->expectedModCount:I

    return-void

    .line 555
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method
