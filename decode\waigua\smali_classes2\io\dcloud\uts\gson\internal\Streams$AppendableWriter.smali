.class final Lio/dcloud/uts/gson/internal/Streams$AppendableWriter;
.super Ljava/io/Writer;
.source "Streams.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lio/dcloud/uts/gson/internal/Streams;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "AppendableWriter"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;
    }
.end annotation


# instance fields
.field private final appendable:Ljava/lang/Appendable;

.field private final currentWrite:Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;


# direct methods
.method constructor <init>(Ljava/lang/Appendable;)V
    .locals 1

    .line 87
    invoke-direct {p0}, Ljava/io/Writer;-><init>()V

    .line 85
    new-instance v0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;

    invoke-direct {v0}, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;-><init>()V

    iput-object v0, p0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter;->currentWrite:Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;

    .line 88
    iput-object p1, p0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter;->appendable:Ljava/lang/Appendable;

    return-void
.end method


# virtual methods
.method public close()V
    .locals 0

    return-void
.end method

.method public flush()V
    .locals 0

    return-void
.end method

.method public write(I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 97
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter;->appendable:Ljava/lang/Appendable;

    int-to-char p1, p1

    invoke-interface {v0, p1}, Ljava/lang/Appendable;->append(C)Ljava/lang/Appendable;

    return-void
.end method

.method public write([CII)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 92
    iget-object v0, p0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter;->currentWrite:Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;

    iput-object p1, v0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;->chars:[C

    .line 93
    iget-object p1, p0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter;->appendable:Ljava/lang/Appendable;

    iget-object v0, p0, Lio/dcloud/uts/gson/internal/Streams$AppendableWriter;->currentWrite:Lio/dcloud/uts/gson/internal/Streams$AppendableWriter$CurrentWrite;

    add-int/2addr p3, p2

    invoke-interface {p1, v0, p2, p3}, Ljava/lang/Appendable;->append(Ljava/lang/CharSequence;II)Ljava/lang/Appendable;

    return-void
.end method
