if(!window.plus||window.plus&&!window.plus.isReady){var plusType=typeof window.plus;"function"==plusType||"object"==plusType?(window.plus.isReady=!0,navigator.plus=window.__html5plus__=window.plus):window.__html5plus__=window.plus=navigator.plus={isReady:!0}}!function(e){var t=e.tools={__UUID__:0,UNKNOWN:-1,IOS:0,ANDROID:1,platform:-1,debug:!1,UUID:function(e){return e+this.__UUID__+++(new Date).valueOf()},extend:function(e,t){for(var n in t)e[n]=t[n]},typeName:function(e){return Object.prototype.toString.call(e).slice(8,-1)},isDate:function(e){return"Date"==t.typeName(e)},isArray:function(e){return"Array"==t.typeName(e)},isDebug:function(){return e.tools.debug},stringify:function(e){return window.JSON3?window.JSON3.stringify(e):JSON.stringify(e)},isNumber:function(e){return"number"==typeof e||e instanceof Number},getElementOffsetInWebview:function(e,t){for(var n=0,i=e;i;)n+=i[t],i=i.offsetParent;return n},getElementOffsetXInWebview:function(e){return t.getElementOffsetInWebview(e,"offsetLeft")},getElementOffsetYInWebview:function(e){return t.getElementOffsetInWebview(e,"offsetTop")},execJSfile:function(e){var t=document.createElement("script");t.type="text/javascript",t.src=e,function e(t){var n=document.head,i=document.body;n?n.insertBefore(t,n.firstChild):i?i.insertBefore(t,i.firstChild):setTimeout(function(){e(t)},100)}(t)},copyObjProp2Obj:function(e,t,n){var i=n instanceof Array;for(var o in t){var r=!0;if(i)for(var c=0;c<n.length;c++)if(o==n[c]){r=!1;break}r?e[o]=t[o]:r=!0}},clone:function(e){if(!e||"function"==typeof e||t.isDate(e)||"object"!=typeof e)return e;var n,i;if(t.isArray(e)){for(n=[],i=0;i<e.length;++i)n.push(t.clone(e[i]));return n}for(i in n={},e)i in n&&n[i]==e[i]||(n[i]=t.clone(e[i]));return n}};t.debug=!(!window.__nativeparamer__||!window.__nativeparamer__.debug),t.platform=window._____platform_____}(window.plus),function(plus){window.__prompt__=window.prompt;var T=plus.tools,B=plus.bridge={NO_RESULT:0,OK:1,CLASS_NOT_FOUND_EXCEPTION:2,ILLEGAL_ACCESS_EXCEPTION:3,INSTANTIATION_EXCEPTION:4,MALFORMED_URL_EXCEPTION:5,IO_EXCEPTION:6,INVALID_ACTION:7,JSON_EXCEPTION:8,ERROR:9,callbacks:{},isInEvalJs:0,exec:function(e,t,n,i,o){if(n&&n.push(i),T.IOS==T.platform){if(window.webkit&&window.webkit.messageHandlers)return void window.webkit.messageHandlers.plus.postMessage(T.stringify([window.__HtMl_Id__,e,t,i||null,n]));"function"==typeof __dc_plus_Command_&&__dc_plus_Command_(T.stringify([window.__HtMl_Id__,e,t,i||null,n]))}else T.ANDROID==T.platform&&(o||window._____platform_os_version_____&&window._____platform_os_version_____<21?window.__prompt__(T.stringify(n),"pdr:"+T.stringify([e,t,!0])):window._bridge.prompt(T.stringify(n),"pdr:"+T.stringify([e,t,!0])))},execSync2:function(e,t,n,i,o){try{if(T.IOS==T.platform&&"function"==typeof __dc__plusGo){var r=T.stringify([window.__HtMl_Id__,e,t,null,n]),c=__dc__plusGo(r);return i?i(c):window.eval(c)}}catch(n){console.log("sf:"+t+"-"+e)}return B.execSync(e,t,n,i,o)},execSync:function(service,action,args,fn,force){if(T.IOS==T.platform){if("function"==typeof __dc__plusGo){var json=T.stringify([window.__HtMl_Id__,service,action,null,args]),ret=__dc__plusGo(json);return fn?fn(ret):window.eval(ret)}try{if(window.webkit&&window.webkit.messageHandlers){var json=T.stringify([window.__HtMl_Id__,service,action,null,args]),retValue=prompt(json,"__dc__plusGo:");return fn?fn(retValue):window.eval(retValue)}}catch(e){console.log("sf:"+action+"-"+service)}return window.eval(sync.responseText)}var ret;if(T.ANDROID==T.platform)return ret=force||window._____platform_os_version_____&&window._____platform_os_version_____<21?window.__prompt__(T.stringify(args),"pdr:"+T.stringify([service,action,!1])):window._bridge.prompt(T.stringify(args),"pdr:"+T.stringify([service,action,!1])),fn?fn(ret):eval(ret)},callbackFromNative:function(e,t){var n=B.callbacks[e];n&&(t.status==B.OK&&n.success?n.success&&n.success(t.message):n.fail&&n.fail(t.message),t.keepCallback||delete B.callbacks[e])},callbackId:function(e,t){var n=T.UUID("plus");return B.callbacks[n]={success:e,fail:t},n}}}(window.plus),plus.obj=plus.obj||{},plus.obj.Callback=function(){function e(){var e=this;e.__callbacks__={},e.__callback_id__=plus.bridge.callbackId(function(t){var n=t.evt,i=t.args,o=e.__callbacks__[n];if(o)for(var r=o.length,c=0;c<r;c++)e.onCallback(o[c],n,i)})}return e.prototype.addEventListener=function(e,t,n){var i=!1;return t&&(this.__callbacks__[e]||(this.__callbacks__[e]=[],i=!0),this.__callbacks__[e].push(t)),i},e.prototype.removeEventListener=function(e,t){var n=!1;return this.__callbacks__[e]&&(this.__callbacks__[e].pop(t),(n=0===this.__callbacks__[e].length)&&(this.__callbacks__[e]=null)),n},e}(),function(e,t){var n=(e=e).bridge,i=e.tools,o="Accelerometer",r=!1,c={},a=[],s=null,u=function(e,t,n){this.xAxis=e,this.yAxis=t,this.zAxis=n};function l(e){var t=n.callbackId(function(e){var t=a.slice(0);s=new u(e.x,e.y,e.z);for(var n=0,i=t.length;n<i;n++)t[n].win(s)},function(e){for(var t=a.slice(0),n=0,i=t.length;n<i;n++)t[n].fail(e)});n.exec(o,"start",[t,e]),r=!0}function _(e,t){return{win:e,fail:t}}function f(e){var t=a.indexOf(e);t>-1&&(a.splice(t,1),0===a.length&&(n.exec(o,"stop",[]),r=!1))}var d={getCurrentAcceleration:function(e,t,n){var i=_(function(t){f(i),e(t)},function(e){f(i),t&&t(e)});a.push(i),r||l(-1)},watchAcceleration:function(e,n,o){var u=o&&o.frequency&&"number"==typeof o.frequency?o.frequency:500,d=i.UUID("watch"),h=_(function(){},function(e){f(h),n&&n(e)});return a.push(h),c[d]={timer:t.setInterval(function(){s&&e(s)},u),listeners:h},r?s&&e(s):l(u),d},clearWatch:function(e){e&&c[e]&&(t.clearInterval(c[e].timer),f(c[e].listeners),delete c[e])}};e.accelerometer=d}(this.plus,this),function(e){var t=(e=e).bridge,n=e.tools;e.audio={getRecorder:function(){var e={_Audio_UUID__:n.UUID("Record"),supportedFormats:["amr","3gp","aac"],supportedSamplerates:[44100,16e3,8e3],record:function(e,n,i){var o=t.callbackId(n,i);t.exec("Audio","RecorderExecMethod",["record",[this._Audio_UUID__,o,e]])},stop:function(){t.exec("Audio","RecorderExecMethod",["stop",[this._Audio_UUID__]])},pause:function(){t.exec("Audio","RecorderExecMethod",["pause",[this._Audio_UUID__]])},resume:function(){t.exec("Audio","RecorderExecMethod",["resume",[this._Audio_UUID__]])}};return n.IOS==n.platform&&(e.supportedFormats=["wav","aac","amr","mp3"]),e},createPlayer:function(e){var i={_Player_Param:e,_Audio_Player_UUID_:n.UUID("Player"),play:function(e,n){var i=t.callbackId(e,n);t.exec("Audio","AudioExecMethod",["play",[this._Audio_Player_UUID_,i]])},pause:function(){t.exec("Audio","AudioExecMethod",["pause",[this._Audio_Player_UUID_]])},resume:function(){t.exec("Audio","AudioExecMethod",["resume",[this._Audio_Player_UUID_]])},stop:function(){t.exec("Audio","AudioExecMethod",["stop",[this._Audio_Player_UUID_]])},seekTo:function(e){t.exec("Audio","AudioExecMethod",["seekTo",[this._Audio_Player_UUID_,e]])},getDuration:function(){return t.execSync2("Audio","AudioSyncExecMethod",["getDuration",[this._Audio_Player_UUID_]])},getPosition:function(){return t.execSync2("Audio","AudioSyncExecMethod",["getPosition",[this._Audio_Player_UUID_]])},setRoute:function(e){t.exec("Audio","AudioExecMethod",["setRoute",[this._Audio_Player_UUID_,e]])},setSessionCategory:function(e){t.exec("Audio","AudioExecMethod",["setSessionCategory",[this._Audio_Player_UUID_,e]])},isPaused:function(){return t.execSync2("Audio","AudioSyncExecMethod",["getPaused",[this._Audio_Player_UUID_]])},getBuffered:function(){return t.execSync2("Audio","AudioSyncExecMethod",["getBuffered",[this._Audio_Player_UUID_]])},getStyles:function(e){return t.execSync2("Audio","AudioSyncExecMethod",["getStyles",[this._Audio_Player_UUID_,e]])},setStyles:function(e){t.exec("Audio","AudioExecMethod",["setStyles",[this._Audio_Player_UUID_,e]])},addEventListener:function(e,n){var i=t.callbackId(n);t.exec("Audio","AudioExecMethod",["addEventListener",[this._Audio_Player_UUID_,e,i]])},removeEventListener:function(e){t.exec("Audio","AudioExecMethod",["removeEventListener",[this._Audio_Player_UUID_,e]])},close:function(){t.exec("Audio","AudioExecMethod",["close",[this._Audio_Player_UUID_]])},playbackRate:function(e){t.exec("Audio","AudioExecMethod",["playbackRate",[this._Audio_Player_UUID_,e]])}},o={};return"string"==typeof i._Player_Param?o.src=i._Player_Param:o=i._Player_Param,t.execSync("Audio","AudioSyncExecMethod",["CreatePlayer",[i._Audio_Player_UUID_,o]]),i},ROUTE_SPEAKER:0,ROUTE_EARPIECE:1}}(window.plus),function(e){var t="barcode",n=(e=e).tools,i=e.bridge,o={};function r(e,o,r,c,a,s){var u=this;u.IDENTITY=t,u.onmarked=null,u.onerror=null,u.isClose=!1,u.__uuid__=n.UUID("bc"),u.callbackId=null;var l=null;a||(a=u.__uuid__),this.callbackId=i.callbackId(function(e){"function"==typeof u.onmarked&&u.onmarked(e.type,e.message,e.file)},function(e){"function"==typeof u.onerror&&u.onerror(e)}),div=document.getElementById(e),null!=div&&void 0!=div&&(div.addEventListener("resize",function(){var e=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight];i.exec(t,"resize",[e])},!1),l=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight]);var _=!0;void 0==s||null==s||s||(_=!1),_&&i.exec(t,"Barcode",[this.__uuid__,this.callbackId,a,l,o,r,c])}var c=r.prototype;c.setStyle=function(e){this.isClose||i.exec(t,"setStyle",[this.__uuid__,e])},c.start=function(e){this.isClose||i.exec(t,"start",[this.__uuid__,e])},c.setFlash=function(e){this.isClose||i.exec(t,"setFlash",[this.__uuid__,e])},c.cancel=function(){this.isClose||i.exec(t,"cancel",[this.__uuid__])},c.close=function(){this.isClose||(i.exec(t,"close",[this.__uuid__]),this.isClose=!0)};var a={Barcode:r,create:function(t,n,i,r){var c=new e.barcode.Barcode(null,n,i,r,t,!0);return o[c.__uuid__]=c,o[c.__uuid__]},getBarcodeById:function(n){if(n&&"string"==typeof n){var r=i.execSync(t,"getBarcodeById",[n]);if(null!=r&&null!=r.uuid){if(o[r.uuid])return o[r.uuid];if(null!=r&&void 0!=r){var c=new e.barcode.Barcode(null,r.filters,r.options,r.autoDecodeCharset,n,!1);return c.__uuid__=r.uuid,i.exec(t,"addCallBack",[c.__uuid__,c.callbackId]),o[c.__uuid__]=c,c}return null}}},scan:function(e,n,o,r,c){var a="function"!=typeof n?null:function(e){n(e.type,e.message,e.file,e.charSet)},s="function"!=typeof o?null:function(e){o(e)},u=i.callbackId(a,s);i.exec(t,"scan",[u,e,r,c])},QR:0,EAN13:1,EAN8:2,AZTEC:3,DATAMATRIX:4,UPCA:5,UPCE:6,CODABAR:7,CODE39:8,CODE93:9,CODE128:10,ITF:11,MAXICODE:12,PDF417:13,RSS14:14,RSSEXPANDED:15};e.barcode=a}(window.plus),function(e){var t=(e=e).bridge,n="Cache";e.cache={clear:function(e){var i=t.callbackId(function(t){e&&e()},null);window.localStorage.clear(),window.sessionStorage.clear(),t.exec(n,"clear",[i])},calculate:function(e){var i=t.callbackId(function(t){e&&e(t)},null);t.exec(n,"calculate",[i])},setMaxSize:function(e){t.exec(n,"setMaxSize",[e])}}}(window.plus),function(e){var t,n=(e=e).bridge;function i(){this.index=1,this.__busy__=!1,this.supportedImageResolutions=[],this.supportedVideoResolutions=[],this.supportedImageFormats=[],this.supportedVideoFormats=[]}var o=i.prototype;o.captureImage=function(e,t,i){var o=this;if(!o.__busy__){var r="function"!=typeof e?null:function(t){o.__busy__=!1,e(t)},c="function"!=typeof t?null:function(e){o.__busy__=!1,t(e)},a=n.callbackId(r,c);i||(i={}),i.index||(i.index=this.index),n.exec("Camera","captureImage",[a,i])}},o.startVideoCapture=function(e,t,i){var o=this;if(!o.__busy__){var r="function"!=typeof e?null:function(t){o.__busy__=!1,e(t)},c="function"!=typeof t?null:function(e){o.__busy__=!1,t(e)},a=n.callbackId(r,c);i||(i={}),i.index||(i.index=this.index),n.exec("Camera","startVideoCapture",[a,i])}},o.stopVideoCapture=function(){n.exec("Camera","stopVideoCapture",[])};var r={getCamera:function(o){if(t)return t;if((t=new i).index=o,e.tools.ANDROID==e.tools.platform){var r=n.callbackId(function(e){t.supportedImageFormats=e.supportedImageFormats,t.supportedVideoFormats=e.supportedVideoFormats,t.supportedImageResolutions=e.supportedImageResolutions,t.supportedVideoResolutions=e.supportedVideoResolutions},null);return(c=n.execSync("Camera","getCamera",[t.__UUID__,o,r]))&&(t.supportedImageFormats=c.supportedImageFormats,t.supportedVideoFormats=c.supportedVideoFormats,t.supportedImageResolutions=c.supportedImageResolutions,t.supportedVideoResolutions=c.supportedVideoResolutions),t}var c;return(c=n.execSync("Camera","getCamera",[t.__UUID__,o]))?(t.supportedImageFormats=c.supportedImageFormats,t.supportedVideoFormats=c.supportedVideoFormats,t.supportedImageResolutions=c.supportedImageResolutions,t.supportedVideoResolutions=c.supportedVideoResolutions):(t.supportedImageFormats=["png","jpg"],t.supportedImageResolutions=["640*480","1280*720","960*540","high","medium","low"],t.supportedVideoFormats=["mp4"],t.supportedVideoResolutions=["640*480","1280*720","960*540"]),t}};e.camera=r}(window.plus),function(e){var t,n,i=(e=e).bridge,o=e.tools,r=function(e){this.code=e||null};function c(e,t,n,i,o,r,c,a,s,u,l,_,f,d,h){this.id=e||null,this.rawId=h||null,this.target=0,this.displayName=t||null,this.name=n||null,this.nickname=i||null,this.phoneNumbers=o||null,this.emails=r||null,this.addresses=c||null,this.ims=a||null,this.organizations=s||null,this.birthday=u||null,this.note=l||null,this.photos=_||null,this.categories=f||null,this.urls=d||null}r.UNKNOWN_ERROR=0,r.INVALID_ARGUMENT_ERROR=1,r.TIMEOUT_ERROR=2,r.PENDING_OPERATION_ERROR=3,r.IO_ERROR=4,r.NOT_SUPPORTED_ERROR=5,r.PERMISSION_DENIED_ERROR=20;var a=c.prototype;function s(e){this.type=e}a.remove=function(e,t){var n=t&&function(e){t(new r(e))};if(null===this.id)n(r.UNKNOWN_ERROR);else{var o=i.callbackId(e,n);i.exec("Contacts","remove",[o,this.id,this.target],{cbid:o})}},a.clone=function(){var e=o.clone(this);function t(e){if(e)for(var t=e.length,n=0;n<t;++n)e[n].id=null}return e.id=null,t(e.phoneNumbers),t(e.emails),t(e.addresses),t(e.ims),t(e.organizations),t(e.categories),t(e.photos),t(e.urls),e},a.save=function(e,c){var a=this,s=function(e){c&&c(new r(e))},u=function(e){var t=e.birthday;if(null!==t){if(!o.isDate(t))try{t=new Date(t)}catch(e){t=null}o.isDate(t)&&(t=t.valueOf()),e.birthday=t}return e}(o.clone(this)),l=i.callbackId(function(i){if(i)try{var c=0==a.target?t.create(i):n.create(i);e&&(function(e,t){function n(e,t){if(e&&t)for(var n=e.length,i=0;i<n;++i)t[i].id=e[i].id}t.id=e.id,n(e.phoneNumbers,t.phoneNumbers),n(e.emails,t.emails),n(e.addresses,t.addresses),n(e.ims,t.ims),n(e.organizations,t.organizations),n(e.categories,t.categories),n(e.photos,t.photos),n(e.urls,t.urls)}(function(e){var t=e.birthday;try{o.isDate(t)||(e.birthday=new Date(parseFloat(t)))}catch(e){console.log("Cordova Contact convertIn error: exception creating date.")}return e}(c),a),e(a))}catch(e){console.log(e)}else s(r.UNKNOWN_ERROR)},s);i.exec("Contacts","save",[l,u,this.target],{cbid:l})};var u=s.prototype;u.create=function(e){var t=new c;for(var n in t.target=this.type,e)void 0!==t[n]&&e.hasOwnProperty(n)&&(t[n]=e[n]);return t},u.find=function(e,n,o,r){var c=i.callbackId(function(e){for(var i=[],o=0,r=e.length;o<r;o++)i.push(t.create(e[o]));n(i)},o);i.exec("Contacts","search",[c,e,r],{cbid:c})};var l=e.contacts={getAddressBook:function(e,o,c){0===e&&1===e||(e=0);var a=i.callbackId(function(i){o&&(l.ADDRESSBOOK_PHONE==e?(t=t||new s(0),o(t)):(n=n||new s(1),o(t)))},function(e){c(new r(e))});i.exec("Contacts","getAddressBook",[a,e],{cbid:a})},ADDRESSBOOK_PHONE:0,ADDRESSBOOK_SIM:1}}(window.plus),function(e){var t=(e=e).bridge,n=e.tools,i={server:"Downloader",getValue:function(e,t){return void 0===e?t:e}};function o(e,t,n){this.type=i.getValue(e,""),this.handles=[],this.capture=i.getValue(n,!1),"function"==typeof t&&this.handles.push(t)}function r(e,t,o){var r=this;r.id=n.UUID("dt"),r.url=i.getValue(e,""),r.downloadedSize=0,r.totalSize=0,r.options=t||{},r.filename=i.getValue(r.options.filename,""),r.method=i.getValue(r.options.method,"GET"),r.timeout=i.getValue(r.options.timeout,120),r.retry=i.getValue(r.options.retry,3),r.retryInterval=i.getValue(r.options.retryInterval,30),r.priority=i.getValue(r.options.priority,1),r.onCompleted=o||null,r.eventHandlers={},r.data=i.getValue(r.options.data,null),r.__requestHeaders__={},r.__responseHeaders__={},r.__noParseResponseHeader__=null,r.__cacheReponseHeaders__={}}o.prototype.fire=function(e){for(var t=0;t<this.handles.length;++t)this.handles[t].apply(this,arguments)};var c=r.prototype;c.getFileName=function(){return this.filename},c.start=function(){t.exec(i.server,"start",[this.id,this.__requestHeaders__])},c.pause=function(){t.exec(i.server,"pause",[this.id])},c.resume=function(){t.exec(i.server,"resume",[this.id])},c.abort=function(){t.exec(i.server,"abort",[this.id])},c.getAllResponseHeaders=function(){if(this.__noParseResponseHeader__)return this.__noParseResponseHeader__;var e="";for(var t in this.__responseHeaders__)e=e+t+" : "+this.__responseHeaders__[t]+"\r\n";return this.__noParseResponseHeader__=e,this.__noParseResponseHeader__},c.getResponseHeader=function(e){if("string"==typeof e){var t=null;if(e=e.toLowerCase(),t=this.__cacheReponseHeaders__[e])return t;for(var n in this.__responseHeaders__){var i=this.__responseHeaders__[n];e===(n=n.toLowerCase())&&(t=t?t+", "+i:i)}return this.__cacheReponseHeaders__[e]=t,t}return""},c.setRequestHeader=function(e,t){if("string"==typeof e&&"string"==typeof t){var n=this.__requestHeaders__[e];this.__requestHeaders__[e]=n?n+", "+t:t}},c.addEventListener=function(e,t,n){if("string"==typeof e&&"function"==typeof t){var i=e.toLowerCase();void 0===this.eventHandlers[i]?this.eventHandlers[i]=new o(e,t,n):this.eventHandlers[i].handles.push(t)}},c.__handlerEvt__=function(e){var t=this;t.filename=i.getValue(e.filename,t.filename),t.state=i.getValue(e.state,t.state),t.downloadedSize=i.getValue(e.downloadedSize,t.downloadedSize),t.totalSize=i.getValue(e.totalSize,t.totalSize),t.__responseHeaders__=i.getValue(e.headers,{});var n=this.eventHandlers.statechanged;n&&n.fire(t,e.status||null),4==t.state&&"function"==typeof t.onCompleted&&t.onCompleted(t,e.status||null)};var a=e.downloader={__taskList__:[],createDownload:function(e,n,o){if("string"==typeof e){var c=new r(e,n,o);return a.__taskList__[c.id]=c,t.exec(i.server,"createDownload",[c]),c}return null},enumerate:function(e,n){var o=t.callbackId(function(t){for(var n=[],o=t.length,c=0,s=a.__taskList__;c<o;c++){var u=t[c];if(u&&u.uuid){var l=s[u.uuid];l||((l=new r).id=u.uuid,s[l.id]=l),l.state=i.getValue(u.state,l.state),l.options=i.getValue(u.options,l.options),l.filename=i.getValue(u.filename,l.filename),l.url=i.getValue(u.url,l.url),l.downloadedSize=i.getValue(u.downloadedSize,l.downloadedSize),l.totalSize=i.getValue(u.totalSize,l.totalSize),l.__responseHeaders__=i.getValue(t.headers,l.__responseHeaders__),n.push(l)}}"function"==typeof e&&e(n)});t.exec(i.server,"enumerate",[o,n])},clear:function(e){var n=-1e4;("number"==typeof e||e instanceof Number)&&(n=e),t.exec(i.server,"clear",[n])},startAll:function(){t.exec(i.server,"startAll",[0])},__handlerEvt__:function(e,t){var n=a.__taskList__[e];n&&(6==t.state&&delete a.__taskList__[e],n.__handlerEvt__(t))}}}(window.plus),function(e){var t=(e=e).bridge,n=0,i=1;function o(e,t){this.code=e||null,this.message=t||""}o.BUSY=1;var r=e.gallery={__galleryStatus:n,onPickImageFinished:null,pick:function(e,c,a){if(i!=r.__galleryStatus){r.__galleryStatus=i;var s=t.callbackId(function(t){if(r.__galleryStatus=n,"function"==typeof e)if(t&&t.multiple){var i={};i.files=t.files,e(i)}else e(t.files[0])},function(e){r.__galleryStatus=n,"function"==typeof c&&c(e)}),u=t.callbackId(function(){"function"==typeof a.onmaxed&&a.onmaxed()});t.exec("Gallery","pick",[s,a,u],{cbid:s})}else window.setTimeout(function(){"function"==typeof c&&c(new o(o.BUSY,"The album has been opened"))},0)},save:function(e,n,i){if("string"==typeof e){var o=t.callbackId(function(e){"function"==typeof n&&n(e)},function(e){"function"==typeof i&&i(e)});return t.exec("Gallery","save",[e,o],{cbid:o}),!0}return!1},compress:function(e,n,i){if("string"==typeof e){var o=t.callbackId(function(e){"function"==typeof n&&n(e)},function(e){"function"==typeof i&&i(e)});return t.exec("Gallery","compress",[e,o],{cbid:o}),!0}return!1}}}(window.plus),function(e){var t="Geolocation",n=(e=e).bridge,i=e.tools,o={};function r(e,t,n,i,o,r,c){this.latitude=e,this.longitude=t,this.accuracy=void 0!==i?i:null,this.altitude=void 0!==n?n:null,this.heading=void 0!==o?o:null,this.speed=void 0!==r?r:null,0!==this.speed&&null!==this.speed||(this.heading=NaN),this.altitudeAccuracy=void 0!==c?c:null}function c(e,t){e?(this.coordsType=e.coordsType,this.address=e.address,this.addresses=e.addresses,this.coords=new r(e.latitude,e.longitude,e.altitude,e.accuracy,e.heading,e.velocity,e.altitudeAccuracy)):this.coords=new r,this.timestamp=void 0!==t?t:(new Date).getTime()}function a(e,t){this.code=e||null,this.message=t||""}function s(e){var t={maximumAge:1e3,enableHighAccuracy:!1,timeout:1/0,geocode:!0};return e&&(void 0!==e.maximumAge&&!isNaN(e.maximumAge)&&e.maximumAge>0&&(t.maximumAge=e.maximumAge),void 0!==e.enableHighAccuracy&&(t.enableHighAccuracy=e.enableHighAccuracy),void 0===e.timeout||isNaN(e.timeout)||(e.timeout<0?t.timeout=0:t.timeout=e.timeout),e.coordsType&&(t.coordsType=e.coordsType),e.provider&&(t.provider=e.provider),void 0!==e.geocode&&(t.geocode=e.geocode)),t}function u(e,t){var n=setTimeout(function(){l(n),n=null,e(new a(a.TIMEOUT,"Position retrieval timed out."))},t);return n}function l(e){!0!==e&&clearTimeout(e)}a.PERMISSION_DENIED=1,a.POSITION_UNAVAILABLE=2,a.TIMEOUT=3,a.UNKNOWN_ERROR=4;var _=e.geolocation={lastPosition:null,getCurrentPosition:function(e,t,n){f(e,t,n,!0)},watchPosition:function(e,r,d){d=s(d);var h=i.UUID("timer");o[h]=f(e,r,d,!1);var p=function(e){o[h].isStop=!0,l(o[h].timer);var t=new a(e.code,e.message);r&&r(t)},y=n.callbackId(function(t){if(!o[h].isStop){l(o[h].timer),d.timeout!==1/0&&(o[h].timer=u(p,d.timeout));var n=new c({latitude:t.latitude,longitude:t.longitude,altitude:t.altitude,accuracy:t.accuracy,heading:t.heading,velocity:t.velocity,coordsType:t.coordsType,address:t.address,addresses:t.addresses,altitudeAccuracy:t.altitudeAccuracy},void 0===t.timestamp?(new Date).getTime():t.timestamp instanceof Date?t.timestamp.getTime():t.timestamp);_.lastPosition=n,e(n)}},p);return n.exec(t,"watchPosition",[y,h,d.enableHighAccuracy,d.coordsType,d.provider,d.geocode,d.timeout,d.maximumAge],{cbid:y,l:location.href}),h},clearWatch:function(e){e&&void 0!==o[e]&&(l(o[e].timer),o[e].isStop=!0,o[e].timer=!1,n.exec(t,"clearWatch",[e]))}};function f(e,i,o,r){o=s(o);var f={timer:null,isStop:!1},d=function(e){l(f.timer),f.timer=null;var t=new a(e.code,e.message);i&&i(t)};if(_.lastPosition&&o.maximumAge&&(new Date).getTime()-_.lastPosition.timestamp<=o.maximumAge)e(_.lastPosition);else if(0===o.timeout)d(new a(a.TIMEOUT,"timeout value in PositionOptions set to 0 and no cached Position object available, or cached Position object's age exceeds provided PositionOptions' maximumAge parameter."));else if(o.timeout!==1/0?f.timer=u(d,o.timeout):f.timer=!0,r){var h=n.callbackId(function(t){if(l(f.timer),f.timer){var n=new c({latitude:t.latitude,longitude:t.longitude,altitude:t.altitude,accuracy:t.accuracy,heading:t.heading,velocity:t.velocity,coordsType:t.coordsType,address:t.address,addresses:t.addresses,altitudeAccuracy:t.altitudeAccuracy},void 0===t.timestamp?(new Date).getTime():t.timestamp instanceof Date?t.timestamp.getTime():t.timestamp);_.lastPosition=n,e(n)}},d);n.exec(t,"getCurrentPosition",[h,o.enableHighAccuracy,o.maximumAge,o.coordsType,o.provider,o.geocode,o.timeout],{cbid:h,l:location.href})}return f}}(window.plus),function(e){var t=(e=e).bridge,n=(e.tools,[]),i={NATIVEF:"File",exec:function(e,n,o,r){var c=t.callbackId(e,n);t.exec(i.NATIVEF,o,[c,r])}};function o(e){this.code=e.code||null,this.message=e.message||""}function r(e){var t="unknown error";switch(e){case o.NOT_FOUND_ERR:t="file not found";break;case o.SECURITY_ERR:t="not authorized";break;case o.ABORT_ERR:t="cancel";break;case o.NOT_READABLE_ERR:t="not allowed to read";break;case o.ENCODING_ERR:t="Coding error";break;case o.NO_MODIFICATION_ALLOWED_ERR:t="no modification allowed";break;case o.INVALID_STATE_ERR:t="invalid state";break;case o.SYNTAX_ERR:t="syntax error";break;case o.INVALID_MODIFICATION_ERR:t="invalid modification";break;case o.QUOTA_EXCEEDED_ERR:t="execution error";break;case o.TYPE_MISMATCH_ERR:t="type mismatch";break;case o.PATH_EXISTS_ERR:t="the path exists"}return{code:e,message:t}}function c(e,t){t=t||{},this.type=e,this.bubbles=!1,this.cancelBubble=!1,this.cancelable=!1,this.lengthComputable=!1,this.loaded=t.loaded||0,this.total=t.total||0,this.target=t.target||null}function a(e,t,n,i,o){this.size=o||0,this.type=n||null,this.name=e||"",this.lastModifiedDate=new Date(i)||null,this.fullPath=t||null}o.NOT_FOUND_ERR=1,o.SECURITY_ERR=2,o.ABORT_ERR=3,o.NOT_READABLE_ERR=4,o.ENCODING_ERR=5,o.NO_MODIFICATION_ALLOWED_ERR=6,o.INVALID_STATE_ERR=7,o.SYNTAX_ERR=8,o.INVALID_MODIFICATION_ERR=9,o.QUOTA_EXCEEDED_ERR=10,o.TYPE_MISMATCH_ERR=11,o.PATH_EXISTS_ERR=12;var s=a.prototype;function u(e,t,n,i,o,r){this.isFile=void 0!==e&&e,this.isDirectory=void 0!==t&&t,this.name=n||"",this.fullPath=i||"",this.fileSystem=o||null,this.__PURL__=r||"",this.__remoteURL__=r?"http://localhost:13131/"+r:""}s.slice=function(e,t,n){var i=this.size>0?this.size-1:0,o=0,r=i;if(arguments.length&&(o=e<0?Math.max(i+e,0):Math.min(i,e)),arguments.length>=2&&(r=t<0?Math.max(i+t,0):Math.min(t,i)),r<o)return null;var c=new a(this.name,this.fullPath,this.type,this.lastModifiedDate,r-o+1);return c.start=o,c.end=r,c},s.close=function(){};var l=u.prototype;function _(e,t,n,i){l.constructor.apply(this,[!0,!1,e,t,n,i])}function f(e,t,n,i){l.constructor.apply(this,[!1,!0,e,t,n,i])}l.getMetadata=function(e,t,n){var r="function"!=typeof e?null:function(t){var n=new function(e){this.modificationTime=void 0!==e?new Date(e):null,this.size=0,this.directoryCount=0,this.fileCount=0}(t.lastModifiedDate);n.size=t.size,n.directoryCount=t.directoryCount,n.fileCount=t.fileCount,e(n)},c="function"!=typeof t?null:function(e){t(new o(e))};i.exec(r,c,"getMetadata",[this.fullPath,n])},l.setMetadata=function(e,t,n){i.exec(e,t,"setMetadata",[this.fullPath,n])},l.moveTo=function(e,t,n,c){var a=this,s=function(e){"function"==typeof c&&c(new o(e))};if(e){var u=this.fullPath,l=t||this.name;i.exec(function(e){if(e){if("function"==typeof n){var t=e.isDirectory?new f(e.name,e.fullPath,a.fileSystem,e.remoteURL):new _(e.name,e.fullPath,a.fileSystem,e.remoteURL);try{n(t)}catch(e){}}}else s(r(o.NOT_FOUND_ERR))},s,"moveTo",[u,e.fullPath,l])}else s(r(o.NOT_FOUND_ERR))},l.copyTo=function(e,t,n,c){var a=this,s=function(e){"function"==typeof c&&c(new o(e))};if(e){var u=this.fullPath,l=t||this.name;i.exec(function(e){if(e){if("function"==typeof n){var t=e.isDirectory?new f(e.name,e.fullPath,e.fileSystem,a.remoteURL):new _(e.name,e.fullPath,a.fileSystem,e.remoteURL);try{n(t)}catch(e){}}}else s(r(o.NOT_FOUND_ERR))},s,"copyTo",[u,e.fullPath,l])}else s(r(o.NOT_FOUND_ERR))},l.remove=function(e,t){var n="function"!=typeof t?null:function(e){t(new o(e))};i.exec(e,n,"remove",[this.fullPath])},l.toURL=function(){return this.__PURL__},l.toLocalURL=function(){return"file://"+this.fullPath},l.toRemoteURL=function(){return this.__remoteURL__},l.getParent=function(e,t){var n=this,r="function"!=typeof e?null:function(t){var i=new f(t.name,t.fullPath,n.fileSystem,t.remoteURL);e(i)},c="function"!=typeof t?null:function(e){t(new o(e))};i.exec(r,c,"getParent",[this.fullPath])},_.prototype=new u,_.prototype.constructor=_,_.prototype.createWriter=function(e,t){this.file(function(n){var i=new v(n);null===i.fileName||""===i.fileName?"function"==typeof t&&t(new o(r(o.INVALID_STATE_ERR))):"function"==typeof e&&e(i)},t)},_.prototype.file=function(e,t){var n="function"!=typeof e?null:function(t){var n=new a(t.name,t.fullPath,t.type,t.lastModifiedDate,t.size);e(n)},r="function"!=typeof t?null:function(e){t(new o(e))};i.exec(n,r,"getFileMetadata",[this.fullPath])};var d=new u;function h(e,t){this.path=e||null,this.__fileSystem__=t||null}function p(){this.fileName="",this.readyState=0,this.result=null,this.error=null,this.onloadstart=null,this.onprogress=null,this.onload=null,this.onabort=null,this.onerror=null,this.onloadend=null}f.prototype=d,d.constructor=f,d.createReader=function(){return new h(this.fullPath,this.fileSystem)},d.getDirectory=function(e,t,n,r){var c=this,a="function"!=typeof n?null:function(e){var t=new f(e.name,e.fullPath,c.fileSystem,e.remoteURL);n(t)},s="function"!=typeof r?null:function(e){r(new o(e))};i.exec(a,s,"getDirectory",[this.fullPath,e,t])},d.removeRecursively=function(e,t){var n="function"!=typeof t?null:function(e){t(new o(e))};i.exec(e,n,"removeRecursively",[this.fullPath])},d.getFile=function(e,t,n,r){var c=this,a="function"!=typeof n?null:function(e){var t=new _(e.name,e.fullPath,c.fileSystem,e.remoteURL);n(t)},s="function"!=typeof r?null:function(e){r(new o(e))};i.exec(a,s,"getFile",[this.fullPath,e,t])},h.prototype.readEntries=function(e,t){var n=this,r="function"!=typeof e?null:function(t){for(var i=[],o=0;o<t.length;o++){var r=null;t[o].isDirectory?r=new f(t[o].name,t[o].fullPath,n.__fileSystem__,t[o].remoteURL):t[o].isFile&&(r=new _(t[o].name,t[o].fullPath,n.__fileSystem__,t[o].remoteURL)),i.push(r)}e(i)},c="function"!=typeof t?null:function(e){t(new o(e))};i.exec(r,c,"readEntries",[this.path])},p.EMPTY=0,p.LOADING=1,p.DONE=2;var y=p.prototype;function v(e){this.fileName="",this.readyState=0,this.result=null,this.length=0,e&&(this.fileName=e.fullPath||e,this.length=e.size||0),this.position=0,this.error=null,this.onwritestart=null,this.onprogress=null,this.onwrite=null,this.onabort=null,this.onsuccess=null,this.onerror=null,this.onwriteend=null}y.abort=function(){this.result=null,this.readyState!=p.DONE&&this.readyState!=p.EMPTY&&(this.readyState=p.DONE,"function"==typeof this.onabort&&this.onabort(new c("abort",{target:this})),"function"==typeof this.onloadend&&this.onloadend(new c("loadend",{target:this})))},y.readAsText=function(e,t){if(this.fileName="",void 0===e.fullPath?this.fileName=e:this.fileName=e.fullPath,this.readyState==p.LOADING)throw new o(o.INVALID_STATE_ERR);this.readyState=p.LOADING,"function"==typeof this.onloadstart&&this.onloadstart(new c("loadstart",{target:this}));var n=t||"UTF-8",r=this;i.exec(function(e){r.readyState!==p.DONE&&(r.result=e,"function"==typeof r.onload&&r.onload(new c("load",{target:r})),r.readyState=p.DONE,"function"==typeof r.onloadend&&r.onloadend(new c("loadend",{target:r})))},function(e){r.readyState!==p.DONE&&(r.readyState=p.DONE,r.result=null,r.error=new o(e),"function"==typeof r.onerror&&r.onerror(new c("error",{target:r})),"function"==typeof r.onloadend&&r.onloadend(new c("loadend",{target:r})))},"readAsText",[this.fileName,n,e.start,e.end])},y.readAsDataURL=function(e){if(this.fileName="",void 0===e.fullPath?this.fileName=e:this.fileName=e.fullPath,this.readyState==p.LOADING)throw new o(o.INVALID_STATE_ERR);this.readyState=p.LOADING,"function"==typeof this.onloadstart&&this.onloadstart(new c("loadstart",{target:this}));var t=this;i.exec(function(e){t.readyState!==p.DONE&&(t.readyState=p.DONE,t.result=e,"function"==typeof t.onload&&t.onload(new c("load",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new c("loadend",{target:t})))},function(e){t.readyState!==p.DONE&&(t.readyState=p.DONE,t.result=null,t.error=new o(e),"function"==typeof t.onerror&&t.onerror(new c("error",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new c("loadend",{target:t})))},"readAsDataURL",[this.fileName,e.start,e.end])},y.readAsBase64=function(e){if(this.fileName="",void 0===e.fullPath?this.fileName=e:this.fileName=e.fullPath,this.readyState==p.LOADING)throw new o(o.INVALID_STATE_ERR);this.readyState=p.LOADING,"function"==typeof this.onloadstart&&this.onloadstart(new c("loadstart",{target:this}));var t=this;i.exec(function(e){t.readyState!==p.DONE&&(t.readyState=p.DONE,t.result=e,"function"==typeof t.onload&&t.onload(new c("load",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new c("loadend",{target:t})))},function(e){t.readyState!==p.DONE&&(t.readyState=p.DONE,t.result=null,t.error=new o(e),"function"==typeof t.onerror&&t.onerror(new c("error",{target:t})),"function"==typeof t.onloadend&&t.onloadend(new c("loadend",{target:t})))},"readAsBase64",[this.fileName,e.start,e.end])},y.readAsArrayBuffer=function(e){},v.INIT=0,v.WRITING=1,v.DONE=2;var g=v.prototype;function w(e,t){this.name=e||null,this.root=null,t&&(this.root=new f(t.name,t.fullPath,this,t.remoteURL))}function I(e,n,o){var r="function"!=typeof e.complete?function(){}:e.complete,c="function"!=typeof e.success?r:function(t){e.success(t),r(t)},a="function"!=typeof e.fail?r:function(t){e.fail(t),r(t)};callbackID=t.callbackId(c,a),t.exec(i.NATIVEF,n,[callbackID,o()])}g.abort=function(){if(this.readyState===v.DONE||this.readyState===v.INIT)throw new o(r(o.INVALID_STATE_ERR));this.error=new o(r(o.ABORT_ERR)),this.readyState=v.DONE,"function"==typeof this.onabort&&this.onabort(new c("abort",{target:this})),"function"==typeof this.onwriteend&&this.onwriteend(new c("writeend",{target:this}))},g.write=function(e){var t=this;if("string"!=typeof e)throw new o(o.TYPE_MISMATCH_ERR);if(this.readyState===v.WRITING)throw new o(o.INVALID_STATE_ERR);this.readyState=v.WRITING,"function"==typeof t.onwritestart&&t.onwritestart(new c("writestart",{target:t})),i.exec(function(e){t.readyState!==v.DONE&&(t.position+=e,t.length+=e,t.readyState=v.DONE,"function"==typeof t.onwrite&&t.onwrite(new c("write",{target:t})),"function"==typeof t.onsuccess&&t.onsuccess(new c("success",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new c("writeend",{target:t})))},function(e){t.readyState!==v.DONE&&(t.readyState=v.DONE,t.error=new o(e),"function"==typeof t.onerror&&t.onerror(new c("error",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new c("writeend",{target:t})))},"write",[this.fileName,e,this.position])},g.writeAsBinary=function(e){var t=this;if("string"!=typeof e)throw new o(o.TYPE_MISMATCH_ERR);if(this.readyState===v.WRITING)throw new o(o.INVALID_STATE_ERR);this.readyState=v.WRITING,"function"==typeof t.onwritestart&&t.onwritestart(new c("writestart",{target:t})),i.exec(function(e){t.readyState!==v.DONE&&(t.position+=e,t.length+=e,t.readyState=v.DONE,"function"==typeof t.onwrite&&t.onwrite(new c("write",{target:t})),"function"==typeof t.onsuccess&&t.onsuccess(new c("success",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new c("writeend",{target:t})))},function(e){t.readyState!==v.DONE&&(t.readyState=v.DONE,t.error=new o(e),"function"==typeof t.onerror&&t.onerror(new c("error",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new c("writeend",{target:t})))},"writeAsBinary",[this.fileName,e,this.position])},g.seek=function(e){if(this.readyState===v.WRITING)throw new o(o.INVALID_STATE_ERR);(e||0===e)&&(e<0?this.position=Math.max(e+this.length,0):e>this.length?this.position=this.length:this.position=e)},g.truncate=function(e){if(this.readyState===v.WRITING)throw new o(o.INVALID_STATE_ERR);this.readyState=v.WRITING;var t=this;"function"==typeof t.onwritestart&&t.onwritestart(new c("writestart",{target:this})),i.exec(function(e){t.readyState!==v.DONE&&(t.readyState=v.DONE,t.length=e,t.position=Math.min(t.position,e),"function"==typeof t.onwrite&&t.onwrite(new c("write",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new c("writeend",{target:t})))},function(e){t.readyState!==v.DONE&&(t.readyState=v.DONE,t.error=new o(e),"function"==typeof t.onerror&&t.onerror(new c("error",{target:t})),"function"==typeof t.onwriteend&&t.onwriteend(new c("writeend",{target:t})))},"truncate",[this.fileName,e,this.position])},e.io={FileSystem:w,DirectoryEntry:f,DirectoryReader:h,FileReader:p,FileWriter:v,requestFileSystem:function(e,t,c){var a=function(e){"function"==typeof c&&c(new o(e))};if(e<1||e>4)a(r(o.SYNTAX_ERR));else{var s=n[e],u=function(i){i?"function"==typeof t&&(s||(s=new w(i.name,i.root),n[e]=s),t(s)):a(r(o.NOT_FOUND_ERR))};s?window.setTimeout(u(s),0):i.exec(u,a,"requestFileSystem",[e])}},resolveLocalFileSystemURL:function(e,t,c){var a=function(e){c&&c(new o(e))};"string"==typeof e?i.exec(function(e){var i;if(e){if(t){var c=n[e.type];c||(c=new w(e.fsName,e.fsRoot),n[e.type]=c),i=e.isDirectory?new f(e.name,e.fullPath,c,e.remoteURL):new _(e.name,e.fullPath,c,e.remoteURL),t(i)}}else a(r(o.NOT_FOUND_ERR))},a,"resolveLocalFileSystemURL",[e]):setTimeout(function(){a(r(o.ENCODING_ERR))},0)},convertLocalFileSystemURL:function(e){return t.execSync(i.NATIVEF,"convertLocalFileSystemURL",[e])},convertAbsoluteFileSystem:function(e){return t.execSync(i.NATIVEF,"convertAbsoluteFileSystem",[e])},getImageInfo:function(e){var n="function"!=typeof e.complete?function(){}:e.complete,o="function"!=typeof e.success?n:function(t){e.success(t),n(t)},r="function"!=typeof e.fail?n:function(t){e.fail(t),n(t)},c="string"!=typeof e.savePath?"":e.savePath;callbackID=t.callbackId(o,r),t.exec(i.NATIVEF,"getImageInfo",[callbackID,e.src,c])},getFileInfo:function(e){I(e,"getFileInfo",function(){return{filePath:e.filePath,digestAlgorithm:e.digestAlgorithm}})},getAudioInfo:function(e){I(e,"getAudioInfo",function(){return{filePath:e.filePath}})},getVideoInfo:function(e){I(e,"getVideoInfo",function(){return{filePath:e.filePath}})},chooseFile:function(e,n,o){var r="function"!=typeof n?null:function(e){n(e)},c="function"!=typeof o?null:function(e){o(e)};callbackID=t.callbackId(r,c),t.exec(i.NATIVEF,"chooseFile",[callbackID,e])},PRIVATE_WWW:1,PRIVATE_DOC:2,PUBLIC_DOCUMENTS:3,PUBLIC_DOWNLOADS:4}}(window.plus),function(e){var t=(e=e).bridge,n=e.tools,i="Maps",o="createObject",r="updateObject",c="updateObjectSYNC",a="execMethod",s={callback:[],pushCallback:function(e,t,n){this.callback[e]={fun:t,nokeep:n}},execCallback:function(e,t){this.callback[e]&&(this.callback[e].fun&&this.callback[e].fun(e,t),this.callback[e].nokeep&&delete this.callback[e])}},u={};function l(e,r,c,a){return n.ANDROID==n.platform?t.exec(i,o,[n.stringify([e,r,c,a])],null):t.exec(i,o,[e,r,c,a],null)}function _(e,o,c){return n.ANDROID==n.platform?t.exec(i,r,[n.stringify([e,[o,c]])],null):t.exec(i,r,[e,[o,c]],null)}function f(e,o,r){return n.ANDROID==n.platform?t.exec(i,a,[n.stringify([e,[o,r]])],null):t.exec(i,a,[e,[o,r]],null)}function d(t,o,r,c,a){o=function(e){var t={zoom:12,type:"MAPTYPE_NORMAL",traffic:!1,zoomControls:!1};return e&&e.center instanceof g&&(t.center=e.center),e&&"number"==typeof e.zoom&&e.zoom<=22&&e.zoom>=1&&(t.zoom=e.zoom),!e||"MAPTYPE_NORMAL"!=e.type&&"MAPTYPE_SATELLITE"!=e.type||(t.type=e.type),e&&"boolean"==typeof e.traffic&&(t.traffic=e.traffic),e&&"boolean"==typeof e.zoomControls&&(t.zoomControls=e.zoomControls),e&&"string"==typeof e.position&&(t.position=e.position),e&&(t.top=e.top,t.left=e.left,t.width=e.width,t.height=e.height),t}(o);var f=this;if(this.IDENTITY=i,this._UUID_=a||n.UUID("map"),this._map_id_=r,this._ui_div_id_=t,this.__showUserLocationVisable__=!1,this.center=o.center?o.center:new e.maps.Point(116.39716,39.91669),this.zoom=o.zoom,this.userLocation=null,this.mapType=o.type,this.zoomControlsVisable=o.zoomControls,this.trafficVisable=o.traffic,this.visable=!0,this.onclick=function(e){},this.onstatuschanged=function(){},s.pushCallback(this._UUID_,function(e,t){if("click"==t.callbackType)f.onclick&&f.onclick(t.payload);else if("change"==t.callbackType&&(f&&(f.zoom=t.zoom),f.onstatuschanged)){var n={};n.target=f,n.zoom=t.zoom,n.center=new g(t.center.long,t.center.lat),t.northease&&(n.bounds=new v(new g(t.northease.long,t.northease.lat),new g(t.southwest.long,t.southwest.lat))),f.onstatuschanged(n)}}),!c){var d=document.getElementById(this._ui_div_id_),h=[o];d&&(e.tools.platform==e.tools.ANDROID?document.addEventListener("plusorientationchange",function(){setTimeout(function(){var e=[d.offsetLeft,d.offsetTop,d.offsetWidth,d.offsetHeight];_(f._UUID_,"resize",e)},200)},!1):d.addEventListener("resize",function(){var e=[d.offsetLeft,d.offsetTop,d.offsetWidth,d.offsetHeight];_(f._UUID_,"resize",e)},!1),h=[o,d.offsetLeft,d.offsetTop,d.offsetWidth,d.offsetHeight]),u[this._UUID_]=l(this._UUID_,"mapview",h,this._map_id_)}}var h=d.prototype;function p(e){this._UUID_=n.UUID("Bubble"),this.label="string"==typeof e?e:"",this.icon=null,this.marker=null,this.__contentImage=null,this.__contentImageAsDataURL=null,this.onclick=function(e){}}h.close=function(){f("map","close",this._UUID_),u[this._UUID_]&&delete u[this._UUID_]},h.centerAndZoom=function(e,t){if(e instanceof g&&"number"==typeof t){this.center=e,this.zoom=t;var n=[e,t];_(this._UUID_,"centerAndZoom",n)}},h.setCenter=function(e){if(e instanceof g){this.center=e;var t=[e];_(this._UUID_,"setCenter",t)}},h.getCenter=function(){return this.center},h.setZoom=function(e){"number"==typeof e&&(this.zoom=e,_(this._UUID_,"setZoom",[e]))},h.resize=function(){var e=document.getElementById(this._ui_div_id_),t=[null];e&&(t=[e.offsetLeft,e.offsetTop,e.offsetWidth,e.offsetHeight]),_(this._UUID_,"resize",t)},h.getZoom=function(){return this.zoom},h.setMapType=function(e){"MAPTYPE_NORMAL"!=e&&"MAPTYPE_SATELLITE"!=e||(this.mapType=e,_(this._UUID_,"setMapType",[e]))},h.getMapType=function(){return this.mapType},h.showUserLocation=function(e){if("boolean"==typeof e&&this.__showUserLocationVisable__!=e){this.__showUserLocationVisable__=e;var t=[e];_(this._UUID_,"showUserLocation",t)}},h.isShowUserLocation=function(){return this.__showUserLocationVisable__},h.getUserLocation=function(e){if("function"==typeof e){var t=n.UUID("callback");s.pushCallback(t,function(t,n){e&&e(n.state,n.point)},!0);var i=[t,window.__HtMl_Id__];return _(this._UUID_,"getUserLocation",i),!0}return!1},h.getCurrentCenter=function(e){if("function"==typeof e){function n(t,n){e&&e(n.state,n.point)}var i=t.callbackId(n);s.pushCallback(i,n,!0);var o=[i,window.__HtMl_Id__];return _(this._UUID_,"getCurrentCenter",o),!0}return!1},h.setTraffic=function(e){if("boolean"==typeof e&&e!=this.trafficVisable){this.trafficVisable=e;var t=[e];_(this._UUID_,"setTraffic",t)}},h.isTraffic=function(){return this.trafficVisable},h.showZoomControls=function(e){if("boolean"==typeof e&&e!=this.zoomControlsVisable){this.zoomControlsVisable=e;var t=[e];_(this._UUID_,"showZoomControls",t)}},h.isShowZoomControls=function(){return this.zoomControlsVisable},h.getBounds=function(){var e,o,r,a=(e=this._UUID_,o="getBounds",r=[],n.ANDROID==n.platform?t.execSync(i,c,[n.stringify([e,[o,r]])],null):t.execSync(i,c,[e,[o,r]],null));return new v(new g(a.northease.longitude,a.northease.latitude),new g(a.southwest.longitude,a.southwest.latitude))},h.reset=function(){_(this._UUID_,"reset",[null])},h.show=function(){if(1!=this.visable){this.visable=!0;var e=document.getElementById(this._ui_div_id_),t=[null];e&&(e.style.display="",t=[e.offsetLeft,e.offsetTop,e.offsetWidth,e.offsetHeight]),_(this._UUID_,"show",t)}},h.hide=function(){0!=this.visable&&(this.visable=!1,document.getElementById(this._ui_div_id_)&&(document.getElementById(this._ui_div_id_).style.display="none"),_(this._UUID_,"hide",[null]))},h.addOverlay=function(e){if(e instanceof D||e instanceof T||e instanceof E||e instanceof N||e instanceof m){var t=[e._UUID_];return _(this._UUID_,"addOverlay",t),!0}return!1},h.removeOverlay=function(e){if(e instanceof D||e instanceof T||e instanceof E||e instanceof N||e instanceof m){var t=[e._UUID_];return _(this._UUID_,"removeOverlay",t),!0}return!1},h.clearOverlays=function(){_(this._UUID_,"clearOverlays",[null])},d.calculateDistance=function(e,n,o,r){var c=t.callbackId(function(e){"function"==typeof o&&o({distance:e})},function(e){"function"==typeof r&&r(e)});t.exec(i,"calculateDistance",[e,n,c])},d.calculateArea=function(e,n,o){var r=t.callbackId(function(e){"function"==typeof n&&n({area:e})},function(e){"function"==typeof o&&o(e)});t.exec(i,"calculateArea",[e,r])},d.convertCoordinates=function(e,n,o,r){var c=t.callbackId(function(e){if("function"==typeof o){var t={};t.coord=new g(e.long,e.lat),t.coordType=e.type,o(t)}},function(e){"function"==typeof r&&r(e)});t.exec(i,"convertCoordinates",[e,n,c])},d.geocode=function(e,n,o,r){var c=t.callbackId(function(e){if("function"==typeof o){var t={};t.coord=new g(e.long,e.lat),t.address=e.addr,t.coordType=e.type,o(t)}},function(e){"function"==typeof r&&r(e)});t.exec(i,"geocode",[e,n,c])},d.reverseGeocode=function(e,n,o,r){var c=t.callbackId(function(e){if("function"==typeof o){var t={};t.coord=new g(e.long,e.lat),t.address=e.addr,t.coordType=e.type,o(t)}},function(e){"function"==typeof r&&r(e)});t.exec(i,"reverseGeocode",[e,n,c])};var y=p.prototype;function v(e,t,n,i){"number"==typeof e&&"number"==typeof t&&"number"==typeof n&&"number"==typeof i?(this.northease=new g(e,t),this.northeast=new g(e,t),this.southwest=new g(n,i)):e instanceof g&&t instanceof g&&(this.northease=e,this.northeast=e,this.southwest=t)}function g(e,t){this.longitude=e,this.latitude=t}y.setIcon=function(e){"string"==typeof e&&(this.icon=e,this.marker&&_(this.marker._UUID_,"setBubbleIcon",[this.icon]))},y.loadImage=function(e){this.__contentImage=e,this.__contentImageAsDataURL=null,this.marker&&_(this.marker._UUID_,"loadImage",[e])},y.loadImageDataURL=function(e){this.__contentImage=null,this.__contentImageAsDataURL=e,this.marker&&_(this.marker._UUID_,"loadImageDataURL",[e])},y.getLabel=function(){return this.label},y.setLabel=function(e){"string"==typeof e&&(this.label=e,this.marker&&_(this.marker._UUID_,"setBubbleLabel",[this.label]))},y.belongMarker=function(){return this.marker},v.prototype.setNorthEase=function(e){e instanceof g&&(this.northease=e,this.northeast=e)},v.prototype.setNorthEast=function(e){e instanceof g&&(this.northeast=e,this.northease=e)},v.prototype.getNorthEase=function(){return this.northease},v.prototype.getNorthEast=function(){return this.northeast},v.prototype.setSouthWest=function(e){e instanceof g&&(this.southwest=e)},v.prototype.getSouthWest=function(){return this.southwest},v.prototype.contains=function(e){return e instanceof g&&(e.longitude<=this.northease.longitude&&e.longitude>=this.southwest.longitude&&e.latitude<=this.northease.latitude&&e.latitude>=this.southwest.latitude)},v.prototype.equals=function(e){return e instanceof v&&(this.northease.equals(e.northease)&&this.southwest.equals(e.southwest))},v.prototype.getCenter=function(){var e=(this.northease.longitude-this.southwest.longitude)/2,t=(this.northease.latitude-this.southwest.latitude)/2;return new g(e+this.southwest.longitude,t+this.southwest.latitude)};var w=g.prototype;function I(){this._UUID_=null,this.visable=!0}w.setLng=function(e){this.longitude=e},w.getLng=function(){return this.longitude},w.setLat=function(e){this.latitude=e},w.getLat=function(){return this.latitude},w.equals=function(e){return this.longitude==e.longitude&&this.latitude==e.latitude};var b=I.prototype;function m(e){var t=this;this._UUID_=n.UUID("marker"),this.point=e,this.icon="",this.caption="",this.bubble=null,this.canDraggable=!1,this.onclick=function(e){},this.onDrag=function(e){},s.pushCallback(this._UUID_,function(e,n){"bubbleclick"==n.type?t.bubble&&t.bubble.onclick&&t.bubble.onclick(t.bubble):"markerclick"==n.type?t.onclick&&t.onclick(t):"onDrag"==n.type&&(t.point=n.pt,t.onDrag(t))}),l(this._UUID_,"marker",[e])}b.show=function(){1!=this.visable&&(this.visable=!0,_(this._UUID_,"show",["true"]))},b.hide=function(){0!=this.visable&&(this.visable=!1,_(this._UUID_,"hide",["false"]))},b.isVisible=function(){return this.visable},b.bringToTop=function(){_(this._UUID_,"bringToTop",[])},m.prototype=new I;var x=m.prototype;function S(){this.strokeColor="#FFFFFF",this.strokeOpacity=1,this.fillColor="#FFFFFF",this.fillOpacity=1,this.lineWidth=5,this.visable=!0}x.constructor=m,x.setPoint=function(e){if(e instanceof g){this.point=e;var t=[e];_(this._UUID_,"setPoint",t)}},x.getPoint=function(){return this.point},x.setIcon=function(e){"string"==typeof e&&(this.icon=e,_(this._UUID_,"setIcon",[e]))},x.setLabel=function(e){"string"==typeof e&&(this.caption=e,_(this._UUID_,"setLabel",[e]))},x.getLabel=function(){return this.caption},x.setBubble=function(e,t){if(e instanceof p){var n=e.marker;if(n&&n!=this){n.bubble=null;var i=[null,null,null,null,!1];_(n._UUID_,"setBubble",i)}e.marker=this,this.bubble=e;i=[this.bubble.label,this.bubble.icon,this.bubble.__contentImageAsDataURL,this.bubble.__contentImage,t];_(this._UUID_,"setBubble",i)}else null==e&&_(this._UUID_,"setBubble",[null,null,null,null,t])},x.hideBubble=function(){this.bubble&&_(this._UUID_,"hideBubble",[])},x.getBubble=function(){return this.bubble},x.setDraggable=function(e){e!=this.canDraggable&&(this.canDraggable=!this.canDraggable,_(this._UUID_,"setDraggable",[this.canDraggable]))},x.isDraggable=function(){return this.canDraggable},x.setIcons=function(e,t){_(this._UUID_,"setIcons",[e,t])},S.prototype=new I;var k=S.prototype;function D(e,t){this.center=e,this.radius=t,this._UUID_=n.UUID("circle"),l(this._UUID_,"circle",[e,t])}k.constructor=S,k.setStrokeColor=function(e){"string"==typeof e&&(this.strokeColor=e,_(this._UUID_,"setStrokeColor",[e]))},k.getStrokeColor=function(){return this.strokeColor},k.setStrokeOpacity=function(e){"number"==typeof e&&(e<0?e=0:e>1&&(e=1),this.strokeOpacity=e,_(this._UUID_,"setStrokeOpacity",[e]))},k.getStrokeOpacity=function(){return this.strokeOpacity},k.setFillColor=function(e){"string"==typeof e&&(this.fillColor=e,_(this._UUID_,"setFillColor",[e]))},k.getFillColor=function(){return this.fillColor},k.setFillOpacity=function(e){"number"==typeof e&&(e<0?e=0:e>1&&(e=1),this.fillOpacity=e,_(this._UUID_,"setFillOpacity",[e]))},k.getFillOpacity=function(){return this.fillOpacity},k.setLineWidth=function(e){"number"==typeof e&&(e<0&&(e=0),this.lineWidth=e,_(this._UUID_,"setLineWidth",[e]))},k.getLineWidth=function(){return this.lineWidth},D.prototype=new S;var U=D.prototype;function T(e){this.path=e,this._UUID_=n.UUID("polygon"),l(this._UUID_,"polygon",[e])}U.constructor=D,U.setCenter=function(e){e instanceof g&&(this.center=e,_(this._UUID_,"setCenter",[e]))},U.getCenter=function(){return this.center},U.setRadius=function(e){"number"==typeof e&&e>=0&&(this.radius=e,_(this._UUID_,"setRadius",[e]))},U.getRadius=function(){return this.radius},T.prototype=new S;var C=T.prototype;function E(e){this.path=e,this._UUID_=n.UUID("polyline"),l(this._UUID_,"polyline",[e])}C.constructor=T,C.setPath=function(e){this.path=e,_(this._UUID_,"setPath",[e])},C.getPath=function(){return this.path},E.prototype=new S;var R=E.prototype;function N(e,t,i){this._UUID_=n.UUID("route"),this.startPoint=e,this.endPoint=t,this.pointCount=0,this.pointList=[],this.distance=0,this.routeTip="",void 0===i&&l(this._UUID_,"route",[e,t,i])}function A(){this.__state__=0,this.__type__=1,this.startPosition=null,this.endPosition=null,this.routeNumber=0,this.routeList=[]}function O(){this.__state__=0,this.__type__=0,this.totalNumber=0,this.currentNumber=0,this.pageNumber=0,this.pageIndex=0,this.poiList=[]}function B(e){var t=this;this._UUID_=n.UUID("search"),this.pageCapacity=10,this.map=e,this.onPoiSearchComplete=function(e,t){},this.onRouteSearchComplete=function(e,t){},s.pushCallback(this._UUID_,function(e,n){0==n.__type__?t.onPoiSearchComplete&&t.onPoiSearchComplete(n.__state__,n):1==n.__type__&&t.onRouteSearchComplete&&t.onRouteSearchComplete(n.__state__,n)}),l(this._UUID_,"search",[null])}R.constructor=E,R.setPath=function(e){this.path=e,_(this._UUID_,"setPath",[e])},R.getPath=function(){return this.path},N.prototype=new I,N.prototype.constructor=N,A.prototype.getRoute=function(e){return e>=0&&e<this.routeNumber?this.routeList[e]:null},O.prototype.getPosition=function(e){return e>=0&&e<this.currentNumber?this.poiList[e]:null};var P=B.prototype;function V(e,t,n,i){return new d(void 0,t,e,n,i)}P.setPageCapacity=function(e){this.pageCapacity=e;var t=[e];_(this._UUID_,"setPageCapacity",t)},P.getPageCapacity=function(){return this.pageCapacity},P.poiSearchInCity=function(e,t,n){if("string"==typeof e&&"string"==typeof t){var i=[e,t,n];return _(this._UUID_,"poiSearchInCity",i),!0}return!1},P.poiSearchNearBy=function(e,t,n,i){if("string"==typeof e&&t instanceof g&&"number"==typeof n){var o=[e,t,n,i];return _(this._UUID_,"poiSearchNearBy",o),!0}return!1},P.poiSearchInbounds=function(e,t,n,i){if("string"==typeof e&&t instanceof g&&n instanceof g){var o=[e,t,n,i];return _(this._UUID_,"poiSearchInbounds",o),!0}return!1},P.setTransitPolicy=function(e){var t=[e];_(this._UUID_,"setTransitPolicy",t)},P.transitSearch=function(e,t,n){if((e instanceof g||"string"==typeof e)&&(t instanceof g||"string"==typeof t)&&"string"==typeof n){var i=[e,t,n];return _(this._UUID_,"transitSearch",i),!0}return!1},P.setDrivingPolicy=function(e){var t=[e];_(this._UUID_,"setDrivingPolicy",t)},P.drivingSearch=function(e,t,n,i){if((e instanceof g||"string"==typeof e)&&(n instanceof g||"string"==typeof n)&&"string"==typeof t&&"string"==typeof i){var o=[e,t,n,i];return _(this._UUID_,"drivingSearch",o),!0}return!1},P.walkingSearch=function(e,t,n,i){if((e instanceof g||"string"==typeof e)&&(n instanceof g||"string"==typeof n)&&"string"==typeof t&&"string"==typeof i){var o=[e,t,n,i];return _(this._UUID_,"walkingSearch",o),!0}return!1},h.setStyles=function(e){n.ANDROID==n.platform?json_map=t.exec(i,"setStyles",[n.stringify([this._UUID_,e])],null):json_map=t.exec(i,"setStyles",[this._UUID_,e],null)},e.maps={Map:d,openSysMap:function(e,t,n){e instanceof g&&n instanceof g&&f("map","openSysMap",[e,t,n])},MapType:{MAPTYPE_SATELLITE:"MAPTYPE_SATELLITE",MAPTYPE_NORMAL:"MAPTYPE_NORMAL"},Marker:m,Bubble:p,Point:g,Bounds:v,Circle:D,Polygon:T,Polyline:E,Position:function(e){this.point=e,this.address="",this.city="",this.name="",this.phone="",this.postcode=""},Route:N,Search:B,SearchPolicy:{TRANSIT_TIME_FIRST:"TRANSIT_TIME_FIRST",TRANSIT_TRANSFER_FIRST:"TRANSIT_TRANSFER_FIRST",TRANSIT_WALK_FIRST:"TRANSIT_WALK_FIRST",TRANSIT_FEE_FIRST:"TRANSIT_FEE_FIRST",DRIVING_TIME_FIRST:"DRIVING_TIME_FIRST",DRIVING_NO_EXPRESSWAY:"DRIVING_NO_EXPRESSWAY",DRIVING_FEE_FIRST:"DRIVING_FEE_FIRST"},__SearchRouteResult__:A,__SearchPoiResult__:O,__bridge__:s,create:V,getMapById:function(e){var o;if(o=n.ANDROID==n.platform?t.execSync(i,"getMapById",[n.stringify([e])],null):t.execSync(i,"getMapById",[e],null)){var r=u[o.uuid];return r||(r=V(e,o.options,"no",o.uuid),u[o.uuid]=r),r}return null}}}(window.plus),function(e){e=e;var t=window.plus.bridge;function n(e){this.__hasPendingOperation__=!1,this.to=[],this.cc=[],this.bcc=[],this.subject="",this.body="",this.bodyType="text",this.silent=!1,this.attachment=[],this.type=e}n.prototype.addAttachment=function(e){"string"==typeof e&&this.attachment.push(e)},e.messaging={createMessage:function(e){return new n(e)},sendMessage:function(e,i,o){if(e instanceof n){var r="function"!=typeof i?null:function(){e.__hasPendingOperation__=!1,i()},c="function"!=typeof o?null:function(t){e.__hasPendingOperation__=!1,o(t)};if(e.__hasPendingOperation__)return void c({code:2,message:"sending"});e.__hasPendingOperation__=!0;var a=t.callbackId(r,c);t.exec("Messaging","sendMessage",[a,e],{cbid:a})}},listenMessage:function(e,n){var i="function"!=typeof e?null:function(t){e(t)},o="function"!=typeof n?null:function(e){n(e)},r=t.callbackId(i,o);t.exec("Messaging","listenMessage",[r],{cbid:r})},TYPE_SMS:1,TYPE_MMS:2,TYPE_EMAIL:3}}(window.plus),function(e){var t=(e=e).bridge,n="UI",i="execMethod",o="syncExecMethod",r={};e.bridge,e.tools;var c={};e.ui={createWaiting:function(t,n){return new e.nativeUI.showWaiting(t,n)},pickTime:function(t,n,i){e.nativeUI.pickTime(t,n,i)},pickDate:function(t,n,i){e.nativeUI.pickDate(t,n,i)},alert:function(t,n,i,o){e.nativeUI.alert(t,n,i,o)},confirm:function(t,n,i,o){e.nativeUI.confirm(t,n,i,o)},prompt:function(t,n,i,o,r){e.nativeUI.prompt(t,n,i,o,r)},toast:function(t,n){e.nativeUI.toast(t,n)},findWindowByName:function(c){var a=t.execSync(n,o,[n,"findWindowByName",[window.__HtMl_Id__,[c]]]);if(a){var s=r[a.uuid];return null==s&&((s=new e.ui.NWindow(null,null,!0)).__uuid__=a.uuid,t.exec(n,i,[n,"setcallbackid",[s.__uuid__,[s.__callback_id__]]])),s}},closeWindow:function(e,t){e&&e.close(t)},createWindow:function(t,n){return new e.ui.NWindow(t,n)},getSelfWindow:function(){var o=r[window.__HtMl_Id__];return null!=o&&void 0!==o||((o=new e.ui.NWindow(null,null,!0)).__uuid__=window.__HtMl_Id__,r[o.__uuid__]=o,t.exec(n,i,[n,"setcallbackid",[o.__uuid__,[o.__callback_id__]]])),o},enumWindow:function(){for(var c=t.execSync(n,o,[n,"enumWindow",[window.__HtMl_Id__]]),a=[],s={},u=0;u<c.length;u++){var l=c[u],_=r[l.uuid];null!=_&&void 0!==_||((_=new e.ui.NWindow(null,null,!0)).__uuid__=l.uuid,t.exec(n,i,[n,"setcallbackid",[_.__uuid__,[_.__callback_id__]]])),a.push(_),s[_.__uuid__]=_}return r=s,a},register:function(e,t){c[e]=t},createView:function(e,o){var r=new c[e](o);return o&&(r.id=o.id),t.exec(n,i,[n,"createView",[window.__HtMl_Id__,[e,r.__uuid__,o,r.__callback_id__]]]),r},exec:function(e,o,r){t.exec(n,i,[e.__IDENTITY__,o,[e.__uuid__,r]])},execSync:function(e,i,r){return t.execSync(n,o,[e.__IDENTITY__,i,[e.__uuid__,r]])},closeSplashscreen:function(){e.navigator.closeSplashscreen()},setFullscreen:function(t){e.navigator.setFullscreen(t)},isFullscreen:function(){return t.execSync(n,o,[n,"isFullScreen",[0]])},__pushWindow__:function(e){r[e.__uuid__]=e},__popWindow__:function(e){delete r[e.__uuid__]},__nviews__:c}}(window.plus),function(e){var t=(e=e).ui,n=e.bridge;function i(t){this.__IDENTITY__=t,this.__uuid__=e.tools.UUID(t),this.id,e.obj.Callback.call(this)}i.prototype.getMetrics=function(e){var i;e&&(i=n.callbackId(function(t){e(t)}),t.exec(this,"getMetrics",[i,window.__HtMl_Id__]))},i.prototype.onCallback=function(e,t,n){e(n)},i.prototype.addEventListener=function(n,i,o){if(e.obj.Callback.prototype.addEventListener.apply(this,[n,i,o])){var r=[n,window.__HtMl_Id__];t.exec(this,"addEventListener",r)}},i.prototype.removeEventListener=function(n,i){if(e.obj.Callback.prototype.removeEventListener.apply(this,[n,i])){var o=[n,window.__HtMl_Id__];t.exec(this,"removeEventListener",o)}},t.NView=i}(window.plus),function(e){var t=(e=e).ui,n="NWindow",i=e.bridge;function o(e,i,o){this.__view_array__=new Array,t.NView.prototype.constructor.apply(this,[n]),o||(t.__pushWindow__(this),t.exec(this,n,[e,i,this.__callback_id__,window.location.href]))}var r=o.prototype;e.tools.extend(r,t.NView.prototype),r.constructor=o,r.show=function(e,n,i){t.exec(this,"show",[e,n,i])},r.close=function(n,i){e.bridge.callbackFromNative(this.__callback_id__,{status:e.bridge.OK,message:{evt:"close"},keepCallback:!0}),t.__popWindow__(this),t.exec(this,"close",[n,i])},r.setOption=function(e){t.exec(this,"setOption",[e])},r.setVisible=function(e){t.exec(this,"setVisible",[e])},r.setPreloadJsFile=function(e){t.exec(this,"setPreloadJsFile",[e])},r.appendPreloadJsFile=function(e){t.exec(this,"appendPreloadJsFile",[e])},r.setContentVisible=function(e){t.exec(this,"setContentVisible",[e])},r.getUrl=function(){return t.execSync(this,"getUrl",[])},r.getTitle=function(){return t.execSync(this,"getTitle",[])},r.getOption=function(){return t.execSync(this,"getOption")},r.load=function(e){t.exec(this,"load",[e,window.location.href])},r.stop=function(){t.exec(this,"stop",[])},r.reload=function(e){t.exec(this,"reload",[e])},r.back=function(){t.exec(this,"back",[])},r.forward=function(){t.exec(this,"forward",[])},r.canBack=function(e){var n;e&&(n=i.callbackId(function(t){e(t)})),t.exec(this,"canBack",[n])},r.canForward=function(e){var n;e&&(n=i.callbackId(function(t){e(t)})),t.exec(this,"canForward",[n])},r.clear=function(){t.exec(this,"clear",[])},r.evalJS=function(e){t.exec(this,"evalJS",[e])},r.append=function(e){this.__view_array__.push(e),t.exec(this,"append",[e.__IDENTITY__,e.__uuid__])},r.setPullToRefresh=function(n,i){var o;i&&(o=e.bridge.callbackId(i)),this.addEventListener("pulldownrefreshevent",i,!1),t.exec(this,"setPullToRefresh",[n,o])},r.endPullToRefresh=function(){t.exec(this,"endPullToRefresh",[])},r.findViewById=function(n){for(var i=this.__view_array__.length-1;i>=0;i--){var o=this.__view_array__[i];if(n==o.id)return o}var r=t.execSync(this,"findViewById",[n]),c=r.identity,a=r.option,s=(r.uuid,new e.ui.__nviews__[c](a));return s.__uuid__=r.uuid,this.__view_array__.push(s),s},t.NWindow=o}(window.plus),function(e){var t=e.ui;e=e;function n(){t.NView.prototype.constructor.apply(this,["Navigator"]),e.obj.Callback.prototype.constructor.apply(this)}t.register("Navigator",n);var i=n.prototype;i.constructor=n,e.tools.extend(i,t.NView.prototype),i.setLeft=function(e){t.exec(this,"setLeft",[e])},i.setRight=function(e){t.exec(this,"setRight",[e])},i.setSystemList=function(e){t.exec(this,"setSystemList",[e])},i.setRight=function(e){t.exec(this,"setRight",[e])},i.addNavigationListener=function(n){var i=e.bridge.callbackId(n);t.exec(this,"addNavigationListener",[i])},i.addListItemListener=function(n){var i=e.bridge.callbackId(n);t.exec(this,"addListItemListener",[i])},i.setTitleText=function(e){t.exec(this,"setTitleText",[e])},i.setList=function(e){t.exec(this,"setList",[e])},t.Navigator=n}(window.plus),function(e){var t=(e=e).bridge,n=e.tools,i="Orientation",o=!1,r={},c=[],a=null,s=function(e,t,n,i,o,r){this.alpha=e,this.beta=t,this.gamma=n,this.magneticHeading=i,this.trueHeading=o,this.headingAccuracy=r},u=void 0,l=void 0,_=void 0;function f(){var e=t.callbackId(function(e){u=e.magneticHeading,l=e.trueHeading,_=e.headingAccuracy,function(e){var t=c.slice(0);a=new s(e.alpha,e.beta,e.gamma,u,l,_);for(var n=0,i=t.length;n<i;n++)t[n].win(a)}(e)},function(e){!function(e){for(var t=c.slice(0),n=0,i=t.length;n<i;n++)t[n].fail(e)}(e)});t.exec(i,"start",[e]),o=!0}function d(e,t){return{win:e,fail:t}}function h(e){var n=c.indexOf(e);n>-1&&(c.splice(n,1),0===c.length&&(t.exec(i,"stop",[]),o=!1))}var p={getCurrentOrientation:function(e,t){var n;n=d(function(t){h(n),e(t)},function(e){h(n),t&&t(e)}),c.push(n),o||f()},watchOrientation:function(e,t,i){var s=i&&i.frequency&&("number"==typeof i.frequency||i.frequency instanceof Number)?i.frequency:500,u=n.UUID("watch"),l=d(function(){},function(e){h(l),t&&t(e)});return c.push(l),r[u]={timer:window.setInterval(function(){a&&e(a)},s),listeners:l},o?a&&e(a):f(),u},clearWatch:function(e){e&&r[e]&&(window.clearInterval(r[e].timer),h(r[e].listeners),delete r[e])}};e.orientation=p}(window.plus),function(e){e=e;var t="Payment",n=window.plus.bridge;function i(){function i(e){"object"==typeof e&&(e.errCode=e.code,e.errMsg=e.message||"")}this.id="",this.description="",this.serviceReady=!0,this.installService=function(){n.exec(t,"installService",[this.id])},this.appStoreReceipt=function(e,i,o){return n.execSync(t,"appStoreReceipt",[this.id])},this.finishTransaction=function(e,o,r){if("appleiap"!==this.id){r({errorcode:"-3"})}var c="function"!=typeof o?null:function(e){o(e)},a="function"!=typeof r?null:function(e){i(e),r(e)},s=n.callbackId(c,a);n.exec(t,"finishTransactionRequest",[this.id,s,e])},this.restoreCompletedTransactions=function(e,t,n){this.restoreComplateRequest(e,t,n)},this.restoreComplateRequest=function(e,o,r){if("appleiap"!==this.id){r({errorcode:"-3"})}var c="function"!=typeof o?null:function(e){o(e)},a="function"!=typeof r?null:function(e){i(e),r(e)},s=n.callbackId(c,a);n.exec(t,"restoreComplateRequest",[this.id,s,e])},this.requestProduct=function(e,t,n){this.requestOrder(e,t,n)},this.requestOrder=function(e,o,r){if("appleiap"===this.id){var c="function"!=typeof o?null:function(e){o(e)},a="function"!=typeof r?null:function(e){i(e),r(e)},s=n.callbackId(c,a);n.exec(t,"requestOrder",[this.id,s,e])}else{r({errorcode:"-3"})}},this.isReadyToPay=function(i,o,r){if(e.tools.ANDROID==e.tools.platform){var c="function"!=typeof o?null:function(e){o(e)},a="function"!=typeof r?null:function(e){r(e)},s=n.callbackId(c,a);n.exec(t,"isReadyToPay",[this.id,i,s])}}}var o={Channel:i,getChannels:function(e,i){var r="function"!=typeof e?null:function(t){for(var n=[],i=t.length,r=0;r<i;r++){var c=new o.Channel;c.id=t[r].id,c.description=t[r].description,c.serviceReady=t[r].serviceReady,n[r]=c}e(n)},c="function"!=typeof i?null:function(e){i(e)},a=n.callbackId(r,c);n.exec(t,"getChannels",[a])},request:function(e,o,r,c){var a="function"!=typeof r?null:function(e){r(e)},s="function"!=typeof c?null:function(e){c(e)};if(e instanceof i){var u=n.callbackId(a,s);n.exec(t,"request",[e.id,o,u])}else window.setTimeout(function(){s({code:62e3})},0)}};e.payment=o}(window.plus),__Mkey__Push__=function(){var e=[];return{pushCallback_Push:function(t,n,i){e[t]={fun:n,nokeep:i}},execCallback_Push:function(t,n,i){e[t]&&e[t].fun&&e[t].fun(i)}}}(),function(e){e=e;var t=window.plus.bridge,n=window.plus.tools;function i(){}i.prototype.setPushChannel=function(n){e.tools.ANDROID==e.tools.platform&&t.exec("Push","setPushChannel",[n])},i.prototype.getAllChannels=function(){return e.tools.ANDROID==e.tools.platform?t.execSync("Push","getAllChannels",[]):[]},e.push={getClientInfo:function(){return t.execSync2("Push","getClientInfo",[])},getClientInfoAsync:function(e,n){var i=t.callbackId(function(t){"function"==typeof e&&e(t)},function(e){"function"==typeof n&&n(e)});t.exec("Push","getClientInfoAsync",[i])},createMessage:function(i,o,r){if(n.platform==n.IOS)t.exec("Push","createMessage",[i,o,r]);else{if(r&&r.icon&&(r.icon=e.io.convertLocalFileSystemURL(r.icon)),r&&r.when){var c=r.when;"[object Date]"===Object.prototype.toString.call(c)?r.when=c.getTime():"string"==typeof c&&(isNaN(parseInt(c))||(r.when=parseInt(c)))}i=new function(e,t,n){this.__UUID__=null,this.message=e,this.Payload=t,this.options=n}(i,o,r);var a=t.execSync("Push","createMessage",[i]);i.__UUID__=a}},clear:function(){t.exec("Push","clear",[])},addEventListener:function(e,i,o){var r=n.UUID(e);__Mkey__Push__.pushCallback_Push(r,i,o),t.exec("Push","addEventListener",[window.__HtMl_Id__,r,e])},remove:function(e){t.exec("Push","remove",[e.__UUID__])},getAllMessage:function(){return t.execSync("Push","getAllMessage",[])},setAutoNotification:function(e){t.exec("Push","setAutoNotification",[e])},getChannelManager:function(){return new i}}}(window.plus),function(e){e=e;var t=window.plus.bridge;e.runtime={arguments:null,version:null,innerVersion:null,uniVersion:"",launchLoadedTime:null,launcher:null,origin:null,processId:null,startupTime:null,isRecovery:!1,restart:function(){t.exec("Runtime","restart",[])},install:function(e,n,i,o){var r=t.callbackId(i,o);t.exec("Runtime","install",[e,r,n])},getProperty:function(e,n){var i=t.callbackId(n);t.exec("Runtime","getProperty",[e,i])},quit:function(){t.exec("Runtime","quit",[])},openURL:function(e,n,i){var o=t.callbackId(null,function(e){"function"==typeof n&&n(e)});t.exec("Runtime","openURL",[e,o,i])},launchApplication:function(e,n){var i=t.callbackId(null,function(e){"function"==typeof n&&n(e)});t.exec("Runtime","launchApplication",[e,i])},setBadgeNumber:function(e,n){"number"==typeof e&&t.exec("Runtime","setBadgeNumber",[e,n])},openFile:function(e,n,i){var o=t.callbackId(null,function(e){"function"==typeof i&&i(e)});t.exec("Runtime","openFile",[e,n,o])},openDocument:function(e,n,i,o){var r=t.callbackId(function(e){"function"==typeof i&&i(e)},function(e){"function"==typeof o&&o(e)});t.exec("Runtime","openDocument",[e,n,r])},showPrivacyDialog:function(t){e.tools.ANDROID==e.tools.platform&&this.execCallback(t,"showPrivacyDialog")},execCallback:function(e,n){var i="function"!=typeof e.complete?function(){}:e.complete,o="function"!=typeof e.success?i:function(t){e.success(t),i(t)},r="function"!=typeof e.fail?i:function(t){e.fail(t),i(t)};callbackID=t.callbackId(o,r),t.exec("Runtime",n,[callbackID,e])},isStreamValid:function(){return t.execSync("Runtime","isStreamValid")},openWeb:function(e){return t.exec("Runtime","openWeb",[e])},isApplicationExist:function(e){return null!==e&&void 0!==e&&"string"!=typeof e&&t.execSync("Runtime","isApplicationExist",[e])},processDirectPage:function(){return t.execSync("Runtime","processDirectPage",[])},isCustomLaunchPath:function(){return t.execSync("Runtime","isCustomLaunchPath")},getDCloudId:function(){return t.execSync("Runtime","getDCloudId")},agreePrivacy:function(){t.exec("Runtime","agreePrivacy",[])},disagreePrivacy:function(){t.exec("Runtime","disagreePrivacy",[])},isAgreePrivacy:function(){return t.execSync("Runtime","isAgreePrivacy",[])},downloadFile:function(e,n,i){if("string"==typeof e.url)if(e.url.length>10&&"blob:file:"==e.url.substring(0,10)){var o=new XMLHttpRequest;o.open("GET",e.url,!0),o.setRequestHeader("Content-type","application/x-www-form-urlencoded"),o.responseType="blob",o.onload=function(o){if(200==this.status){var r=this.response;try{const o=new FileReader;o.onload=function(o){var r=t.callbackId(n,i);t.exec("Runtime","downloadBlob",[o.target.result,e.fileName,r])},o.readAsDataURL(r),o.onerror=function(){i({code:-1,message:"blob error"})}}catch(e){i(c={code:-1,message:e})}}else{var c={code:-1,message:this.status};i(c)}},o.onerror=function(e){i({code:-1,message:"data error"})},o.send()}else{var r=document.createElement("a");r.href=e.url+"&DCLOUD_DOWNLOAD_BLOB:"+e.fileName,r.download=e.fileName,r.click()}else try{const o=new FileReader;o.onload=function(o){var r=t.callbackId(n,i);t.exec("Runtime","downloadBlob",[o.target.result,e.fileName,r])},o.readAsDataURL(e.url),o.onerror=function(){i({code:-1,message:"blob error"})}}catch(e){i({code:-1,message:e})}}}}(window.plus),function(e){var t=(e=e).bridge,n=e.tools,i="Share",o={};function r(e,t,n,i){this.id=e,this.description=t,this.authenticated=n,this.accessToken=i,this.nativeClient=!1}var c=r.prototype;function a(o,r){var c=this;c.__UUID__=n.UUID("Authorize"),c.__componentid__=o,c.display=r,c.onloaded=null,c.onauthenticated=null,c.onerror=null,c.__top__=0,c.__left__=0,c.__width__=0,c.__height__=0;var a=document.getElementById(c.__componentid__);a&&(c.__left__=a.offsetLeft,c.__top__=a.offsetTop,c.__width__=a.offsetWidth,c.__height__=a.offsetHeight);var s=function(e){"function"==typeof c.onerror&&c.onerror(e)},u=t.callbackId(function(t){"load"==t.evt?"function"==typeof c.onloaded&&c.onloaded():"auth"==t.evt&&"function"==typeof c.onauthenticated&&e.share.getServices(function(e){for(var n=0;n<e.length;n++){var i=e[n];if(i.id==t.type){i.authenticated=t.authenticated,i.accessToken=t.accessToken,c.onauthenticated(i);break}}},function(e){s(e)})},s);t.exec(i,"create",[c.__UUID__,u,c.display,c.__left__,c.__top__,c.__width__,c.__height__])}c.authorize=function(e,n,o){var r=this,c="function"!=typeof e?null:function(t){r.authenticated=t.authenticated,r.accessToken=t.accessToken,e(r)},a="function"!=typeof n?null:function(e){n(e)},s=t.callbackId(c,a);t.exec(i,"authorize",[s,this.id,o])},c.forbid=function(){this.authenticated=!1,this.accessToken=null,t.exec(i,"forbid",[this.id])},c.send=function(e,n,o){var r="function"!=typeof n?null:function(e){n()},c="function"!=typeof o?null:function(e){o(e)},a=t.callbackId(r,c);t.exec(i,"send",[a,this.id,e])},c.launchMiniProgram=function(e,n,o){var r="function"!=typeof n?null:function(e){n(e)},c="function"!=typeof o?null:function(e){o(e)},a=t.callbackId(r,c);t.exec(i,"launchMiniProgram",[a,this.id,e])},c.openCustomerServiceChat=function(e,n,o){var r="function"!=typeof n?null:function(e){n(e)},c="function"!=typeof o?null:function(e){o(e)},a=t.callbackId(r,c);t.exec(i,"openCustomerServiceChat",[a,this.id,e])},a.prototype.load=function(e){this.id=e,t.exec(i,"load",[this.__UUID__,e])},a.prototype.setVisible=function(e){t.exec(i,"setVisible",[this.__UUID__,e])};var s={Authorize:a,getServices:function(e,n){var c="function"!=typeof e?null:function(t){for(var n=[],i=0;i<t.length;i++){var c=t[i];if(c.id){var a=o[c.id];a||(a=new r),a.id=c.id,a.description=c.description,a.authenticated=c.authenticated,a.accessToken=c.accessToken,a.nativeClient=c.nativeClient,o[c.id]=a,n.push(a)}}e(n)},a="function"!=typeof n?null:function(e){n(e)},s=t.callbackId(c,a);t.exec(i,"getServices",[s])},sendWithSystem:function(e,n,o){var r="function"!=typeof n?null:function(e){n()},c="function"!=typeof o?null:function(e){o(e)},a=t.callbackId(r,c);t.exec(i,"sendWithSystem",[a,e])}};e.share=s}(window.plus),function(e){e=e;var t=window.plus.bridge,n={startRecognize:function(e,n,i){var o="function"!=typeof n?null:function(e){n(e)},r="function"!=typeof i?null:function(e){i(e)},c=t.callbackId(o,r),a={};if(e.onstart){var s="function"!=typeof e.onstart?null:function(){e.onstart()};a.onstart=t.callbackId(s)}if(e.onend){var u="function"!=typeof e.onend?null:function(){e.onend()};a.onend=t.callbackId(u)}t.exec("Speech","startRecognize",[c,e,a])},stopRecognize:function(){t.exec("Speech","stopRecognize",[])},addEventListener:function(e,n,i){var o;if(n){var r=function(e){"function"==typeof n&&n(e)};n.listener=r,o=t.callbackId(r)}t.exec("Speech","addEventListener",[e,o,window.__HtMl_Id__])}};e.speech=n}(window.plus),function(e){var t=(e=e).bridge,n="Statistic";e.statistic={eventTrig:function(e,i){t.exec(n,"eventTrig",[e,i])},eventStart:function(e,i){t.exec(n,"eventStart",[e,i])},eventEnd:function(e,i){t.exec(n,"eventEnd",[e,i])},eventDuration:function(e,i,o){t.exec(n,"eventDuration",[e,i,o])}}}(window.plus),function(e){e=e;var t=window.plus.bridge;e.storage={getLength:function(){return t.execSync2("Storage","getLength",[null])},getAllKeys:function(){return t.execSync2("Storage","getAllKeys",[null])},getAllKeysAsync:function(e,n){if(e&&"function"==typeof e){var i="function"!=typeof n?null:function(e){n(e)},o=[t.callbackId(e,i)];t.exec("Storage","getAllKeysAsync",o)}},getItem:function(e){return"string"==typeof e&&t.execSync2("Storage","getItem",[e],function(e){var t=e.indexOf(":");return"null"===e.substr(0,t)?null:e.substr(t+1)})},getItemAsync:function(e,n,i){if("string"==typeof e&&n&&"function"==typeof n){var o="function"!=typeof i?null:function(e){i(e)},r=[t.callbackId(n,o),e];t.exec("Storage","getItemAsync",r)}},setItem:function(e,n){return"string"==typeof e&&"string"==typeof n&&t.execSync2("Storage","setItem",[e,n])},setItemAsync:function(e,n,i,o){if("string"==typeof e&&"string"==typeof n&&i&&"function"==typeof i){var r="function"!=typeof o?null:function(e){o(e)},c=[t.callbackId(i,r),e,n];t.exec("Storage","setItemAsync",c)}},removeItem:function(e){return"string"==typeof e&&t.execSync2("Storage","removeItem",[e])},removeItemAsync:function(e,n,i){if("string"==typeof e&&n&&"function"==typeof n){var o="function"!=typeof i?null:function(e){i(e)},r=[t.callbackId(n,o),e];t.exec("Storage","removeItemAsync",r)}},clear:function(){return t.execSync2("Storage","clear",[null])},clearAsync:function(e,n){if(e&&"function"==typeof e){var i="function"!=typeof n?null:function(e){n(e)},o=[t.callbackId(e,i)];t.exec("Storage","clearAsync",o)}},key:function(e){return"number"==typeof e&&t.execSync2("Storage","key",[e])}}}(window.plus),function(e){e=e;var t=window.plus.bridge,n=window.plus.tools,i={UUID:function(){return n.UUID("uploader")},server:"Uploader",getValue:function(e,t){return void 0===e?t:e}};function o(e,t,n){this.type=i.getValue(e,""),this.handles=[],this.capture=i.getValue(n,!1),"function"==typeof t&&this.handles.push(t)}function r(e,t,n){this.__UUID__=i.UUID(),this.url=i.getValue(e,""),this.options=t||{},this.uploadedSize=0,this.totalSize=0,this.responseText="",this.method=i.getValue(this.options.method,"GET"),this.chunkSize=i.getValue(this.options.chunkSize,0),this.timeout=i.getValue(this.options.timeout,120),this.retry=i.getValue(this.options.retry,3),this.retryInterval=i.getValue(this.options.retryInterval,30),this.priority=i.getValue(this.options.priority,1),this.onCompleted=n||null,this.eventHandlers={},this.__requestHeaders__={},this.__responseHeaders__={},this.__noParseResponseHeader__=null,this.__cacheReponseHeaders__={}}o.prototype.fire=function(e){for(var t=0;t<this.handles.length;++t)this.handles[t].apply(this,arguments)},r.prototype.addFile=function(e,n){return"string"==typeof e&&"object"==typeof n&&(t.exec(i.server,"addFile",[this.__UUID__,e,n]),!0)},r.prototype.addData=function(e,n){return"string"==typeof e&&"string"==typeof n&&(t.exec(i.server,"addData",[this.__UUID__,e,n]),!0)},r.prototype.getAllResponseHeaders=function(){if(this.__noParseResponseHeader__)return this.__noParseResponseHeader__;var e="";for(var t in this.__responseHeaders__)e=e+t+" : "+this.__responseHeaders__[t]+"\r\n";return this.__noParseResponseHeader__=e,this.__noParseResponseHeader__},r.prototype.getResponseHeader=function(e){if("string"==typeof e){var t=null;if(e=e.toLowerCase(),t=this.__cacheReponseHeaders__[e])return t;for(var n in this.__responseHeaders__){var i=this.__responseHeaders__[n];e===(n=n.toLowerCase())&&(t=t?t+", "+i:i)}return this.__cacheReponseHeaders__[e]=t,t}return""},r.prototype.setRequestHeader=function(e,t){if("string"==typeof e&&"string"==typeof t){var n=this.__requestHeaders__[e];this.__requestHeaders__[e]=n?n+", "+t:t}},r.prototype.start=function(){t.exec(i.server,"start",[this.__UUID__,this.__requestHeaders__])},r.prototype.pause=function(){t.exec(i.server,"pause",[this.__UUID__])},r.prototype.resume=function(){t.exec(i.server,"resume",[this.__UUID__])},r.prototype.abort=function(){t.exec(i.server,"abort",[this.__UUID__])},r.prototype.addEventListener=function(e,t,n){if("string"==typeof e&&"function"==typeof t){var i=e.toLowerCase();void 0===this.eventHandlers[i]?this.eventHandlers[i]=new o(e,t,n):this.eventHandlers[i].handles.push(t)}},r.prototype.__handlerEvt__=function(e){var t=this;t.state=i.getValue(e.state,t.state),t.uploadedSize=i.getValue(e.uploadedSize,t.uploadedSize),t.totalSize=i.getValue(e.totalSize,t.totalSize),t.__responseHeaders__=i.getValue(e.headers,{}),4==t.state&&"function"==typeof t.onCompleted&&(t.responseText=i.getValue(e.responseText,t.responseText),t.onCompleted(t,e.status||null));var n=this.eventHandlers.statechanged;n&&n.fire(t,void 0===e.status?null:e.status)},e.uploader={__taskList__:{},createUpload:function(e,n,o){if("string"==typeof e){var c=new r(e,n,o);return this.__taskList__[c.__UUID__]=c,t.exec(i.server,"createUpload",[c]),c}return null},enumerate:function(e,n){var o=this.__taskList__,c=t.callbackId(function(t){for(var n=0;n<t.length;n++){var c=t[n];if(c&&c.uuid){var a=o[c.uuid];a||((a=new r).__UUID__=c.uuid,o[a.__UUID__]=a),a.state=i.getValue(c.state,a.state),a.options=i.getValue(c.options,a.options),a.url=i.getValue(c.url,a.url),a.uploadedSize=i.getValue(c.uploadedSize,a.uploadedSize),a.totalSize=i.getValue(c.totalSize,a.totalSize),a.responseText=i.getValue(c.responseText,a.responseText),a.__responseHeaders__=i.getValue(t.headers,a.__responseHeaders__)}}var s=[];for(var u in o)s.push(o[u]);"function"==typeof e&&e(s)});t.exec(i.server,"enumerate",[c])},clear:function(e){var n=4;"number"==typeof e&&(n=e),t.exec(i.server,"clear",[n])},startAll:function(){t.exec(i.server,"startAll",[0])},__handlerEvt__:function(e,t){var n=this.__taskList__[e];n&&n.__handlerEvt__(t)}}}(window.plus),function(e){e=e,window.plus.bridge;e.widget={restart:function(){mkey.exec("SUSF","restart",[])},install:function(e,t,n,i){var o=mkey.helper.callbackid(n,i);mkey.exec("SUSF","install",[e,o,t])},getProperty:function(e,t){var n=mkey.helper.callbackid(t);mkey.exec("SUSF","getProperty",[e,n])}}}(window.plus),function(e){var t=(e=e).bridge,n=e.tools;function i(){}function o(){this.__init__(),this.__UUID__=n.UUID("xhr")}i.Timeout=0,i.Other=1,o.Uninitialized=0,o.Open=1,o.Sent=2,o.Receiving=3,o.Loaded=4,o.__F__="XMLHttpRequest";var r=o.prototype;r.__init__=function(){this.readyState=o.Uninitialized,this.responseText="",this.responseXML=null,this.status=o.Uninitialized,this.statusText=null,this.onreadystatechange,this.responseType=null,this.response=null,this.withCredentials=!0,this.timeout=12e4,this.__noParseResponseHeader__=null,this.__requestHeaders__={},this.__responseHeaders__={},this.__cacheReponseHeaders__={},this.__progessEvent__=new function(e,t,n,i){this.target=e,this.lengthComputable=t,this.loaded=n,this.total=i}(this,!1,0,0),Object.defineProperty(this.__progessEvent__,"target",{enumerable:!1})},r.abort=function(){this.readyState>o.Uninitialized&&("function"==typeof this.onabort&&this.onabort(this.__progessEvent__),this.__init__(),t.exec(o.__F__,"abort",[this.__UUID__]))},r.getAllResponseHeaders=function(){if(this.readyState>=o.Receiving){if(this.__noParseResponseHeader__)return this.__noParseResponseHeader__;var e="";for(var t in this.__responseHeaders__)e=e+t+": "+this.__responseHeaders__[t]+"\r\n";return this.__noParseResponseHeader__=e,this.__noParseResponseHeader__}return null},r.getResponseHeader=function(e){if("string"==typeof e&&this.readyState>=o.Receiving){var t=null;if(e=e.toLowerCase(),t=this.__cacheReponseHeaders__[e])return t;for(var n in this.__responseHeaders__){var i=this.__responseHeaders__[n];e===(n=n.toLowerCase())&&(t=t?t+", "+i:i)}return this.__cacheReponseHeaders__[e]=t,t}return null},r.setRequestHeader=function(e,t){if("string"==typeof e&&"string"==typeof t&&o.Open==this.readyState){var n=this.__requestHeaders__[e];this.__requestHeaders__[e]=n?n+", "+t:t}},r.open=function(e,n,i,r){o.Open!=this.readyState&&o.Loaded!=this.readyState||this.__init__(),o.Uninitialized==this.readyState&&(this.readyState=o.Open,t.exec(o.__F__,"open",[this.__UUID__,e,function(e){return"string"==typeof e?0==(e=e.replace(/(^\s*)|(\s*$)/g,"")).indexOf("http://")||0==e.indexOf("https://")?e:e=0==e.indexOf("/")?location.origin+e:location.origin+location.pathname+e:""}(n),i,r,this.timeout]),"function"==typeof this.onreadystatechange&&this.onreadystatechange())},r.overrideMimeType=function(e){t.exec(o.__F__,"overrideMimeType",[this.__UUID__,e])},r.send=function(e){var n=this;if(o.Open==this.readyState){this.readyState=o.Sent,"function"==typeof this.onloadstart&&this.onloadstart(n.__progessEvent__);var r=t.callbackId(function(e){if(o.Receiving==e.readyState)o.Sent==n.readyState?(n.readyState=o.Receiving,n.status=e.status,n.statusText=e.statusText,n.__responseHeaders__=e.header,n.__progessEvent__.lengthComputable=e.lengthComputable,n.__progessEvent__.total=e.totalSize):o.Receiving==n.readyState&&(n.responseText=e.responseText,n.__progessEvent__.loaded=e.revSize),"function"==typeof n.onreadystatechange&&n.onreadystatechange(),"function"==typeof n.onprogress&&n.onprogress(n.__progessEvent__);else if(o.Loaded==e.readyState){n.readyState=o.Loaded,e.status&&(n.status=e.status);try{if(n.responseText){var t=new DOMParser;n.responseXML=t.parseFromString(n.responseText,"text/xml")}}catch(e){n.responseXML=null}try{if("document"==n.responseType){t=new DOMParser;n.response=n.responseXML}else"json"==n.responseType&&(n.response=JSON.parse(n.responseText))}catch(e){n.response=null}"function"==typeof n.onreadystatechange&&n.onreadystatechange(),e.error==i.Timeout?"function"==typeof n.ontimeout&&n.ontimeout(n.__progessEvent__):e.error==i.Other?"function"==typeof n.onerror&&n.onerror(n.__progessEvent__):"function"==typeof n.onload&&n.onload(n.__progessEvent__),"function"==typeof n.onloadend&&n.onloadend(n.__progessEvent__)}});return t.exec(o.__F__,"send",[this.__UUID__,r,e,this.__requestHeaders__]),void("function"==typeof this.onreadystatechange&&this.onreadystatechange())}throw new Error("XMLHttpRequest not open")},e.net={XMLHttpRequest:o}}(window.plus),function(e){e=e;var t=window.plus.bridge;e.zip={compressVideo:function(e,n,i){var o=t.callbackId(function(e){if(n){var t={tempFilePath:e.tempFilePath,size:e.size};n(t)}},i);t.exec("Zip","compressVideo",[e,o])},decompress:function(e,n,i,o){var r=t.callbackId(i,o);t.exec("Zip","decompress",[e,n,r])},compress:function(e,n,i,o){var r=t.callbackId(i,o);t.exec("Zip","compress",[e,n,r])},compressImage:function(e,n,i){var o=t.callbackId(function(e){if(n){var t={target:e.path,width:e.w,height:e.h,size:e.size};n(t)}},i);t.exec("Zip","compressImage",[e,o])}}}(window.plus),function(e){var t=(e=e).bridge,n=e.tools,i="Proximity",o=!1,r={},c=[];function a(e){var n=c.indexOf(e);n>-1&&(c.splice(n,1),0===c.length&&(t.exec(i,"stop",[]),o=!1))}var s={getCurrentProximity:function(e,o){var r=t.callbackId(function(t){n.IOS==n.platform&&(t=0==t?0:1/0),e&&e(t)},function(e){o&&o(e)});t.exec(i,"getCurrentProximity",[r])},watchProximity:function(e,s){var u,l=n.UUID("watch"),_={win:function(t){n.IOS==n.platform&&(t=0==t?0:1/0),e&&e(t)},fail:function(e){a(_),s&&s(e)}};return c.push(_),r[l]={listeners:_},o||(u=t.callbackId(function(e){for(var t=c.slice(0),n=0,i=t.length;n<i;n++)t[n].win(e)},function(e){for(var t=c.slice(0),n=0,i=t.length;n<i;n++)t[n].fail(e)}),t.exec(i,"start",[u]),o=!0),l},clearWatch:function(e){e&&r[e]&&(a(r[e].listeners),delete r[e])}};e.proximity=s}(window.plus),function(e){e=e;var t=window.plus.bridge,n=window.plus.tools;_Server="Invocation",_importHash=[],_frameObjHash={},_currentFrameObj=null;var i={},o={};i.undefObjectHash={};var r="";function c(e,t){this.type=e,this.value=t}function a(){this.__TYPE__="JSBObject",this.__UUID__=n.UUID("JSBaseObject")}function s(i,o){i=_(i);var r=this;this.__UUID__=n.UUID("JSImplements"),this.callback=o,this.callbackId=t.callbackId(function(t){for(var n=t.arguments,i=[],o=0;o<n.length;o++)i.push(e.ios.__Tool.New(n[o],!0));r.callback[t.method].apply(r,i)},null);var c=[];for(var a in o)c.push(a);var s=t.execSync2(_Server,"implements",[this.__UUID__,i,c,this.callbackId],null,!0);return e.ios.__Tool.New(s,!0)}function u(){this.__Tool=i,this.__JSBBaseObject=a}e.tools.IOS==e.tools.platform?r+="plus.ios.":e.tools.ANDROID==e.tools.platform&&(r+="plus.android."),i.process=function(e){for(var t=[],n=0;n<e.length;n++)t.push(this.warp(e[n]));return t},i.attach=function(e,t){var n=this.undefObjectHash[e];if(n&&t){for(var i=0;i<n.length;i++)n[i].__proto__=t.prototype;delete this.undefObjectHash.className}},i.New=function(e,t){var n=null;if(e){if("object"==e.type){var r=o.isImport(e.className);if(r)n=new r(t);else{if(e.superClassNames)for(var s=0;s<e.superClassNames.length&&!(r=o.isImport(e.superClassNames[s]));s++);if(r)n=new r(t);else{n=new a;var u=this.undefObjectHash[e.className];u||(this.undefObjectHash[e.className]=u=[]),u.push(n)}}return n.className=e.className,n.__UUID__=e.value,n}if("struct"==e.type)return new c(e.type,e.value);if(e.value instanceof Array)for(s=0;s<e.value.length;s++)e.value[s]=i.New(e.value[s],t);return e.value}return null},i.handleClassName=function(e){return e.replace("$",".")},i.saveContent=function(e,n){t.execSync2(_Server,"__saveContent",[e,n],null,!0)},i.warp=function(n){var i={};if(n&&"JSBObject"==n.__TYPE__)i.type="object",i.value=n.__UUID__;else if(n instanceof c){if(i.type=n.type,i.value=n.value,0==n.type.indexOf("@block")){i.type=n.type;var o=t.callbackId(function(t){n.value;for(var i=[],o=0;o<t.length;o++)i.push(e.ios.__Tool.New(t[o],!0));n.value.apply(this,i)});i.value=o,alert(i.value)}}else if(void 0===n||null==n)i.type="null",i.value=n;else if("string"==typeof n||"String"==typeof n)i.type="string",i.value=n;else if("number"==typeof n)i.type="number",i.value=n;else if("boolean"==typeof n)i.type="boolean",i.value=n;else if("function"==typeof n){i.type="block";o=t.callbackId(function(t){for(var i=[],o=0;o<t.length;o++)i.push(e.ios.__Tool.New(t[o],!0));n.apply(this,i)});i.value=o}else e.tools.IOS==e.tools.platform&&"object"==typeof n?(i.type="jsonObject",i.value=n):i=n;return i},c.prototype.constructor=c,a.prototype.plusSetAttribute=function(){var t=null;try{for(var n=[],o=1;o<arguments.length;o++)n.push(i.warp(arguments[o]));t=e.bridge.execSync2(_Server,"__plusSetAttribute",[this.__UUID__,arguments[0],n],null,!0),t=i.New(t,!0)}catch(e){throw e}return t},a.prototype.plusGetAttribute=function(t){var n=null;try{n=e.bridge.execSync2(_Server,"__plusGetAttribute",[this.__UUID__,t],null,!0),n=e.ios.__Tool.New(n,!0)}catch(e){throw e}return n},a.prototype.importClass=function(){return e.android.importClass(this)},a.prototype.plusCallMethod=function(t){var n=null;try{var i="",o=[],r=0;for(var c in t){if("string"!=typeof c)return;var a=t[c];if(0==r){if(i=c,void 0===a){r++;break}i+=":"}else i+=c+":";o.push(a),r++}if(0==r)return;o=window.plus.ios.__Tool.process(o);n=window.plus.bridge.execSync2(_Server,"__exec",[this.__UUID__,i,o],null,!0),n=e.ios.__Tool.New(n,!0)}catch(e){throw e}return n},s.prototype=new a,s.prototype.constructor=s,o.hashTable={},o.importClass=function(n,r){var c=this.isImport(n);if(c)return c;for(var a=this.newClassDefine(n,r),s=t.execSync2(_Server,"import",[n],null,!0),u=s.ClassMethod,l=0;l<u.length;l++)a+=this.AddMethodToClass(n,u[l],!0);var _=s.InstanceMethod;for(l=0;l<_.length;l++)a+=this.AddMethodToClass(n,_[l]);if(e.tools.ANDROID==e.tools.platform){var f=s.ClassConstKeys,d=s.ClassConstValues;for(l=0;l<f.length;l++)a+=this.AddStaticConstToClass(n,f[l],d[l])}return this.hashTable[n]=c=o.createClass(n,a),i.attach(n,c),c},o.isImport=function(e){return o.hashTable[e]},o.newClassDefine=function(e,t){var n,o,c="",a=e;e=i.handleClassName(e),t&&(t=i.handleClassName(t)),o=r+t;for(var s=(n=r+e).split("."),u="window",l=0;l<s.length-1;l++)c+="if(!"+(u=u+"."+s[l])+")",c+=u+"={};";return c+=u+"."+s[s.length-1]+"=",c+="function(nocreate) {            this.__UUID__ = window.plus.tools.UUID('JSB');            this.__TYPE__ = 'JSBObject';            var args = window.plus.ios.__Tool.process(arguments);            if ( nocreate && plus.tools.IOS == plus.tools.platform ) {} else {                window.plus.bridge.execSync2('"+_Server+"', '__Instance',[this.__UUID__, '"+a+"',args],null,true);            }        };",(t=t?o:"plus.ios.__JSBBaseObject")&&(c+=n+".prototype = new "+t+"('__super__constructor__');",c+=n+".prototype.constructor = "+n+";"),c},o.createClass=function(e,t){e=i.handleClassName(e);var n="(function(plus){"+t+"return "+r+e+";})(window.plus);";return window.eval(n)},o.AddStaticConstToClass=function(e,t,n){var o;return e=i.handleClassName(e),n instanceof Array&&0==n.length?(o=r+e+"."+t+"=[];",o+=r+e+".prototype."+t+"=[];"):(o=r+e+"."+t+"="+n+";",o+=r+e+".prototype."+t+"="+n+";"),o},o.AddMethodToClass=function(t,n,o){var c,a=t;t=i.handleClassName(t);var s="";if(e.tools.IOS==e.tools.platform){var u=n.split(":"),l=u.length;if(l>0)for(var _=0;_<l;_++)s+=u[_];else s=u}else s=n;return c=o?r+t+"."+s:r+t+".prototype."+s,c+=" = function (){            var ret = null;            try {                var args = window.plus.ios.__Tool.process(arguments);",c+=o?"ret = window.plus.bridge.execSync2('"+_Server+"', '__execStatic', ['"+a+"', '"+n+"', args],null,true);":"ret = window.plus.bridge.execSync2('"+_Server+"', '__exec', [this.__UUID__, '"+n+"', args],null,true);",c+="ret = plus.ios.__Tool.New(ret, true);            } catch (e) {                throw e;            }            return ret;        };"},u.prototype.requestPermissions=function(e,n,i){if(Array.isArray(e)){var o=t.callbackId(n,i);t.exec(_Server,"requestPermissions",[o,e])}else{i({code:"-1",message:"Request permission must be array type"})}},u.prototype.checkPermission=function(e,n,i){if(Array.isArray(e)){i()}else{var o=t.callbackId(n,i);t.exec(_Server,"checkPermission",[o,e])}},u.prototype.importClass=function(e){var n,i;if((e=_(e)).__TYPE__){if(!e.className)return;i=e.__UUID__,n=e,e=e.className}var r=o.isImport(e);if(r)return r;var c=t.execSync2(_Server,"__inheritList",[e,i],null,!0);if(c){for(var a=c.length,s=a-1;s>=0;s--)r=s==a-1?o.importClass(c[s],null):o.importClass(c[s],c[s+1]);return n&&(n.__proto__=r.prototype),r}return null},u.prototype.invoke=function(t,n){for(var i=null,o=[],r=2;r<arguments.length;r++)o.push(window.plus.ios.__Tool.warp(arguments[r]));if("string"==typeof t)try{i=window.plus.bridge.execSync2(_Server,"__execStatic",[t,n,o],null,!0)}catch(e){throw e}else t&&"JSBObject"==t.__TYPE__?i=window.plus.bridge.execSync2(_Server,"__exec",[t.__UUID__,n,o],null,!0):null==t&&"string"==typeof n&&(i=window.plus.bridge.execSync2(_Server,"__execCFunction",[n,o],null,!0));return i=e.ios.__Tool.New(i,!0)},u.prototype.autoCollection=function(e){e&&"JSBObject"==e.__TYPE__&&window.plus.bridge.execSync2(_Server,"__autoCollection",[e.__UUID__],null,!0)},u.prototype.setAttribute=function(e,t,n){"function"==typeof e||e&&"JSBObject"==e.__TYPE__&&e.plusSetAttribute(t,n)},u.prototype.getAttribute=function(e,t){if("function"==typeof e);else if(e&&"JSBObject"==e.__TYPE__)return e.plusGetAttribute(t);return null},u.prototype.load=function(e){window.plus.bridge.execSync2(_Server,"__loadDylib",[e],null,!0)},u.prototype.newObject=function(t,n){var i=null;if("string"==typeof t){for(var o=[],r=1;r<arguments.length;r++)o.push(window.plus.ios.__Tool.warp(arguments[r]));i=window.plus.bridge.execSync2(_Server,"__newObject",[t,o],null,!0)}return(i=e.ios.__Tool.New(i,!0))||new c(t,n)},u.prototype.currentWebview=function(){if(!_currentFrameObj){var t=window.plus.bridge.execSync2(_Server,"currentWebview",[],null,!0);_currentFrameObj=e.ios.__Tool.New(t,!0)}return _currentFrameObj},u.prototype.getWebviewById=function(t){if(t===window.__HtMl_Id__)return this.currentWebview();var n=_frameObjHash[t];return n||(n=window.plus.bridge.execSync2(_Server,"getWebviewById",[t],null,!0),(n=e.ios.__Tool.New(n,!0))&&(_frameObjHash[t]=n)),n},u.prototype.deleteObject=function(e){t.execSync2(_Server,"__release",[e.__UUID__],null,!0)},u.prototype.implements=function(e,t){return new s(e,t)};var l={"io.dcloud.adapter.":"io.dcloud.common.adapter.","io.dcloud.android.content.":"io.dcloud.feature.internal.reflect.","io.dcloud.app.":"io.dcloud.common.app.","io.dcloud.constant.":"io.dcloud.common.constant.","io.dcloud.core.":"io.dcloud.common.core.","io.dcloud.DHInterface.":"io.dcloud.common.DHInterface.","io.dcloud.net.":"io.dcloud.common.util.net.","io.dcloud.permission.":"io.dcloud.common.core.permission.","io.dcloud.sdk.":"io.dcloud.feature.internal.sdk.","io.dcloud.splash.":"io.dcloud.feature.internal.splash.","io.dcloud.ui.":"io.dcloud.common.core.ui.","io.dcloud.util.":"io.dcloud.common.util."};function _(e){if("string"!=typeof e||n.platform==n.IOS)return e;var t,i;for(var o in l)if(0==e.indexOf(o)){t=o,i=l[o];break}return i&&e?e.replace(t,i):e}e.ios=e.android=new u,e.tools.ANDROID==e.tools.platform&&(e.android.runtimeMainActivity=function(){var t;if(e.android.__runtimeMainActivity__)return e.android.__runtimeMainActivity__;var n=e.bridge.callbackId(function(e){t.onActivityResult&&t.onActivityResult(e[0],e[1],e[2])}),i=(t=e.bridge.execSync2(_Server,"getContext",[n],null,!0)).className;return t.superClassNames=[],t=e.ios.__Tool.New(t,!0),e.android.importClass(i),e.android.__runtimeMainActivity__=t,t})}(window.plus),function(e){var t=(e=e).bridge,n="NativeUI",i=e.bridge,o=e.tools;function r(e){t.execSync(n,"setUiStyle",[e])}function c(e){this.onCancel=null,this.onConfirm=null,this.onChange=null;var o=this,r=i.callbackId(function(){"function"==typeof o.onCancel&&o.onCancel()}),c=i.callbackId(function(e){"function"==typeof o.onConfirm&&o.onConfirm(e)}),a=i.callbackId(function(e){"function"==typeof o.onChange&&o.onChange(e)});t.exec(n,"showPicker",[e,r,c,a])}function a(o,r){this.__uuid__=e.tools.UUID("WaitingView"),this.onclose=null;var c=this,a=i.callbackId(function(){"function"==typeof c.onclose&&c.onclose()});t.exec(n,"WaitingView",[this.__uuid__,[o,r,a]])}function s(n,o,r){var c;this.__uuid__=e.tools.UUID("NativeObj_"),r&&(c=i.callbackId(function(e){var t={};t.index=e,r(t)})),t.exec("NativeUI",n,[window.__HtMl_Id__,[o,c,this.__uuid__]])}c.prototype.update=function(e){t.exec(n,"updatePicker",[e])},a.prototype.close=function(){t.exec(n,"WaitingView_close",[this.__uuid__])},a.prototype.setTitle=function(e){t.exec(n,"WaitingView_setTitle",[this.__uuid__,[e]])},s.prototype.close=function(){t.exec("NativeUI","_NativeObj_close",[this.__uuid__])},e.nativeUI={pickTime:function(e,o,r){if(e&&"function"==typeof e){var c=!1;if("object"==typeof r){var a=r.time;a instanceof Date&&(r.__hours=a.getHours(),r.__minutes=a.getMinutes(),c=!0)}var s="function"!=typeof e?null:function(t){var n=void 0!==t?new Date(t):null,i={};i.date=n,e(i)},u="function"!=typeof o?null:function(e){o(e)},l=i.callbackId(s,u);t.exec(n,"pickTime",[window.__HtMl_Id__,[l,r]]),c&&(delete r.__hours,delete r.__minutes)}},pickDate:function(e,r,c){if(e&&"function"==typeof e){var a={};c&&(c.minDate instanceof Date?(a.startYear=c.minDate.getFullYear(),a.startMonth=c.minDate.getMonth(),a.startDay=c.minDate.getDate()):o.isNumber(c.startYear)&&(a.startYear=c.startYear,a.startMonth=0,a.startDay=1),c.maxDate instanceof Date?(a.endYear=c.maxDate.getFullYear(),a.endMonth=c.maxDate.getMonth(),a.endDay=c.maxDate.getDate()):o.isNumber(c.endYear)&&(a.endYear=c.endYear,a.endMonth=11,a.endDay=31),c.date instanceof Date&&(a.setYear=c.date.getFullYear(),a.setMonth=c.date.getMonth(),a.setDay=c.date.getDate()),a.popover=c.popover,a.title=c.title);var s="function"!=typeof e?null:function(t){var n=void 0!==t?new Date(t):null,i={};i.date=n,e(i)},u="function"!=typeof r?null:function(e){r(e)},l=i.callbackId(s,u);t.exec(n,"pickDate",[window.__HtMl_Id__,[l,a]])}},alert:function(e,o,r,c){var a,s;e&&(s="string"!=typeof e?e.toString():e,o&&(a=i.callbackId(function(e){o(e)})),t.exec(n,"alert",[window.__HtMl_Id__,[s,a,r,c]]))},confirm:function(e,o,r,c){var a,s;e&&(s="string"!=typeof e?e.toString():e,o&&(a=i.callbackId(function(e){var t={};t.index=e,o(t)})),t.exec(n,"confirm",[window.__HtMl_Id__,[s,a,r,c]]))},showWaiting:function(e,t){return new a(e,t)},prompt:function(e,o,r,c,a){var s,u;e&&(s="string"!=typeof e?e.toString():e,o&&(u=i.callbackId(function(e){e.value=e.message,o(e)})),t.exec(n,"prompt",[window.__HtMl_Id__,[s,u,r,c,a]]))},toast:function(e,i){var o;e&&(o="string"!=typeof e?e.toString():e,t.exec(n,"toast",[window.__HtMl_Id__,[o,i]]))},closeToast:function(){t.exec(n,"closeToast",[window.__HtMl_Id__,[]])},showMenu:function(e,o,r){var c;e&&(e.onclick&&(e.__plus__onclickCallbackId=i.callbackId(e.onclick)),r&&(c=i.callbackId(function(e){var t={};t.index=e.index,t.target=o[e.index],r(t)})),t.exec(n,"showMenu",[window.__HtMl_Id__,[e,o,c]]))},hideMenu:function(){t.exec(n,"hideMenu",[window.__HtMl_Id__])},isTitlebarVisible:function(){return t.execSync(n,"isTitlebarVisible",[window.__HtMl_Id__])},setTitlebarVisible:function(e){return t.exec(n,"setTitlebarVisible",[window.__HtMl_Id__,[e]])},getTitlebarHeight:function(){return t.execSync(n,"getTitlebarHeight",[window.__HtMl_Id__])},actionSheet:function(e,t){return new s("actionSheet",e,t)},closeWaiting:function(){i.exec(n,"closeWaiting",[])},setUiStyle:r,setUIStyle:r,previewImage:function(e,t){var o=t,r=i.callbackId(function(e){"function"==typeof o.onLongPress&&o.onLongPress(e)});i.exec(n,"previewImage",[this.__uuid__,[e,t,r]])},closePreviewImage:function(){i.exec(n,"closePreviewImage",[this.__uuid__])},showPicker:function(e){return new c(e)}}}(window.plus),function(e){e=e;var t=window.plus.bridge,n=e.tools,i="Navigator";e.navigator={closeSplashscreen:function(){t.exec(i,"closeSplashscreen",[0])},updateSplashscreen:function(e){t.exec(i,"updateSplashscreen",[e])},isFullscreen:function(){return t.execSync(i,"isFullScreen",[0])},setFullscreen:function(e){t.exec(i,"setFullscreen",[e])},isImmersedStatusbar:function(){return t.execSync(i,"isImmersedStatusbar",[])},getStatusbarHeight:function(e){return this.__statusBarHeight__||(this.__statusBarHeight__=t.execSync(i,"getStatusbarHeight",[])),this.__statusBarHeight__},setStatusBarBackground:function(e){t.exec(i,"setStatusBarBackground",[e])},getStatusBarBackground:function(){return t.execSync(i,"getStatusBarBackground",[])},setStatusBarStyle:function(e){t.exec(i,"setStatusBarStyle",[e])},getStatusBarStyle:function(){return t.execSync(i,"getStatusBarStyle",[])},getUiStyle:function(){return t.execSync(i,"getUiStyle",[])},setUserAgent:function(e,n){t.execSync(i,"setUserAgent",[e,n])},getUserAgent:function(){return t.execSync(i,"getUserAgent",[],function(e){try{return null==window.eval(e)?null:e}catch(t){return e}return e})},removeCookie:function(e){t.exec(i,"removeCookie",[e])},removeSessionCookie:function(e,n){t.exec(i,"removeSessionCookie",[])},removeAllCookie:function(e,n){t.exec(i,"removeAllCookie",[])},setCookie:function(e,n){t.exec(i,"setCookie",[e,n])},getCookie:function(e){return t.execSync(i,"getCookie",[e],function(e){try{return null==window.eval(e)?null:e}catch(t){return e}return e})},setLogs:function(e){t.exec(i,"setLogs",[e])},isLogs:function(){return t.execSync(i,"isLogs",[0])},createShortcut:function(e,n,o){var r=t.callbackId(n,o);t.exec(i,"createShortcut",[e,r])},hasShortcut:function(e,o){if(n.platform!=n.IOS){var r=t.callbackId(o);t.exec(i,"hasShortcut",[e,r])}},checkPermission:function(e){return t.execSync(i,"checkPermission",[e])},requestPermission:function(e,n){var o=t.callbackId(n);return t.exec(i,"requestPermission",[e,o])},isBackground:function(){return t.execSync(i,"isBackground",[])},hasNotchInScreen:function(){return t.execSync(i,"hasNotchInScreen",[])},hasSplashscreen:function(){return t.execSync(i,"hasSplashscreen",[])},hideSystemNavigation:function(){return t.exec(i,"hideSystemNavigation",[])},showSystemNavigation:function(){return t.exec(i,"showSystemNavigation",[])},getSafeAreaInsets:function(){return e.tools.ANDROID==e.tools.platform?{bottom:0,left:0,right:0,top:0,deviceBottom:0,deviceLeft:0,deviceRight:0,deviceTop:0}:t.execSync(i,"getSafeAreaInsets",[])},getOrientation:function(){return t.execSync(i,"getOrientation",[])},isRoot:function(){return e.tools.ANDROID!=e.tools.platform&&t.execSync(i,"isRoot",[])},isSimulator:function(){return t.execSync(i,"isSimulator",[])},getSignature:function(){return t.execSync(i,"getSignature",[])}},e.navigator.getUIStyle=e.navigator.getUiStyle}(window.plus),function(e){e=e;var t=window.plus.bridge,n=e.tools;e.key={},keyEvent={},keyEvent.backbutton="back",keyEvent.menubutton="menu",keyEvent.searchbutton="search",keyEvent.volumeupbutton="volumeup",keyEvent.volumedownbutton="volumedown",keyEvent.keyup="keyup",keyEvent.keydown="keydown",keyEvent.longpressed="longpressed",e.key.addEventListener=function(n,i,o){if(n&&i&&"string"==typeof n&&"function"==typeof i){var r=e.webview.currentWebview();if(e.obj.Callback.prototype.addEventListener.apply(r,[keyEvent[n],function(e){var t={};t.keycode=e.keyType,t.keyCode=e.keyCode,t.keyType=e.keyType,i(t)},o])){var c=[keyEvent[n],window.__HtMl_Id__];t.exec("UI","execMethod",[r.__IDENTITY__,"addEventListener",[r.__uuid__,c]])}}},e.key.removeEventListener=function(n,i){if(n&&i&&"string"==typeof n&&"function"==typeof i){var o=e.webview.currentWebview();if(e.obj.Callback.prototype.removeEventListener.apply(o,[keyEvent[n],i])){var r=[keyEvent[n],window.__HtMl_Id__];o=e.webview.currentWebview();t.exec("UI","execMethod",[o.__IDENTITY__,"removeEventListener",[o.__uuid__,r]])}}},e.key.setAssistantType=function(i){if(n.platform==n.IOS)window.__keyboardAssist.setInputType(i);else{var o=[i,window.__HtMl_Id__],r=e.webview.currentWebview();t.exec("UI","execMethod",[r.__IDENTITY__,"setAssistantType",[r.__uuid__,o]])}},e.key.hideSoftKeybord=function(){if(n.platform==n.IOS)t.exec("Runtime","hideSoftKeybord");else{var i=[window.__HtMl_Id__],o=e.webview.currentWebview();t.exec("UI","execMethod",[o.__IDENTITY__,"hideSoftKeybord",[o.__uuid__,i]])}},e.key.showSoftKeybord=function(){var n=[window.__HtMl_Id__],i=e.webview.currentWebview();t.exec("UI","execMethod",[i.__IDENTITY__,"showSoftKeybord",[i.__uuid__,n]])},e.key.setVolumeButtonEnabled=function(i){if(n.ANDROID==n.platform){var o=[i],r=e.webview.currentWebview();t.exec("UI","execMethod",[r.__IDENTITY__,"setVolumeButtonEnabled",[r.__uuid__,o]])}}}(window.plus),function(e){var t=(e=e).bridge,n="UI",i="execMethod",o="syncExecMethod",r={},c=e.bridge,a=e.tools;function s(){return a.platform!=a.IOS&&t.execSync(n,o,[n,"defaultHardwareAccelerated",[]])}function u(c){if(c&&"string"==typeof c){var a=t.execSync(n,o,[n,"findWindowByName",[window.__HtMl_Id__,[c]]]);if(a){var s=r[a.uuid];return null==s&&((s=new e.webview.Webview(null,null,!0,a.extras)).__uuid__=a.uuid,s.id=a.id,t.exec(n,i,[n,"setcallbackid",[s.__uuid__,[s.__callback_id__]]]),r[s.__uuid__]=s),s}for(var u in r){var l=r[u];if(l&&l.id===c)return l}return null}}e.webview={open:function(t,n,i,o,r,c,a){var s=e.webview.create(t,n,i,a);return s.show(o,r,c),s},show:function(t,n,i,o,r){var c=null;if("string"==typeof t)c=u(t);else{if(!(t instanceof e.webview.Webview))return;c=t}c&&c.show(n,i,o,r)},__test__:function(){"save"==arguments[0]?t.exec(n,i,[n,"debug",[location.href,arguments]]):t.execSync(n,i,[n,"debug",[location.href,arguments]])},hide:function(t,n,i,o){var r=null;if("string"==typeof t)r=u(t);else{if(!(t instanceof e.webview.Webview))return;r=t}r&&r.hide(n,i,o)},createGroup:null,getWapLaunchWebview:function(){if(a.IOS==a.platform)return null;var c=t.execSync(n,o,[n,"getWapLaunchWebview",[]]);if(c){var s=r[c.uuid];return null==s&&((s=new e.webview.Webview(null,null,!0,c.extras)).__uuid__=c.uuid,s.id=c.id,t.exec(n,i,[n,"setcallbackid",[s.__uuid__,[s.__callback_id__]]]),r[s.__uuid__]=s),s}return null},getLaunchWebview:function(){var c=t.execSync2(n,o,[n,"getLaunchWebview",[]]);if(c){var a=r[c.uuid];return null==a&&((a=new e.webview.Webview(null,null,!0,c.extras)).__uuid__=c.uuid,a.id=c.id,t.exec(n,i,[n,"setcallbackid",[a.__uuid__,[a.__callback_id__]]]),r[a.__uuid__]=a),a}},getSecondWebview:function(){var c=t.execSync2(n,o,[n,"getSecondWebview",[]]);if(c){var a=r[c.uuid];return null==a&&((a=new e.webview.Webview(null,null,!0,c.extras)).__uuid__=c.uuid,a.id=c.id,t.exec(n,i,[n,"setcallbackid",[a.__uuid__,[a.__callback_id__]]]),r[a.__uuid__]=a),a}},getWebviewById:u,getTopWebview:function(){var c=t.execSync(n,o,[n,"getTopWebview",[window.__HtMl_Id__]]);if(c){var a=r[c.uuid];return null==a&&((a=new e.webview.Webview(null,null,!0,c.extras)).__uuid__=c.uuid,a.id=c.id,t.exec(n,i,[n,"setcallbackid",[a.__uuid__,[a.__callback_id__]]]),r[a.__uuid__]=a),a}return null},close:function(t,n,i){var o=null;if("string"==typeof t)o=u(t);else{if(!(t instanceof e.webview.Webview))return;o=t}o&&o.close(n,i)},create:function(t,n,i,o){return(i=i||{}).name=n,new e.webview.Webview(t,i,!1,o)},prefetchURL:function(e){t.exec(n,i,[n,"prefetchURL",[window.__HtMl_Id__,[e]]])},prefetchURLs:function(e){t.exec(n,i,[n,"prefetchURLs",[window.__HtMl_Id__,[e]]])},currentWebview:function(){var c=r[window.__HtMl_Id__];if(null==c||void 0===c){var a=t.execSync2(n,o,[n,"currentWebview",[window.__HtMl_Id__]]);a&&((c=new e.webview.Webview(null,null,!0,a.extras)).__uuid__=window.__HtMl_Id__,c.id=a.id,r[c.__uuid__]=c,t.exec(n,i,[n,"setcallbackid",[c.__uuid__,[c.__callback_id__]]]))}return c},postMessageToUniNView:function(e,o){var c=r[window.__HtMl_Id__];t.exec(n,i,[n,"postMessageToUniNView",[c.__uuid__,[e,o]]])},__callNativeModuleSync__:function(e,i){var c=r[window.__HtMl_Id__];return t.execSync2(n,o,[n,"__callNativeModuleSync",[c.__uuid__,[e,i]]])},all:function(){for(var c=t.execSync(n,o,[n,"enumWindow",[window.__HtMl_Id__]]),a=[],s={},u=0;u<c.length;u++){var l=c[u],_=r[l.uuid];_||((_=new e.webview.Webview(null,null,!0,l.extras)).__uuid__=l.uuid,_.id=l.id,t.exec(n,i,[n,"setcallbackid",[_.__uuid__,[_.__callback_id__]]])),a.push(_),s[_.__uuid__]=_}return r=s,a},getDisplayWebview:function(){var c=t.execSync(n,o,[n,"getDisplayWebview",[window.__HtMl_Id__]]),a=[];if(!c)return a;for(var s={},u=0;u<c.length;u++){var l=c[u],_=r[l.uuid];_?_.id||(_.id=l.id):((_=new e.webview.Webview(null,null,!0,l.extras)).__uuid__=l.uuid,_.id=l.id,t.exec(n,i,[n,"setcallbackid",[_.__uuid__,[_.__callback_id__]]])),a.push(_),s[_.__uuid__]=_}return r=s,a},defauleHardwareAccelerated:function(){return s()},defaultHardwareAccelerated:s,exec:function(e,o,r){t.exec(n,i,[e.__IDENTITY__,o,[e.__uuid__,r]])},execSync:function(e,i,r){return t.execSync(n,o,[e.__IDENTITY__,i,[e.__uuid__,r]])},_find__Window_By_UUID__:function(o,c,a){if(o&&"string"==typeof o){var s=r[o];return s||((s=new e.webview.Webview(null,null,!0,a)).__uuid__=o,s.id__=c,s.id=c,t.exec(n,i,[n,"setcallbackid",[s.__uuid__,[s.__callback_id__]]]),r[o]=s),s}},__pushWindow__:function(e){r[e.__uuid__]=e},__popWindow__:function(e){delete r[e.__uuid__]},__JSON_Window_Stack:r,__Webview_LoadEvent_CallBack_:function(e){var t=r[e.WebviewID];t&&("onloading"==e.Event?null!=t.onloading&&t.onloading({target:t}):"onclose"==e.Event?(null!=t.onclose&&t.onclose({target:t}),delete r[e.WebviewID]):"onerror"==e.Event?null!=t.onerror&&t.onerror({target:t}):"onloaded"==e.Event&&null!=t.onloaded&&t.onloaded({target:t}))},startAnimation:function(o,r,a){var s,u;o&&o.view&&o.styles&&o.styles.toLeft&&("string"==typeof o.view||o.view instanceof e.webview.Webview)&&(o.view instanceof e.webview.Webview&&(o.view=o.view.__uuid__),r&&r.view&&("string"==typeof r.view||r.view instanceof e.webview.Webview)&&r.view instanceof e.webview.Webview&&(r.view=r.view.__uuid__),a&&"function"==typeof a&&(s=e.webview.currentWebview().__uuid__,u=c.callbackId(function(t){t.target=e.webview._find__Window_By_UUID__(t.target.uuid),a(t)})),t.exec(n,i,[n,"startAnimation",[window.__HtMl_Id__,[o,r,s,u]]]))}}}(window.plus),function(e){var t=(e=e).webview,n=e.bridge;function i(t){this.__IDENTITY__=t,this.__uuid__=e.tools.UUID(t),this.id,e.obj.Callback.call(this)}i.prototype.getMetrics=function(e){var i;e&&(i=n.callbackId(function(t){var n={};n.canForward=t,e(n)}),t.exec(this,"getMetrics",[i,window.__HtMl_Id__]))},i.prototype.onCallback=function(t,n,i){if("popGesture"==n){var o=i.private_args,r={target:e.webview._find__Window_By_UUID__(o.uuid,o.id,o.extras),type:i.type,progress:i.progress};"result"in i&&(r.result=i.result),i=r}else(i=i||{}).target=this;t(i)},i.prototype.addEventListener=function(n,i,o){if(e.obj.Callback.prototype.addEventListener.apply(this,[n,i,o])){var r=[n,window.__HtMl_Id__];t.exec(this,"addEventListener",r)}},i.prototype.removeEventListener=function(n,i){if(e.obj.Callback.prototype.removeEventListener.apply(this,[n,i])){var o=[n,window.__HtMl_Id__];t.exec(this,"removeEventListener",o)}},t.NView=i}(window.plus),function(e){e=e;var t="WebviewGroup";function n(n,i){e.webview.NView.prototype.constructor.apply(this,[t]),this.__children=[];var o=[];if(n instanceof Array)for(var r=0;r<n.length;r++){var c=n[r];c.styles=c.styles||{},c.styles.name=c.id;var a=new e.webview.Webview(c.url,c.styles,!0,c.extras);e.webview.__pushWindow__(a),this.__children.push(a);var s=[a.__uuid__,[c.url,c.styles,a.__callback_id__,location.host+location.pathname,c.extras]];o.push(s)}e.webview.exec(this,"createGroup",[o,i,this.__callback_id__])}var i=n.prototype;e.tools.extend(i,e.webview.NView.prototype),i.constructor=n,n.prototype.setSelectIndex=function(t){e.webview.exec(this,"setSelectIndex",[t])},n.prototype.children=function(){return this.__children},e.webview.createGroup=function(e,t){return new n(e,t)},e.webview.WebviewGroup=n}(window.plus),function(e){var t=(e=e).webview,n="NWindow",i=e.bridge,o=e.tools;function r(){return window.location.href}function c(e){if(e instanceof Array)for(var t=0;t<e.length;t++)a(e[t])}function a(e){!function(e){if(e.onclick&&"function"==typeof e.onclick){var t=i.callbackId(function(){e.onclick(e)});e.__cb__={id:t,htmlId:window.__HtMl_Id__}}}(e)}function s(e){if(e instanceof Array)for(var t,n=0;n<e.length;n++)(t=e[n])&&"input"==t.tag?t.inputStyles&&(t.inputStyles.onComplete&&"function"==typeof t.inputStyles.onComplete&&(t.inputStyles.__onCompleteCallBackId__=i.callbackId(t.inputStyles.onComplete)),t.inputStyles.onFocus&&"function"==typeof t.inputStyles.onFocus&&(t.inputStyles.__onFocusCallBackId__=i.callbackId(t.inputStyles.onFocus)),t.inputStyles.onBlur&&"function"==typeof t.inputStyles.onBlur&&(t.inputStyles.__onBlurCallBackId__=i.callbackId(t.inputStyles.onBlur))):t&&"richtext"==t.tag&&t.richTextStyles&&t.richTextStyles.onClick&&"function"==typeof t.richTextStyles.onClick&&(t.richTextStyles.__onClickCallBackId__=i.callbackId(t.richTextStyles.onClick))}function u(i,a,u,l){if(this.__view_array__=new Array,t.NView.prototype.constructor.apply(this,[n]),this.id=null,a&&a.name&&(this.id=a.name),l)for(var _ in l)this[_]=l[_];!this.id&&i&&(this.id=a.name=i),o.platform==o.IOS&&a&&a.navigationbar&&(this.__navigationbar__=a.navigationbar);var f=[],d=null;if(a&&(d=a.titleNView||a.navigationbar)&&(d.uuid="_Nav_Bar_"+this.__uuid__,f.push(d.uuid),e.nativeObj.__appendSubViewInfo(d),c(d.buttons),s(d.tags)),a&&a.subNViews&&a.subNViews instanceof Array){for(var h=0;h<a.subNViews.length;h++){var p=a.subNViews[h];s(p.tags),p.uuid=o.UUID("Nativeview"),f.push(p.uuid),e.nativeObj.__appendSubViewInfo(p)}this.__subNViewsUids__=f,this.checkJSObject=!0,setTimeout(function(){this.checkJSObject=!1,e.nativeObj.__removeSubViewInfos(f)})}u||(t.__pushWindow__(this),t.exec(this,n,[i,a,this.__callback_id__,r(),l]))}var l=u.prototype;function _(){window.__needNotifyNative__&&"touchmove"==this.type&&(window.__needNotifyNative__=t.execSync(e.webview.currentWebview(),"needTouchEvent",[])),l.oldPreventDefault.call(this)}e.tools.extend(l,t.NView.prototype),l.constructor=u,l.show=function(e,n,o,r){var c;o&&"function"==typeof o&&(c=i.callbackId(function(){o()})),t.exec(this,"show",[e,n,null,c,r])},l.close=function(n,i,o){this===e.webview.__JSON_Window_Stack[window.__HtMl_Id__]&&e.bridge.callbackFromNative(this.__callback_id__,{status:e.bridge.OK,message:{evt:"close"},keepCallback:!0}),t.exec(this,"close",[n,i,o])},l.setStyle=function(e){if(e&&"object"==typeof e){if(e){var n=e.titleNView||e.navigationbar;n&&(s(n.tags),c(n.buttons))}t.exec(this,"setOption",[e])}},l.updateSubNViews=function(e){if(e instanceof Array)for(var n,i=0;i<e.length;i++)(n=e[i])&&s(n.tags);t.exec(this,"updateSubNViews",[e])},l.nativeInstanceObject=function(){var t=e.ios||e.android;return t&&t.getWebviewById?t.getWebviewById(this.__uuid__):null},l.hide=function(e,n,i){t.exec(this,"hide",[e,n,i])},l.setVisible=function(e){t.exec(this,"setVisible",[e])},l.pause=function(){t.exec(this,"pause",[])},l.resume=function(){t.exec(this,"resume",[])},l.isPause=function(){return t.execSync(this,"isPause",[])},l.isVisible=function(){return t.execSync(this,"isVisible",[])},l.setFixBottom=function(e){t.exec(this,"setFixBottom",[e])},l.getSubNViews=function(){var n=[],i=t.execSync(this,"getSubNViews",[]);if(i&&i instanceof Array&&i.length>0){for(var o=0;o<i.length;o++){var r=e.nativeObj.View.__getViewByUidAndCreate(i[o]);n.push(r)}return n}if(this.checkJSObject&&this.__subNViewsUids__)for(o=0;o<this.__subNViewsUids__.length;o++){(r=e.nativeObj.View.__getViewByUidFromCache(this.__subNViewsUids__[o]))&&n.push(r)}return n},l.getNavigationbar=function(){return this.getTitleNView()},l.setTitleNViewButtonBadge=function(e){t.exec(this,"setTitleNViewButtonBadge",[e])},l.removeTitleNViewButtonBadge=function(e){t.exec(this,"removeTitleNViewButtonBadge",[e])},l.showTitleNViewButtonRedDot=function(e){t.exec(this,"showTitleNViewButtonRedDot",[e])},l.hideTitleNViewButtonRedDot=function(e){t.exec(this,"hideTitleNViewButtonRedDot",[e])},l.setTitleNViewSearchInputFocus=function(e){t.exec(this,"setTitleNViewSearchInputFocus",[e])},l.setTitleNViewSearchInputText=function(e){t.exec(this,"setTitleNViewSearchInputText",[e])},l.getTitleNViewSearchInputText=function(){return t.execSync(this,"getTitleNViewSearchInputText")},l.setTitleNViewButtonStyle=function(e,n){void 0!=e&&n&&(a(n),t.exec(this,"setTitleNViewButtonStyle",[e,n]))},l.getTitleNView=function(){var n;if(o.platform==o.IOS){var i="_Nav_Bar_"+this.__uuid__;!(n=e.nativeObj.View.getViewById(i))&&this.__navigationbar__&&((n=new e.nativeObj.View(i,this.__navigationbar__,"",!0)).__uuid__=i)}else{i=this.__uuid__;if(n=e.nativeObj.View.getViewById(i))!n&&this.__navigationbar__&&((n=new e.nativeObj.View(i,this.__navigationbar__,"",!0)).__uuid__=i);else{var r=t.execSync(this,"getTitleNView",[]);r&&((n=new e.nativeObj.View(r.id,r.styles,!0)).__uuid__=r.uuid)}}return n},window.__needNotifyNative__=!1,l.__needTouchEvent__=function(){Event.prototype.preventDefault!==_&&(l.oldPreventDefault=Event.prototype.preventDefault,Event.prototype.preventDefault=_)},l.drag=function(n,o,r){if(n&&n.direction&&"string"==typeof n.direction&&n.moveMode&&"string"==typeof n.moveMode){var c=n.moveMode.toLocaleLowerCase();if("followfinger"==c||"follow"==c||"silent"==c||"bounce"==c){var a=n.direction.toLocaleLowerCase();if("left"==a||"right"==a||"rtl"==a||"ltr"==a){if("rtl"==a&&(n.direction="left"),"ltr"==a&&(n.direction="right"),o){var s=o.view;if(null!=s&&("string"==typeof s||s instanceof e.webview.Webview||s instanceof e.nativeObj.View)&&("string"!=typeof s&&(o.view=s.__uuid__),o.moveMode)){if("string"!=typeof o.moveMode)return;var u=o.moveMode.toLocaleLowerCase();if("follow"!=u&&"silent"!=u)return;if("silent"==c&&"silent"==u)return}}var l,_;r&&"function"==typeof r&&(l=e.webview.currentWebview().__uuid__,_=i.callbackId(r)),t.exec(this,"drag",[n,o,l,_])}}}},l.setJsFile=function(e,n){e&&"string"==typeof e&&t.exec(this,"setPreloadJsFile",[e,n])},l.appendJsFile=function(e){e&&"string"==typeof e&&t.exec(this,"appendPreloadJsFile",[e])},l.setContentVisible=function(e){t.exec(this,"setContentVisible",[e])},l.opener=function(){var n=t.execSync(this,"opener",[]);return n?e.webview._find__Window_By_UUID__(n.uuid,n.id,n.extras):null},l.opened=function(){var n=t.execSync(this,"opened",[]);if(n){for(var o=[],r={},c=0;c<n.length;c++){var a=n[c],s=e.webview.__JSON_Window_Stack[a];s||((s=new e.webview.Webview(null,null,!0,a.extras)).__uuid__=a.uuid,s.id=a.id,i.exec("UI","execMethod",["UI","setcallbackid",[s.__uuid__,[s.__callback_id__]]])),o.push(s),r[s.__uuid__]=s}return o}},l.remove=function(i){var o;if("string"==typeof i)o=i;else{if(!(i instanceof e.webview.Webview))return i instanceof e.nativeObj.View?void t.exec(this,"removeNativeView",[n,i.__uuid__]):void 0;o=i.__uuid__}t.exec(this,"remove",[o])},l.removeFromParent=function(){t.exec(this,"removeFromParent",[])},l.parent=function(){var n=t.execSync(this,"parent",[]);return n?e.webview._find__Window_By_UUID__(n.uuid,n.id,n.extras):null},l.children=function(){for(var n=[],i=t.execSync(this,"children",[]),o=0;o<i.length;o++)n.push(e.webview._find__Window_By_UUID__(i[o].uuid,i[o].id,i[o].extras));return n},l.getURL=function(){return t.execSync(this,"getUrl",[])},l.getTitle=function(){return t.execSync(this,"getTitle",[])},l.getStyle=function(){return t.execSync(this,"getOption")},l.loadURL=function(e,n){e&&"string"==typeof e&&t.exec(this,"load",[e,r(),n])},l.loadData=function(e,n){e&&"string"==typeof e&&t.exec(this,"loadData",[e,n])},l.stop=function(){t.exec(this,"stop",[])},l.reload=function(e){t.exec(this,"reload",[e])},l.draw=function(e,n,r,c){if(o.platform==o.IOS)e.__captureWebview?e.__captureWebview(this.__uuid__,c,n,r):"function"==typeof r&&r({code:-1,message:"Parameter error"});else{var a=function(e){"function"==typeof r&&r(e)};if(e&&e.__id__){var s=i.callbackId(function(){"function"==typeof n&&n()},a);t.exec(this,"draw",[e.__id__,this.__uuid__,s,c])}else a({code:-1,message:"Destroyed objects"})}},l.checkRenderedContent=function(e,n,o){var r=i.callbackId(function(e){"function"==typeof n&&n(e)},function(e){"function"==typeof o&&o(e)});t.exec(this,"checkRenderedContent",[this.__uuid__,r,e])},l.back=function(){t.exec(this,"back",[])},l.forward=function(){t.exec(this,"forward",[])},l.canBack=function(e){var n;e&&"function"==typeof e&&(e&&(n=i.callbackId(function(t){var n={};n.canBack=t,e(n)})),t.exec(this,"canBack",[n]))},l.canForward=function(e){var n;e&&"function"==typeof e&&(e&&(n=i.callbackId(function(t){var n={};n.canForward=t,e(n)})),t.exec(this,"canForward",[n]))},l.clear=function(){t.exec(this,"clear",[])},l.evalJS=function(e){e&&"string"==typeof e&&t.exec(this,"evalJS",[e])},l.test=function(e){t.exec(this,"test",[e])},l.append=function(i){i&&(i instanceof e.webview.Webview||i instanceof e.webview.WebviewGroup?(this.__view_array__.push(i),t.exec(this,"append",[i.__IDENTITY__,i.__uuid__])):i instanceof e.nativeObj.View?t.exec(this,"appendNativeView",[i.IDENTITY||n,i.__uuid__]):t.exec(this,"appendNativeView",[i.IDENTITY||n,i.__uuid__||i.id||i._UUID_]))},l.setPullToRefresh=function(n,i){var o;i&&(o=e.bridge.callbackId(i)),this.addEventListener("pulldownrefreshevent",i,!1),t.exec(this,"setPullToRefresh",[n,o])},l.endPullToRefresh=function(){t.exec(this,"endPullToRefresh",[])},l.beginPullToRefresh=function(){t.exec(this,"beginPullToRefresh",[])},l.setBounce=function(e,n){t.exec(this,"setBounce",[e,n])},l.resetBounce=function(){t.exec(this,"resetBounce",[])},l.setBlockNetworkImage=function(e){t.exec(this,"setBlockNetworkImage",[e])},l.captureSnapshot=function(n,i,o){var r="function"!=typeof i?null:function(e){i()},c="function"!=typeof o?null:function(e){o(e)},a=e.bridge.callbackId(r,c);e.tools.platform==e.tools.IOS?r():t.exec(this,"captureSnapshot",[n,a])},l.clearSnapshot=function(n){e.tools.platform!=e.tools.IOS&&t.exec(this,"clearSnapshot",[n])},l.overrideUrlLoading=function(e,n){var o=i.callbackId(function(e){"function"==typeof n&&n(e)});t.exec(this,"overrideUrlLoading",[e,o])},l.overrideResourceRequest=function(e){t.exec(this,"overrideResourceRequest",[e])},l.isHardwareAccelerated=function(){return o.platform!=o.IOS&&t.execSync(this,"isHardwareAccelerated",[])},l.listenResourceLoading=function(e,n){var o=i.callbackId(function(e){"function"==typeof n&&n(e)});t.exec(this,"listenResourceLoading",[e,o])},l.setCssFile=function(e){t.exec(this,"setCssFile",[e])},l.setCssText=function(e){t.exec(this,"setCssText",[e])},l.showBehind=function(e){t.exec(this,"showBehind",[e.__IDENTITY__,e.__uuid__])},l.animate=function(e,n){var r;o.platform!=o.IOS?(n&&(r=i.callbackId(function(){"function"==typeof n&&n()})),t.exec(this,"webview_animate",[e,r])):setTimeout(n,10)},l.interceptTouchEvent=function(e){t.exec(this,"interceptTouchEvent",[e])},l.setFavoriteOptions=function(e){t.exec(this,"setFavoriteOptions",[e])},l.getFavoriteOptions=function(){return t.execSync(this,"getFavoriteOptions")},l.setShareOptions=function(e){t.exec(this,"setShareOptions",[e])},l.getShareOptions=function(){return t.execSync(this,"getShareOptions")},l.getSafeAreaInsets=function(){return e.tools.ANDROID==e.tools.platform?{bottom:0,left:0,right:0,top:0,deviceBottom:0,deviceLeft:0,deviceRight:0,deviceTop:0}:t.execSync(this,"getSafeAreaInsets")},l.restore=function(){t.exec(this,"webview_restore",null)},l.setSoftinputTemporary=function(e){t.exec(this,"setSoftinputTemporary",[e])},t.Webview=u}(window.plus),function(e){var t=(e=e).bridge;function n(){this.id="",this.description="",this.nativeClient=!1,this.authResult=null,this.userInfo=null,this.appleInfo=null,this.univerifyInfo=null}n.prototype.getCheckBoxState=function(){if("univerify"==this.id)return t.execSync2("OAuth","getCheckBoxState",[this.id])},n.prototype.closeAuthView=function(){"univerify"==this.id&&t.exec("OAuth","closeAuthView",[this.id],{sid:this.id})},n.prototype.preLogin=function(e,n){var i="function"!=typeof e?null:function(t){e({})},o="function"!=typeof n?null:function(e){n(e)};if("univerify"==this.id){var r=t.callbackId(i,o);t.exec("OAuth","preLogin",[this.id,r],{cbid:r,sid:this.id})}else{n({code:10012,message:"univerify only"})}},n.prototype.login=function(e,n,i){if(i&&"object"==typeof i){var o=i.univerifyStyle;if(o&&"object"==typeof o){var r=o.buttons;r&&"object"==typeof r&&function(e){if(Array.isArray(e))for(var n=0;n<e.length;n++)!function(e){if(e.onclick&&"function"==typeof e.onclick){var n=t.callbackId(function(){e.onclick(e)});e.__cb__={id:n,htmlId:window.__HtMl_Id__}}}(e[n])}(r.list)}}var c=this,a="function"!=typeof e?null:function(t){var n={};n.target=c,c.univerifyInfo=t.univerifyInfo,c.authResult=t.authResult,c.userInfo=t.userInfo,c.appleInfo=t.appleInfo,c.extra=t.extra,e(n)},s="function"!=typeof n?null:function(e){n(e)},u=t.callbackId(a,s);t.exec("OAuth","login",[this.id,u,i],{cbid:u,sid:this.id})},n.prototype.authorize=function(e,n,i){var o=this,r="function"!=typeof e?null:function(t){var n={};(n=t).target=o,e(n)},c="function"!=typeof n?null:function(e){n(e)};if("weixin"==o.id){var a=t.callbackId(r,c);t.exec("OAuth","authorize",[this.id,a,i],{cbid:a,sid:this.id})}else{n({code:10012,message:"WeChat only"})}},n.prototype.logout=function(e,n){var i=this,o="function"!=typeof e?null:function(t){var n={};i.authResult=null,i.userInfo=null,i.appleInfo=null,i.extra=t.extra,n.target=i,e(n)},r="function"!=typeof n?null:function(e){n(e)};this.authResult=null,this.userInfo=null,this.appleInfo=null;var c=t.callbackId(o,r);t.exec("OAuth","logout",[this.id,c],{cbid:c,sid:this.id})},n.prototype.getUserInfo=function(e,n){var i=this,o="function"!=typeof e?null:function(t){var n={};i.authResult=t.authResult,i.userInfo=t.userInfo,i.appleInfo=t.appleInfo,i.extra=t.extra,n.target=i,e(n)},r="function"!=typeof n?null:function(e){n(e)},c=t.callbackId(o,r);t.exec("OAuth","getUserInfo",[this.id,c],{cbid:c,sid:this.id})},n.prototype.addPhoneNumber=function(e,n){var i=this,o="function"!=typeof e?null:function(t){var n={};i.authResult=t.authResult,i.userInfo=t.userInfo,i.appleInfo=t.appleInfo,i.extra=t.extra,n.target=i,e(n)},r="function"!=typeof n?null:function(e){n(e)},c=t.callbackId(o,r);t.exec("OAuth","addPhoneNumber",[this.id,c],{cbid:c,sid:this.id})};var i={AuthService:n,getServices:function(e,n){var o="function"!=typeof e?null:function(t){for(var n=[],o=t.length,r=0;r<o;r++){var c=new i.AuthService;c.id=t[r].id,c.description=t[r].description,c.authResult=t[r].authResult,c.userInfo=t[r].userInfo,c.appleInfo=t[r].appleInfo,c.univerifyInfo=t[r].univerifyInfo,c.nativeClient=t[r].nativeClient,n[r]=c}e(n)},r="function"!=typeof n?null:function(e){n(e)},c=t.callbackId(o,r);t.exec("OAuth","getServices",[c])}};e.oauth=i}(window.plus),function(e){var t="NativeObj",n=(e=e).bridge,i=e.tools,o={},r={},c={},a={};function s(e,o,c){this.__id__=i.UUID("Bitmap"),this.id=e,this.type="bitmap",c||(r[this.__id__]=this,n.exec(t,"Bitmap",[this.__id__,e,o]))}function u(e){if(e instanceof Array)for(var t,i=0;i<e.length;i++)(t=e[i])&&"input"==t.tag?t.inputStyles&&(t.inputStyles.onComplete&&"function"==typeof t.inputStyles.onComplete&&(t.inputStyles.__onCompleteCallBackId__=n.callbackId(t.inputStyles.onComplete)),t.inputStyles.onFocus&&"function"==typeof t.inputStyles.onFocus&&(t.inputStyles.__onFocusCallBackId__=n.callbackId(t.inputStyles.onFocus)),t.inputStyles.onBlur&&"function"==typeof t.inputStyles.onBlur&&(t.inputStyles.__onBlurCallBackId__=n.callbackId(t.inputStyles.onBlur))):t&&"richtext"==t.tag&&t.richTextStyles&&t.richTextStyles.onClick&&"function"==typeof t.richTextStyles.onClick&&(t.richTextStyles.__onClickCallBackId__=n.callbackId(t.richTextStyles.onClick))}function l(e,r,c,a,s){this.__id__=e,this.id=e,this.__uuid__=i.UUID("NativeView"),this.type="nativeView",this.IDENTITY=t,a||(u(c),n.exec(t,"View",[this.__id__,this.__uuid__,r,c,s]),o[this.__uuid__]=this)}function _(e,t){var n=null;for(var i in a){var o=a[i];if(t){if(o.uid&&o.uid==e||o.uuid&&o.uuid==e){n=o;break}}else if(o.id==e){n=o;break}}return n}function f(t){var n=o[t.uuid];if(!n){if(t.type&&"ImageSlider"==t.type)n=new e.nativeObj.ImageSlider(t.id,t.styles,"",!0);else{var i=c[t.type];n=i?new i(t.id,t.styles,"",!0):new e.nativeObj.View(t.id,t.styles,"",!0)}n.__uuid__=t.uuid,o[n.__uuid__]=n}return n}s.prototype.clear=function(){n.exec(t,"clear",[this.__id__]),this.__id__=void 0,r[this.__id__]&&delete r[this.__id__]},s.prototype.recycle=function(){n.exec(t,"bitmapRecycle",[this.__id__])},s.prototype.load=function(e,i,o){var r=function(e){"function"==typeof o&&o(e)};if(this.__id__){var c=n.callbackId(function(){"function"==typeof i&&i()},r);n.exec(t,"load",[this.__id__,e,c])}else r({code:-1,message:"Destroyed objects"})},s.prototype.loadBase64Data=function(e,i,o){var r=function(e){"function"==typeof o&&o(e)};if(this.__id__){var c=n.callbackId(function(){"function"==typeof i&&i()},r);n.exec(t,"loadBase64Data",[this.__id__,e,c])}else r({code:-1,message:"Destroyed objects"})},s.prototype.save=function(e,i,o,r){var c=function(e){"function"==typeof r&&r(e)};if(this.__id__){var a=n.callbackId(function(e){if("function"==typeof o){var t={target:e.path,width:e.w,height:e.h,size:e.size};o(t)}},c);n.exec(t,"save",[this.__id__,e,i,a])}else c({code:-1,message:"Destroyed objects"})},s.prototype.__captureWebview=function(e,i,o,r){var c=function(e){"function"==typeof r&&r(e)};if(this.__id__){var a=n.callbackId(function(){"function"==typeof o&&o()},c);n.exec(t,"captureWebview",[this.__id__,e,a,i])}else c({code:-1,message:"Destroyed objects"})},s.prototype.toBase64Data=function(){return this.__id__?n.execSync(t,"toBase64Data",[this.__id__]):null},s.getItems=function(){for(var e=[],i=n.execSync(t,"getItems",[]),o=0;o<i.length;o++){var c=i[o].__id__,a=r[c];if(!a){var u=new s(i[o].id,null,!0);u.__id__=c,r[c]=u,a=u}e.push(a)}return e},s.getBitmapById=function(e){var i=n.execSync(t,"getBitmapById",[e]);if(i){var o=r[i.__id__];if(!o){var c=new s(i.id,null,!0);c.__id__=i.__id__,r[i.__id__]=c,o=c}return o}return null},l.prototype.setImages=function(e){n.exec(t,"setImages",[this.__id__,this.__uuid__,e])},l.prototype.currentImageIndex=function(){return n.execSync(t,"currentImageIndex",[this.__id__,this.__uuid__])},l.prototype.addImages=function(e){n.exec(t,"addImages",[this.__id__,this.__uuid__,e])},l.prototype.drawBitmap=function(e,i,o,r){n.exec(t,"drawBitmap",[this.__id__,this.__uuid__,e,i,o,r])},l.prototype.drawText=function(e,i,o,r){n.exec(t,"drawText",[this.__id__,this.__uuid__,e,i,o,r])},l.prototype.drawRichText=function(e,i,o,r){o&&o.onClick&&"function"==typeof o.onClick&&(o.__onClickCallBackId__=n.callbackId(o.onClick)),n.exec(t,"drawRichText",[this.__id__,this.__uuid__,e,i,o,r])},l.prototype.drawInput=function(e,i,o){i.onComplete&&"function"==typeof i.onComplete&&(i.__onCompleteCallBackId__=n.callbackId(i.onComplete)),i.onFocus&&"function"==typeof i.onFocus&&(i.__onFocusCallBackId__=n.callbackId(i.onFocus)),i.onBlur&&"function"==typeof i.onBlur&&(i.__onBlurCallBackId__=n.callbackId(i.onBlur)),n.exec(t,"drawInput",[this.__id__,this.__uuid__,e,i,o])},l.prototype.getInputValueById=function(e){return n.execSync(t,"getInputValueById",[this.__id__,this.__uuid__,e])},l.prototype.getInputFocusById=function(e){return n.execSync(t,"getInputFocusById",[this.__id__,this.__uuid__,e])},l.prototype.setInputFocusById=function(e,i){n.exec(t,"setInputFocusById",[this.__id__,this.__uuid__,e,i])},l.prototype.show=function(){n.exec(t,"show",[this.__id__,this.__uuid__])},l.prototype.setStyle=function(e){n.exec(t,"setStyle",[this.__id__,this.__uuid__,e])},l.prototype.hide=function(){n.exec(t,"hide",[this.__id__,this.__uuid__])},l.prototype.clear=function(){this.close()},l.prototype.close=function(){n.exec(t,"view_close",[this.__id__,this.__uuid__]),delete o[this.__uuid__]},l.prototype.animate=function(e,i){var o;i&&(o=n.callbackId(function(){"function"==typeof i&&i()})),n.exec(t,"view_animate",[this.__id__,this.__uuid__,e,o])},l.prototype.reset=function(){n.exec(t,"view_reset",[this.__id__,this.__uuid__])},l.prototype.restore=function(){n.exec(t,"view_restore",[this.__id__,this.__uuid__])},l.prototype.isVisible=function(){return n.execSync(t,"isVisible",[this.__id__,this.__uuid__])},l.prototype.drawRect=function(e,i,o){n.exec(t,"view_drawRect",[this.__id__,this.__uuid__,e,i,o])},l.prototype.interceptTouchEvent=function(e){n.exec(t,"interceptTouchEvent",[this.__id__,this.__uuid__,e])},l.prototype.addEventListener=function(e,i){var o,r=this;if(i){var c=function(e){"function"==typeof i&&(e.target=r,i(e))};i.callback=c,o=n.callbackId(c)}n.exec(t,"addEventListener",[this.__id__,this.__uuid__,e,o])},l.prototype.setEventListener=function(e,i){var o,r=this;if(i){var c=function(e){"function"==typeof i&&(e.target=r,i(e))};i.callback=c,o=n.callbackId(c)}n.exec(t,"setEventListener",[this.__id__,this.__uuid__,e,o])},l.prototype.setTouchEventRect=function(e){n.exec(t,"setTouchEventRect",[this.__id__,this.__uuid__,e])},l.getViewById=function(e){var i=n.execSync(t,"getViewById",[e]);return i||(i=_(e,!1)),i?f(i):null},l.__getViewByUidAndCreate=function(e){return f(e)},l.__getViewByUidFromCache=function(e){var t=_(e,!0);return t?f(t):null},l.startAnimation=function(e,i,o,r){var c,a,s;e&&i&&(a=i instanceof l?{viewId:i.__uuid__}:{uuid:i.bitmap?i.bitmap.__id__:null,texts:{value:i.text,textStyles:i.textStyles,textRect:i.textRect}},o&&(s=o instanceof l?{viewId:o.__uuid__}:{uuid:o.bitmap?o.bitmap.__id__:null,texts:{value:o.text,textStyles:o.textStyles,textRect:o.textRect}}),r&&(c=n.callbackId(function(){"function"==typeof r&&r()})),n.exec(t,"startAnimation",[e,a,s,c]))},l.clearAnimation=function(e){e||(e="none"),n.exec(t,"clearAnimation",[e])},l.prototype.clearRect=function(e,i){n.exec(t,"view_clearRect",[this.__id__,this.__uuid__,e,i])},l.prototype.draw=function(e){u(e),n.exec(t,"view_draw",[this.__id__,this.__uuid__,e])};var d={Bitmap:s,View:l,ImageSlider:function(e,t,n,i){var o=new l(e,t,n,i,"ImageSlider");return o.type="ImageSlider",o},__createNView:function(t,n,i,r){var c=new e.nativeObj.View(n,i,r);return c.__uuid__=t,o[c.__uuid__]=c,c},__appendSubViewInfo:function(e){a[e.uuid]=e},__removeSubViewInfos:function(e){for(var t=0;t<e.length;t++)delete a[e[t]]},__regChildViews:function(e,t){e&&"function"==typeof t&&(c[e]=t)}};e.nativeObj=d}(plus),function(e){e=e;var t=window.plus.bridge,n=e.tools;e.stream={open:function(e,n,i){var o=t.callbackId(n,i);t.exec("Stream","open",[e,o])},setRestoreState:function(e){t.exec("Stream","setRestoreState",[e])},preload:function(e){t.exec("Stream","preload",[e])},list:function(e,n,i){var o=t.callbackId(n,i);t.exec("Stream","list",[e,o])},remove:function(e){t.exec("Stream","remove",[e])},freetrafficRequest:function(e,i,o){if(n.platform!=n.IOS){var r=t.callbackId(i,o);t.exec("Stream","freetrafficRequest",[e,r])}else"function"==typeof o&&o({code:-3,message:"不支持"})},freetrafficBind:function(e,i,o){if(n.platform!=n.IOS){var r=t.callbackId(i,o);t.exec("Stream","freetrafficBind",[e,r])}else"function"==typeof o&&o({code:-3,message:"Not Supported"})},freetrafficRelease:function(e,i,o){if(n.platform!=n.IOS){var r=t.callbackId(i,o);t.exec("Stream","freetrafficRelease",[e,r])}else"function"==typeof o&&o({code:-3,message:"Not Supported"})},freetrafficInfo:function(e,i){if(n.platform!=n.IOS){var o=t.callbackId(e,i);t.exec("Stream","freetrafficInfo",[o])}else"function"==typeof i&&i({code:-3,message:"Not Supported"})},freetrafficIsValid:function(){if(n.platform!=n.IOS)return t.exec("Stream","freetrafficIsValid",null);"function"==typeof errorCallback&&errorCallback({code:-3,message:"Not Supported"})},activate:function(){n.platform!=n.IOS?t.exec("Stream","activate",null):"function"==typeof errorCallback&&errorCallback({code:-3,message:"Not Supported"})}}}(window.plus),function(e){var t=(e=e).bridge;e.device={imei:"",imsi:[],model:"",vendor:"",uuid:"",dial:function(e,n){t.exec("Device","dial",[e,n])},beep:function(e){t.exec("Device","beep",[e])},vibrate:function(e){t.exec("Device","vibrate",[e])},setWakelock:function(e){t.exec("Device","setWakelock",[e])},isWakelock:function(){return t.execSync("Device","isWakelock",[])},setVolume:function(e){t.execSync("Device","setVolume",[e])},getVolume:function(){return t.execSync("Device","getVolume",[])},execCallback:function(e,n){var i="function"!=typeof e.complete?function(){}:e.complete,o="function"!=typeof e.success?i:function(t){e.success(t),i(t)},r="function"!=typeof e.fail?i:function(t){e.fail(t),i(t)};callbackID=t.callbackId(o,r),t.exec("Device",n,[callbackID])},getInfo:function(e){this.execCallback(e,"getInfo")},getOAID:function(e){this.execCallback(e,"getOAID")},getVAID:function(e){this.execCallback(e,"getVAID")},getAAID:function(e){this.execCallback(e,"getAAID")},getDeviceId:function(){return t.execSync("Device","getDeviceId",[])}},e.os={language:"",version:"",name:"",vendor:""},e.screen={resolutionHeight:0,resolutionWidth:0,scale:1,dpiX:0,dpiY:0,height:0,width:0,setBrightness:function(e,n){t.execSync("Device","setBrightness",[e,n])},lockOrientation:function(e){t.exec("Device","lockOrientation",[e])},unlockOrientation:function(){t.exec("Device","unlockOrientation",[])},getBrightness:function(e){return t.execSync("Device","getBrightness",[e])},getCureentSize:function(){return t.execSync("Device","getCurrentSize",[])},getCurrentSize:function(){return t.execSync("Device","getCurrentSize",[])},isCaptured:function(){return t.execSync("Device","isCaptured",[])}},e.display={resolutionHeight:0,resolutionWidth:0},e.networkinfo={getCurrentType:function(){return t.execSync("Device","getCurrentType",null)},getCurrentAPN:function(){return t.execSync("Device","getCurrentAPN",null)},isSetProxy:function(){return t.execSync("Device","isSetProxy",null)}},e.networkinfo.CONNECTION_TYPE=0,e.networkinfo.CONNECTION_UNKNOW=0,e.networkinfo.CONNECTION_NONE=1,e.networkinfo.CONNECTION_ETHERNET=2,e.networkinfo.CONNECTION_WIFI=3,e.networkinfo.CONNECTION_CELL2G=4,e.networkinfo.CONNECTION_CELL3G=5,e.networkinfo.CONNECTION_CELL4G=6,e.networkinfo.CONNECTION_CELL5G=7}(window.plus),function(e){var t=(e=e).bridge;e.fingerprint={isSupport:function(){return t.execSync("fingerprint","isSupport")},isEnrolledFingerprints:function(){return t.execSync("fingerprint","isEnrolledFingerprints")},isKeyguardSecure:function(){return t.execSync("fingerprint","isKeyguardSecure")},authenticate:function(e,n,i){var o="function"!=typeof e?null:function(t){e(t)},r="function"!=typeof n?null:function(e){n(new function(e,t){this.code=e,this.message=t,this.UNSUPPORT=1,this.KEYGUARD_INSECURE=2,this.FINGERPRINT_UNENROLLED=3,this.AUTHENTICATE_MISMATCH=4,this.AUTHENTICATE_OVERLIMIT=5,this.CANCEL=6,this.UNKNOWN_ERROR=7}(e.code,e.message))};callbackID=t.callbackId(o,r),t.exec("fingerprint","authenticate",[callbackID,i])},cancel:function(){return t.exec("fingerprint","cancel")}}}(window.plus),__Media_Live__Push__=function(){var e=[];return{pushCallback_LivePush:function(t,n,i,o,r){e[r]={fun:n,nokeep:i,pushobj:o}},execCallback_LivePush:function(t,n,i){if(e[i]){var o={};o.type=t,o.target=e[i].pushobj,o.detail=n,e[i].fun&&e[i].fun(o)}}}}(),function(e){var t=(e=e).bridge,n="VideoPlayer",i="LivePusher",o=e.tools,r={},c={};function a(e){return o.getElementOffsetXInWebview(e)}function s(e){return o.getElementOffsetYInWebview(e)}function u(i,r,c,u){this.id=o.UUID("dt"),this.IDENTITY=n,this.userId=c,div=document.getElementById(i);var l=this.id;if(!u){var _=[];div&&(e.tools.platform==e.tools.ANDROID?document.addEventListener("plusorientationchange",function(){setTimeout(function(){var e=[a(div),s(div),div.offsetWidth,div.offsetHeight];t.exec(n,"resize",[l,e])},200)},!1):div.addEventListener("resize",function(){var e=[a(div),s(div),div.offsetWidth,div.offsetHeight];t.exec(n,"resize",[l,e])},!1),_=[a(div),s(div),div.offsetWidth,div.offsetHeight]),t.exec(n,"VideoPlayer",[this.id,_,r,c])}}var l=u.prototype;function _(n,o,r,c){this.IDENTITY="LivePusher",this.id=r,this.__uuid__=e.tools.UUID("LivePusher"),this.options=o,this.onCapture=null,me=this;var u=null;null!=r&&void 0!=r||(this.id=this.__uuid__);var l=document.getElementById(n);null!=l&&void 0!=l&&(e.tools.ANDROID==e.tools.platform?window.onresize=function(){var e=document.getElementById(n),o=[a(e),s(e),e.offsetWidth,e.offsetHeight];t.exec(i,"resize",[me.__uuid__,o])}:e.tools.IOS==e.tools.platform&&l.addEventListener("resize",function(){var e=[a(l),s(l),l.offsetWidth,l.offsetHeight];t.exec(i,"resize",[me.__uuid__,e])},!1),u=[a(l),s(l),l.offsetWidth,l.offsetHeight]);var _=!0;void 0==c||null==c||c||(_=!1),_&&t.exec(i,"LivePusher",[this.__uuid__,this.id,u,o])}l.play=function(){t.exec(n,"VideoPlayer_play",[this.id])},l.pause=function(){t.exec(n,"VideoPlayer_pause",[this.id])},l.stop=function(){t.exec(n,"VideoPlayer_stop",[this.id])},l.close=function(){t.exec(n,"VideoPlayer_close",[this.id])},l.sendDanmu=function(e){t.exec(n,"VideoPlayer_sendDanmu",[this.id,e])},l.seek=function(e){t.exec(n,"VideoPlayer_seek",[this.id,e])},l.playbackRate=function(e){t.exec(n,"VideoPlayer_playbackRate",[this.id,e])},l.requestFullScreen=function(e){t.exec(n,"VideoPlayer_requestFullScreen",[this.id,e])},l.exitFullScreen=function(){t.exec(n,"VideoPlayer_exitFullScreen",[this.id])},l.hide=function(){t.exec(n,"VideoPlayer_hide",[this.id])},l.show=function(){t.exec(n,"VideoPlayer_show",[this.id])},l.hideStatusBar=function(){t.exec(n,"VideoPlayer_hideStatusBar",[this.id])},l.showStatusBar=function(){t.exec(n,"VideoPlayer_showStatusBar",[this.id])},l.setOptions=function(e){t.exec(n,"VideoPlayer_setOptions",[this.id,e])},l.setStyles=l.setOptions,l.addEventListener=function(e,i){var o,r=this;if(i){var c=function(e){"function"==typeof i&&(e.target=r,i(e))};i.callback=c,o=t.callbackId(c)}t.exec(n,"VideoPlayer_addEventListener",[this.id,e,o,window.__HtMl_Id__])};var f=_.prototype;function d(i,o){o&&o.onCapture&&"function"==typeof o.onCapture&&(o.__onCaptureCallbackId__=t.callbackId(o.onCapture));var r=document.getElementById(i);e.tools.ANDROID==e.tools.platform?window.onresize=function(){var e=document.getElementById(i),o=[a(e),s(e),e.offsetWidth,e.offsetHeight];t.exec(n,"resize",o)}:e.tools.IOS==e.tools.platform&&r.addEventListener("resize",function(){var e=[a(r),s(r),r.offsetWidth,r.offsetHeight];t.exec(n,"resize",e)},!1);var c=[r.offsetLeft,r.offsetTop,r.offsetWidth,r.offsetHeight];t.exec(n,"VideoCapture",[c,o])}f.start=function(e,n){var o="function"!=typeof e?null:function(t){e()},r="function"!=typeof n?null:function(e){n(e)},c=t.callbackId(o,r);t.exec(i,"start",[this.__uuid__,c])},f.preview=function(){t.exec(i,"preview",[this.__uuid__])},f.stop=function(e){t.exec(i,"stop",[this.__uuid__,e])},f.pause=function(){t.exec(i,"pause",[this.__uuid__])},f.close=function(){t.exec(i,"close",[this.__uuid__])},f.resume=function(){t.exec(i,"resume",[this.__uuid__])},f.close=function(){t.exec(i,"close",[this.__uuid__])},f.setStyles=function(e){t.exec(i,"setOptions",[this.__uuid__],e)},f.setOptions=function(e){t.exec(i,"setOptions",[this.__uuid__],e)},f.switchCamera=function(e){t.exec(i,"switchCamera",[this.__uuid__])},f.addEventListener=function(e,n,o){var r,c=this;if(n){var a=function(e){"function"==typeof callback&&(e.target=c,n(e))};n.listener=a,r=t.callbackId(a)}__Media_Live__Push__.pushCallback_LivePush(e,n,o,this,r),t.exec(i,"addEventListener",[this.__uuid__,window.__HtMl_Id__,e,r])},f.snapshot=function(e,n){var o="function"!=typeof e?null:function(t){e(t)},r="function"!=typeof n?null:function(e){n(e)},c=t.callbackId(o,r);t.exec(i,"snapshot",[this.__uuid__,c])};var h=d.prototype;function p(e,i){i&&i.video&&(i.video.onCaptionChanged&&"function"==typeof i.video.onCaptionChanged&&(i.video.__onCaptionChangedCallbackId__=t.callbackId(i.onCapture)),i.video.onWatermarkChanged&&"function"==typeof i.video.onWatermarkChanged&&(i.video.__onWatermarkChangedCallbackId__=t.callbackId(i.onCapture))),div=document.getElementById(e),div.addEventListener("resize",function(){var e=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight];t.exec(_BARCODE,"resize",[e])},!1);var o=[div.offsetLeft,div.offsetTop,div.offsetWidth,div.offsetHeight];t.exec(n,"VideoEditor",[o,i])}h.setFilter=function(e){t.exec(n,"VideoCapture_setFilter",[e])},h.setFacing=function(e){t.exec(n,"VideoCapture_setFacing",[e])},h.setResolution=function(e){t.exec(n,"VideoCapture_setResolution",[e])},h.start=function(){t.exec(n,"VideoCapture_start",[])},h.stop=function(){t.exec(n,"VideoCapture_stop",[])},h.getSupportedResolutions=function(){return t.execSync(n,"VideoCapture_getSupportedResolutions",[])},h.close=function(){t.exec(n,"VideoCapture_close",[]),this.isClose=!0};var y=p.prototype;y.setCaption=function(e,i){t.exec(n,"VideoEditor_setCaption",[e,i])},y.setWatermark=function(e,i){t.exec(n,"VideoEditor_setWatermark",[e,i])},y.setIndex=function(e){t.exec(n,"VideoEditor_setIndex",[e])},y.play=function(){t.exec(n,"VideoEditor_play",[])},y.pause=function(){t.exec(n,"VideoEditor_pause",[])},y.close=function(){t.exec(n,"VideoEditor_close",[]),this.isClose=!0};var v={VideoPlayer:u,createVideoPlayer:function(e,t){var n=new u(null,t,e);return c[n.id]=n,c[n.id]},getVideoPlayerById:function(i){if(i&&"string"==typeof i){var o=t.execSync(n,"getVideoPlayerById",[i]);if(null!=o&&null!=o.uid){if(c[o.uid])return c[o.uid];if(null!=o&&void 0!=o){var r=new e.video.VideoPlayer(null,null,o.name,!0);return r.id=o.uid,c[o.uid]=r,r}return null}}},createLivePusher:function(t,n,i){var o=!0;void 0==i||null==i||i||(o=!1);var c=new e.video.LivePusher(null,n,t,o);return r[c.__uuid__]=c,r[c.__uuid__]},getLivePusherById:function(n){if(n&&"string"==typeof n){var o=t.execSync(i,"getLivePusherById",[n]);if(null!=o&&void 0!=o&&o.uuid){if(r[o.uuid])return r[o.uuid];var c=new e.video.LivePusher(null,null,n,!1);return c.__uuid__=o.uuid,r[o.uuid]=c,c}return null}},VideoCapture:d,VideoEditor:p,LivePusher:_,getSupportedResolutions:function(){return t.execSync(n,"getSupportedResolutions",[])},getVideoInfo:function(e,i){var o=null;i&&"function"==typeof i&&(o=t.callbackId(i)),t.exec(n,"getVideoInfo",[e,o])}};e.video=v}(window.plus),function(e){var t=(e=e).bridge,n="ShortVideo",i=e.tools;function o(e){return i.getElementOffsetXInWebview(e)}function r(e){return i.getElementOffsetYInWebview(e)}function c(c,a){this.id=i.UUID("dt"),this.IDENTITY=n,div=document.getElementById(c);var s=this.id,u=[];div&&(e.tools.platform==e.tools.ANDROID?document.addEventListener("plusorientationchange",function(){setTimeout(function(){var e=[o(div),r(div),div.offsetWidth,div.offsetHeight];t.exec(n,"resize",[s,e])},200)},!1):div.addEventListener("resize",function(){var e=[o(div),r(div),div.offsetWidth,div.offsetHeight];t.exec(n,"resize",[s,e])},!1),u=[o(div),r(div),div.offsetWidth,div.offsetHeight]),t.exec(n,"ShortVideo",[this.id,u,a])}var a=c.prototype;function s(c,a){this.id=i.UUID("dt"),this.IDENTITY=n,div=document.getElementById(c);var s=[],u=this.id;div&&(e.tools.platform==e.tools.ANDROID?document.addEventListener("plusorientationchange",function(){setTimeout(function(){var e=[o(div),r(div),div.offsetWidth,div.offsetHeight];t.exec(n,"shortVideoEditor_resize",[u,e])},200)},!1):div.addEventListener("resize",function(){var e=[o(div),r(div),div.offsetWidth,div.offsetHeight];t.exec(n,"shortVideoEditor_resize",[u,e])},!1),s=[o(div),r(div),div.offsetWidth,div.offsetHeight]),t.exec(n,"ShortVideoEditor",[this.id,s,a])}a.start=function(e){t.exec(n,"ShortVideo_start",[this.id,e])},a.pause=function(){t.exec(n,"ShortVideo_pause",[this.id])},a.resume=function(){t.exec(n,"ShortVideo_resume",[this.id])},a.stop=function(){t.exec(n,"ShortVideo_stop",[this.id])},a.close=function(){t.exec(n,"ShortVideo_close",[this.id])},a.switchCamera=function(e){t.exec(n,"ShortVideo_switchCamera",[this.id,e])},a.setStyles=function(e){t.exec(n,"ShortVideo_setStyles",[this.id,e])},a.startMixAudio=function(e){t.exec(n,"ShortVideo_startMixAudio",[this.id,e])},a.pauseMixAudio=function(){t.exec(n,"ShortVideo_pauseMixAudio",[this.id])},a.resumeMixAudio=function(){t.exec(n,"ShortVideo_resumeMixAudio",[this.id])},a.stopMixAudio=function(){t.exec(n,"ShortVideo_stopMixAudio",[this.id])},a.addEventListener=function(e,i){var o,r=this;if(i){var c=function(e){"function"==typeof i&&(e.target=r,i(e))};i.callback=c,o=t.callbackId(c)}t.exec(n,"ShortVideo_addEventListener",[this.id,e,o])};var u=s.prototype;u.play=function(){t.exec(n,"shortVideoEditor_play",[this.id])},u.pause=function(){t.exec(n,"shortVideoEditor_pause",[this.id])},u.resume=function(){t.exec(n,"shortVideoEditor_resume",[this.id])},u.stop=function(){t.exec(n,"shortVideoEditor_stop",[this.id])},u.close=function(){t.exec(n,"shortVideoEditor_close",[this.id])},u.setStyles=function(e){t.exec(n,"shortVideoEditor_setStyles",[this.id,e])},u.save=function(e,i,o){var r="function"!=typeof i?null:function(e){i(e)},c="function"!=typeof o?null:function(e){o(e)},a=t.callbackId(r,c);t.exec(n,"shortVideoEditor_save",[this.id,e,a])},u.addEventListener=function(e,i){var o,r=this;if(i){var c=function(e){"function"==typeof i&&(e.target=r,i(e))};i.callback=c,o=t.callbackId(c)}t.exec(n,"shortVideoEditor_addEventListener",[this.id,e,o])};var l={Camera:c,Editor:s,upload:function(e,i,o){var r="function"!=typeof i?null:function(e){i(e)},c="function"!=typeof o?null:function(e){o(e)},a=t.callbackId(r,c);t.exec(n,"uploader",[e,a])}};e.shortvideo=l}(window.plus),function(e){var t=window.plus.bridge,n={closeBluetoothAdapter:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","closeBluetoothAdapter",[callbackID])},openBluetoothAdapter:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","openBluetoothAdapter",[callbackID])},getBluetoothAdapterState:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","getBluetoothAdapterState",[callbackID])},getBluetoothDevices:function(e){var n=this,i="function"!=typeof e.complete?function(){}:e.complete,o="function"!=typeof e.success?null:function(t){for(var o=t.devices,r=0;r<o.length;r++){var c=o[r];if(c.advertisData&&"string"==typeof c.advertisData&&"{}"!=c.advertisData&&(c.advertisData=n.string2ArrayBuffer(c.advertisData)),c.serviceData&&"{}"!=c.serviceData)for(var a in c.serviceData)"string"==typeof c.serviceData[a]&&""!=c.serviceData[a]&&(c.serviceData[a]=n.string2ArrayBuffer(c.serviceData[a]))}e.success(t),i(t)},r="function"!=typeof e.fail?null:function(t){e.fail(t),i(t)},c=t.callbackId(o,r);return t.exec("Bluetooth","getBluetoothDevices",[c])},getConnectedBluetoothDevices:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","getConnectedBluetoothDevices",[callbackID,e])},startBluetoothDevicesDiscovery:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","startBluetoothDevicesDiscovery",[callbackID,e])},stopBluetoothDevicesDiscovery:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","stopBluetoothDevicesDiscovery",[callbackID])},onBluetoothAdapterStateChange:function(e){return callbackID=this.getCallbackIDByFunction(e),t.exec("Bluetooth","onBluetoothAdapterStateChange",[callbackID])},onBluetoothDeviceFound:function(e){var n=this,i="function"!=typeof e?null:function(t){for(var i=t.devices,o=0;o<i.length;o++){var r=i[o];if(r.advertisData&&"string"==typeof r.advertisData&&"{}"!=r.advertisData&&(r.advertisData=n.string2ArrayBuffer(r.advertisData)),r.serviceData&&"{}"!=r.serviceData)for(var c in r.serviceData)"string"==typeof r.serviceData[c]&&""!=r.serviceData[c]&&(r.serviceData[c]=n.string2ArrayBuffer(r.serviceData[c]))}e(t)};return callbackID=t.callbackId(i),t.exec("Bluetooth","onBluetoothDeviceFound",[callbackID])},createBLEConnection:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","createBLEConnection",[callbackID,e])},closeBLEConnection:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","closeBLEConnection",[callbackID,e])},setBLEMTU:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","setBLEMTU",[callbackID,e])},getBLEDeviceRSSI:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","getBLEDeviceRSSI",[callbackID,e])},getBLEDeviceCharacteristics:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","getBLEDeviceCharacteristics",[callbackID,e])},getBLEDeviceServices:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","getBLEDeviceServices",[callbackID,e])},notifyBLECharacteristicValueChange:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","notifyBLECharacteristicValueChange",[callbackID,e])},onBLECharacteristicValueChange:function(e){var n=this,i="function"!=typeof e?null:function(t){t.value&&"string"==typeof t.value&&(t.value=n.string2ArrayBuffer(t.value)),e(t)};return callbackID=t.callbackId(i),t.exec("Bluetooth","onBLECharacteristicValueChange",[callbackID])},onBLEConnectionStateChange:function(e){return callbackID=this.getCallbackIDByFunction(e),t.exec("Bluetooth","onBLEConnectionStateChange",[callbackID])},readBLECharacteristicValue:function(e){return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),t.exec("Bluetooth","readBLECharacteristicValue",[callbackID,e])},writeBLECharacteristicValue:function(e){if("string"!=typeof e.value)return callbackID=this.getCallbackIDByFunction(e.success,e.fail,e.complete),e.value=this.ab2hex(e.value),t.exec("Bluetooth","writeBLECharacteristicValue",[callbackID,e]);e.fail({code:10019,message:"Value cannot be a string"})},ab2hex:function(e){return Array.prototype.map.call(new Uint8Array(e),function(e){return("00"+e.toString(16)).slice(-2)}).join("")},string2ArrayBuffer:function(e){return new Uint8Array(e.match(/[\da-f]{2}/gi).map(function(e){return parseInt(e,16)})).buffer},getCallbackIDByFunction:function(e,n,i){i="function"!=typeof i?function(){}:i;var o="function"!=typeof e?null:function(t){e(t),i(t)},r="function"!=typeof n?null:function(e){n(e),i(e)};return t.callbackId(o,r)}};e.bluetooth=n}(window.plus),function(e){var t=e.bridge,n={startBeaconDiscovery:function(e){if("object"==typeof e){var n="function"!=typeof e.complete?function(){}:e.complete,i="function"!=typeof e.success?n:function(t){e.success(t),n(t)},o="function"!=typeof e.fail?n:function(t){e.fail(t),n(t)};return callbackID=t.callbackId(i,o),t.exec("iBeacon","startBeaconDiscovery",[callbackID,e.uuids])}},stopBeaconDiscovery:function(e){var n="function"!=typeof e.complete?function(){}:e.complete,i="function"!=typeof e.success?n:function(t){e.success(t),n(t)},o="function"!=typeof e.fail?n:function(t){e.fail(t),n(t)};return callbackID=t.callbackId(i,o),t.exec("iBeacon","stopBeaconDiscovery",[callbackID])},getBeacons:function(e){var n="function"!=typeof e.complete?function(){}:e.complete,i="function"!=typeof e.success?n:function(t){e.success(t),n(t)},o="function"!=typeof e.fail?n:function(t){e.fail(t),n(t)};return callbackID=t.callbackId(i,o),t.exec("iBeacon","getBeacons",[callbackID])},onBeaconUpdate:function(e){var n="function"!=typeof e?null:e;return callbackID=t.callbackId(n),t.exec("iBeacon","onBeaconUpdate",[callbackID])},onBeaconServiceChange:function(e){var n="function"!=typeof e?null:e;return callbackID=t.callbackId(n),t.execSync("iBeacon","onBeaconServiceChange",[callbackID])}};e.ibeacon=n}(window.plus),function(e){var t=e.bridge,n={isOpenDatabase:function(e){return t.execSync("Sqlite","isOpenDatabase",[e])},openDatabase:function(e){if(e&&e.name&&e.path)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),t.exec("Sqlite","openDatabase",[callbackID,e.name,e.path])},closeDatabase:function(e){if(e&&e.name)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),t.exec("Sqlite","closeDatabase",[callbackID,e.name])},transaction:function(e){if(e&&e.name&&e.operation)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),t.exec("Sqlite","transaction",[callbackID,e.name,e.operation])},executeSql:function(e){if(e&&e.name&&e.sql)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),t.exec("Sqlite","executeSql",[callbackID,e.name,e.sql])},selectSql:function(e){if(e&&e.name&&e.sql)return callbackID=this.getCallbackIDByFunction(e.success,e.fail),t.exec("Sqlite","selectSql",[callbackID,e.name,e.sql])},getCallbackIDByFunction:function(e,n,i){var o="function"!=typeof e?null:function(t){e(t)},r="function"!=typeof n?null:function(e){n(e)};return t.callbackId(o,r)}};e.sqlite=n}(window.plus),function(e){var t="Ad",n=(e=e).bridge,i=e.tools,o={},r={},c={};function a(e,o,c){this.__id__=i.UUID("Bitmap"),this.id=e,this.type="bitmap",c||(r[this.__id__]=this,n.exec(t,"Bitmap",[this.__id__,e,o]))}function s(e){if(e instanceof Array)for(var t,i=0;i<e.length;i++)(t=e[i])&&"input"==t.tag?t.inputStyles&&(t.inputStyles.onComplete&&"function"==typeof t.inputStyles.onComplete&&(t.inputStyles.__onCompleteCallBackId__=n.callbackId(t.inputStyles.onComplete)),t.inputStyles.onFocus&&"function"==typeof t.inputStyles.onFocus&&(t.inputStyles.__onFocusCallBackId__=n.callbackId(t.inputStyles.onFocus)),t.inputStyles.onBlur&&"function"==typeof t.inputStyles.onBlur&&(t.inputStyles.__onBlurCallBackId__=n.callbackId(t.inputStyles.onBlur))):t&&"richtext"==t.tag&&t.richTextStyles&&t.richTextStyles.onClick&&"function"==typeof t.richTextStyles.onClick&&(t.richTextStyles.__onClickCallBackId__=n.callbackId(t.richTextStyles.onClick))}function u(e,r,c,a,u){this.__id__=e,this.id=e,this.__uuid__=i.UUID("NativeView"),this.type="nativeView",this.IDENTITY=t,a||(s(c),n.exec(t,"View",[this.__id__,this.__uuid__,r,c,u]),o[this.__uuid__]=this)}function l(e,t){var n=null;for(var i in c){var o=c[i];if(t){if(o.uid&&o.uid==e||o.uuid&&o.uuid==e){n=o;break}}else if(o.id==e){n=o;break}}return n}function _(t){var n=o[t.uuid];return n||((n=new e.nativeObj.View(t.id,t.styles,"",!0)).__uuid__=t.uuid,o[n.__uuid__]=n),n}function u(e,r,c,a,s,u){this.__id__=e||i.UUID("adView"),this.id=this.__id__,this.__uuid__=i.UUID(this.id),this.type="UniAdView",this.IDENTITY=t;var l={};l.top=r,l.left=c,l.width=a,l.height=s,l.position=u,n.exec(t,"createAdView",[this.__id__,this.__uuid__,l,this.type]),o[this.__uuid__]=this}function f(e,i,o){var r="function"!=typeof i?null:function(e){i(e)},c="function"!=typeof o?null:function(e){o(e)},a=n.callbackId(r,c);n.exec(t,"getAds",[a],e)}function d(){var e=this;this.__cbs__={},this.callbackId=n.callbackId(function(t){var n=t.evt,i=t.args,o=e.__cbs__[n];if(o){var r=o.length,c=0;for(c=0;c<r;c++)o[c](i)}})}function h(e){this.__uuid__=i.UUID("RewardedVideoAd"),this.__cbs__=new d,n.exec(t,"RewardedVideoAd",[this.__uuid__,e,this.__cbs__.callbackId])}a.prototype.clear=function(){n.exec(t,"clear",[this.__id__]),this.__id__=void 0,r[this.__id__]&&delete r[this.__id__]},a.prototype.recycle=function(){n.exec(t,"bitmapRecycle",[this.__id__])},a.prototype.load=function(e,i,o){var r=function(e){"function"==typeof o&&o(e)};if(this.__id__){var c=n.callbackId(function(){"function"==typeof i&&i()},r);n.exec(t,"load",[this.__id__,e,c])}else r({code:-1,message:"已经销毁的对象"})},a.prototype.loadBase64Data=function(e,i,o){var r=function(e){"function"==typeof o&&o(e)};if(this.__id__){var c=n.callbackId(function(){"function"==typeof i&&i()},r);n.exec(t,"loadBase64Data",[this.__id__,e,c])}else r({code:-1,message:"已经销毁的对象"})},a.prototype.save=function(e,i,o,r){var c=function(e){"function"==typeof r&&r(e)};if(this.__id__){var a=n.callbackId(function(e){if("function"==typeof o){var t={target:e.path,width:e.w,height:e.h,size:e.size};o(t)}},c);n.exec(t,"save",[this.__id__,e,i,a])}else c({code:-1,message:"已经销毁的对象"})},a.prototype.__captureWebview=function(e,i,o,r){var c=function(e){"function"==typeof r&&r(e)};if(this.__id__){var a=n.callbackId(function(){"function"==typeof o&&o()},c);n.exec(t,"captureWebview",[this.__id__,e,a,i])}else c({code:-1,message:"已经销毁的对象"})},a.prototype.toBase64Data=function(){return this.__id__?n.execSync(t,"toBase64Data",[this.__id__]):null},a.getItems=function(){for(var e=[],i=n.execSync(t,"getItems",[]),o=0;o<i.length;o++){var c=i[o].__id__,s=r[c];if(!s){var u=new a(i[o].id,null,!0);u.__id__=c,r[c]=u,s=u}e.push(s)}return e},a.getBitmapById=function(e){var i=n.execSync(t,"getBitmapById",[e]);if(i){var o=r[i.__id__];if(!o){var c=new a(i.id,null,!0);c.__id__=i.__id__,r[i.__id__]=c,o=c}return o}return null},u.prototype.setImages=function(e){n.exec(t,"setImages",[this.__id__,this.__uuid__,e])},u.prototype.currentImageIndex=function(){return n.execSync(t,"currentImageIndex",[this.__id__,this.__uuid__])},u.prototype.addImages=function(e){n.exec(t,"addImages",[this.__id__,this.__uuid__,e])},u.prototype.drawBitmap=function(e,i,o,r){n.exec(t,"drawBitmap",[this.__id__,this.__uuid__,e,i,o,r])},u.prototype.drawText=function(e,i,o,r){n.exec(t,"drawText",[this.__id__,this.__uuid__,e,i,o,r])},u.prototype.drawRichText=function(e,i,o,r){o&&o.onClick&&"function"==typeof o.onClick&&(o.__onClickCallBackId__=n.callbackId(o.onClick)),n.exec(t,"drawRichText",[this.__id__,this.__uuid__,e,i,o,r])},u.prototype.drawInput=function(e,i,o){i.onComplete&&"function"==typeof i.onComplete&&(i.__onCompleteCallBackId__=n.callbackId(i.onComplete)),i.onFocus&&"function"==typeof i.onFocus&&(i.__onFocusCallBackId__=n.callbackId(i.onFocus)),i.onBlur&&"function"==typeof i.onBlur&&(i.__onBlurCallBackId__=n.callbackId(i.onBlur)),n.exec(t,"drawInput",[this.__id__,this.__uuid__,e,i,o])},u.prototype.getInputValueById=function(e){return n.execSync(t,"getInputValueById",[this.__id__,this.__uuid__,e])},u.prototype.getInputFocusById=function(e){return n.execSync(t,"getInputFocusById",[this.__id__,this.__uuid__,e])},u.prototype.setInputFocusById=function(e,i){n.exec(t,"setInputFocusById",[this.__id__,this.__uuid__,e,i])},u.prototype.show=function(){n.exec(t,"show",[this.__id__,this.__uuid__])},u.prototype.setStyle=function(e){n.exec(t,"setStyle",[this.__id__,this.__uuid__,e])},u.prototype.hide=function(){n.exec(t,"hide",[this.__id__,this.__uuid__])},u.prototype.clear=function(){this.close()},u.prototype.close=function(){n.exec(t,"view_close",[this.__id__,this.__uuid__]),delete o[this.__uuid__]},u.prototype.animate=function(e,i){var o;i&&(o=n.callbackId(function(){"function"==typeof i&&i()})),n.exec(t,"view_animate",[this.__id__,this.__uuid__,e,o])},u.prototype.reset=function(){n.exec(t,"view_reset",[this.__id__,this.__uuid__])},u.prototype.restore=function(){n.exec(t,"view_restore",[this.__id__,this.__uuid__])},u.prototype.isVisible=function(){return n.execSync(t,"isVisible",[this.__id__,this.__uuid__])},u.prototype.drawRect=function(e,i,o){n.exec(t,"view_drawRect",[this.__id__,this.__uuid__,e,i,o])},u.prototype.interceptTouchEvent=function(e){n.exec(t,"interceptTouchEvent",[this.__id__,this.__uuid__,e])},u.prototype.addEventListener=function(e,i){var o,r=this;if(i){var c=function(e){"function"==typeof i&&(e.target=r,i(e))};i.callback=c,o=n.callbackId(c)}n.exec(t,"addEventListener",[this.__id__,this.__uuid__,e,o])},u.prototype.setTouchEventRect=function(e){n.exec(t,"setTouchEventRect",[this.__id__,this.__uuid__,e])},u.getViewById=function(e){var i=n.execSync(t,"getViewById",[e]);return i||(i=l(e,!1)),i?_(i):null},u.__getViewByUidAndCreate=function(e){return _(e)},u.__getViewByUidFromCache=function(e){var t=l(e,!0);return t?_(t):null},u.startAnimation=function(e,i,o,r){var c,a,s;e&&i&&(a=i instanceof u?{viewId:i.__uuid__}:{uuid:i.bitmap?i.bitmap.__id__:null,texts:{value:i.text,textStyles:i.textStyles,textRect:i.textRect}},o&&(s=o instanceof u?{viewId:o.__uuid__}:{uuid:o.bitmap?o.bitmap.__id__:null,texts:{value:o.text,textStyles:o.textStyles,textRect:o.textRect}}),r&&(c=n.callbackId(function(){"function"==typeof r&&r()})),n.exec(t,"startAnimation",[e,a,s,c]))},u.clearAnimation=function(e){e||(e="none"),n.exec(t,"clearAnimation",[e])},u.prototype.clearRect=function(e,i){n.exec(t,"view_clearRect",[this.__id__,this.__uuid__,e,i])},u.prototype.draw=function(e){s(e),n.exec(t,"view_draw",[this.__id__,this.__uuid__,e])},u.prototype.bind=function(e){n.exec(t,"bind",[this.__id__,this.__uuid__,e])},u.prototype.renderingBind=function(e){n.exec(t,"renderingBind",[this.__id__,this.__uuid__,e])},u.prototype.setDownloadListener=function(e){var i="function"!=typeof e?null:function(t){e(t)},o=n.callbackId(i);n.exec(t,"setDownloadListener",[this.__id__,this.__uuid__,o])},u.prototype.changeDownloadStatus=function(){n.exec(t,"changeDownloadStatus",[this.__id__,this.__uuid__])},u.prototype.setRenderingListener=function(e){var i="function"!=typeof e?null:function(t){e(t)},o=n.callbackId(i);n.exec(t,"setRenderingListener",[this.__id__,this.__uuid__,o])},u.prototype.setAdClickedListener=function(e){var i="function"!=typeof e?null:function(t){e(t)},o=n.callbackId(i);n.exec(t,"setAdClickedListener",[this.__id__,this.__uuid__,o])},u.prototype.setDislikeListener=function(e){var i="function"!=typeof e?null:function(t){e(t)},o=n.callbackId(i);n.exec(t,"setDislikeListener",[this.__id__,this.__uuid__,o])},d.prototype.push=function(e,t){this.__cbs__[e]||(this.__cbs__[e]=[]),this.__cbs__[e].push(t)},d.prototype.pop=function(e,t){this.__cbs__[e]&&this.__cbs__[e].pop(t)},h.prototype.load=function(){n.exec(t,"load",[this.__uuid__])},h.prototype.show=function(){n.exec(t,"show",[this.__uuid__])},h.prototype.getProvider=function(){return n.execSync(t,"getProvider",[this.__uuid__])},h.prototype.destroy=function(){n.exec(t,"destroy",[this.__uuid__])},h.prototype.onLoad=function(e){this.__cbs__.push("load",e)},h.prototype.offLoad=function(e){this.__cbs__.pop("load",e)},h.prototype.onError=function(e){this.__cbs__.push("error",e)},h.prototype.offError=function(e){this.__cbs__.pop("error",e)},h.prototype.onClose=function(e){this.__cbs__.push("close",e)},h.prototype.offClose=function(e){this.__cbs__.pop("close",e)},h.prototype.onVerify=function(e){this.__cbs__.push("verify",e)},h.prototype.offVerify=function(e){this.__cbs__.pop("verify",e)},h.prototype.onAdClicked=function(e){this.__cbs__.push("adClicked",e)},h.prototype.offAdClicked=function(e){this.__cbs__.pop("adClicked",e)};var p={Bitmap:a,View:u,createAdView:function(t){return new e.ad.View(t.id,t.top,t.left,t.width,t.height,t.position)},releaseAdData:function(e){n.exec(t,"releaseAdData",[e])},measureAdHeight:function(e,i){return n.execSync(t,"measureAdHeight",[e,i])},getAds:f,getDrawAds:function(e,t,n){e._t_="draw_flow",f(e,t,n)},getProviders:function(e,i){var o="function"!=typeof e?null:function(t){e(t)},r="function"!=typeof i?null:function(e){i(e)},c=n.callbackId(o,r);n.exec(t,"getProviders",[c],null)},createRewardedVideoAd:function(e){return new h(e=e||{})},createFullScreenVideoAd:function(e){return(e=e||{}).__type="fullScreenVideo",new h(e)},createInterstitialAd:function(e){return(e=e||{}).__type="InterstitialAd",new h(e)},setSplashAd:function(e,i,o){var r=n.callbackId(i,o);n.exec(t,"setSplashAd",[e,r])},getSplashAd:function(){return n.execSync(t,"getSplashAd",null)},setPersonalizedAd:function(e){n.exec(t,"setPersonalizedAd",[e])},getPersonalizedAd:function(){return n.execSync(t,"getPersonalizedAd",null)},setPrivacyConfig:function(e){n.execSync(t,"setPrivacyConfig",[e])},showContentPage:function(e,i,o){var r=n.callbackId(i,o);n.exec(t,"showContentPage",[e,r])},__createNView:function(t,n,i,r){var c=new e.nativeObj.View(n,i,r);return c.__uuid__=t,o[c.__uuid__]=c,c},__appendSubViewInfo:function(e){c[e.uuid]=e},__removeSubViewInfos:function(e){for(var t=0;t<e.length;t++)delete c[e[t]]}};e.ad=p}(plus);